# DutyRosterController API 文档

## 概述

DutyRosterController 是值班表管理的控制器，提供对 DutyRoster（值班表）和 DutyRosterSchedule（排班时间）两个实体的完整 CRUD 操作。

## 技术特点

- 使用 MyBatis-Plus 的 LambdaQueryWrapper 进行类型安全的查询
- 支持灵活的条件查询和分页
- 统一的异常处理和返回格式
- 完整的日志记录和 API 文档

## 基础路径

```
/dutyroster
```

## DutyRoster（值班表）相关接口

### 1. 分页查询值班表

**接口地址：** `GET /dutyroster/list`

**参数：**
- `pageNo`: 页码（默认：1）
- `pageSize`: 每页大小（默认：10）
- `title`: 值班表标题（模糊查询，可选）
- `orgId`: 组织ID（精确查询，可选）
- `orgName`: 组织名称（模糊查询，可选）

**返回示例：**
```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": {
    "records": [...],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 2. 添加值班表

**接口地址：** `POST /dutyroster/add`

**请求体：**
```json
{
  "title": "2025年春节值班表",
  "orgId": "org-001",
  "orgName": "技术部",
  "sort": 1
}
```

### 3. 编辑值班表

**接口地址：** `POST /dutyroster/edit`

**请求体：**
```json
{
  "id": "roster-id-001",
  "title": "2025年春节值班表（修改）",
  "orgId": "org-001",
  "orgName": "技术部",
  "sort": 1
}
```

### 4. 删除值班表

**接口地址：** `GET /dutyroster/delete`

**参数：**
- `id`: 值班表ID（必填）

### 5. 批量删除值班表

**接口地址：** `GET /dutyroster/deleteBatch`

**参数：**
- `ids`: 值班表ID列表，用逗号分隔（必填）

### 6. 根据ID查询值班表

**接口地址：** `GET /dutyroster/queryById`

**参数：**
- `id`: 值班表ID（必填）

## DutyRosterSchedule（排班时间）相关接口

### 1. 分页查询排班时间

**接口地址：** `GET /dutyroster/schedule/list`

**参数：**
- `pageNo`: 页码（默认：1）
- `pageSize`: 每页大小（默认：10）
- `rosterId`: 值班表ID（可选）
- `startTime`: 开始时间（可选）
- `endTime`: 结束时间（可选）

### 2. 添加排班时间

**接口地址：** `POST /dutyroster/schedule/add`

**请求体：**
```json
{
  "rosterId": "roster-id-001",
  "startTime": "2025-01-20",
  "endTime": "2025-01-26"
}
```

### 3. 编辑排班时间

**接口地址：** `POST /dutyroster/schedule/edit`

**请求体：**
```json
{
  "id": "schedule-id-001",
  "rosterId": "roster-id-001",
  "startTime": "2025-01-20",
  "endTime": "2025-01-27"
}
```

### 4. 删除排班时间

**接口地址：** `GET /dutyroster/schedule/delete`

**参数：**
- `id`: 排班时间ID（必填）

### 5. 批量删除排班时间

**接口地址：** `GET /dutyroster/schedule/deleteBatch`

**参数：**
- `ids`: 排班时间ID列表，用逗号分隔（必填）

### 6. 根据ID查询排班时间

**接口地址：** `GET /dutyroster/schedule/queryById`

**参数：**
- `id`: 排班时间ID（必填）

### 7. 根据值班表ID查询排班时间列表

**接口地址：** `GET /dutyroster/schedule/queryByRosterId`

**参数：**
- `rosterId`: 值班表ID（必填）

**返回示例：**
```json
{
  "success": true,
  "message": "操作成功！",
  "code": 200,
  "result": [
    {
      "id": "schedule-id-001",
      "rosterId": "roster-id-001",
      "startTime": "2025-01-20",
      "endTime": "2025-01-26"
    },
    {
      "id": "schedule-id-002",
      "rosterId": "roster-id-001",
      "startTime": "2025-01-27",
      "endTime": "2025-02-02"
    }
  ]
}
```

## 实体字段说明

### DutyRoster（值班表）

| 字段名 | 类型 | 说明 | 是否必填 |
|--------|------|------|----------|
| id | String | 主键ID | 否（自动生成） |
| title | String | 值班表标题 | 是 |
| orgId | String | 所属组织ID | 是 |
| orgName | String | 所属组织名称 | 是 |
| createBy | String | 创建人ID | 否（自动填充） |
| createTime | LocalDateTime | 创建时间 | 否（自动填充） |
| updateBy | String | 最后更新人ID | 否（自动填充） |
| updateTime | LocalDateTime | 最后更新时间 | 否（自动填充） |
| delFlag | Integer | 软删除标记 | 否（自动管理） |
| sort | Integer | 排序号 | 否 |

### DutyRosterSchedule（排班时间）

| 字段名 | 类型 | 说明 | 是否必填 |
|--------|------|------|----------|
| id | String | 主键ID | 否（自动生成） |
| rosterId | String | 关联的值班表ID | 是 |
| startTime | LocalDate | 开始日期 | 是 |
| endTime | LocalDate | 结束日期 | 是 |

## 注意事项

1. 所有接口都使用统一的 Result 返回格式
2. 分页查询支持多种条件组合查询
3. 删除操作使用软删除机制
4. 时间字段会自动填充创建时间和更新时间
5. 排班时间按开始时间升序排列
6. 值班表按创建时间倒序排列

## 错误处理

所有接口都包含异常处理，当发生错误时会返回：

```json
{
  "success": false,
  "message": "具体错误信息",
  "code": 500
}
```
