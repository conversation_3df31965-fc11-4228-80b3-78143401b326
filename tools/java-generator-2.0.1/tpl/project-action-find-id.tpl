package cn.microvideo.demo.module.gantry.action;

import cn.microvideo.bds.sys.spring.MicrovideoSpringBeanUtil;
import cn.microvideo.framework.core.action.MicrovideoAction;
import cn.microvideo.framework.core.code.MicrovideoCode;
import cn.microvideo.framework.core.constants.MicrovideoConstants;
import cn.microvideo.framework.core.context.MicrovideoContext;
import cn.microvideo.framework.core.exception.MicrovideoBizException;
import cn.microvideo.framework.string.util.MicrovideoStringUtil;
import org.springframework.stereotype.Service;

/**
 * @description 根据id查询
 * @com 感动科技
 * <AUTHOR>
 * @date 2021/03/12 14:55:41
 **/
@Service
public class ${table.objectName}FindIdAction extends MicrovideoAction<Object, Object> {
    @Override
    public MicrovideoContext<Object, Object> transfer(MicrovideoContext<Object, Object> context) {
        context.setTransitionVal(MicrovideoConstants.YES);
        if(MicrovideoStringUtil.isBlank(context.getId())){
            throw new MicrovideoBizException(MicrovideoCode.PARAM_ID_NULL);
        }
        context.setObj(MicrovideoSpringBeanUtil.get${table.objectName}LocalService().findById(context.getId()));
        return context;
    }
}
