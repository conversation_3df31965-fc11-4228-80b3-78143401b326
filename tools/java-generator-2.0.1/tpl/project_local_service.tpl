package cn.microvideo.framework;

import ${entityPackage}.${table.objectName};
import ${requestObjectPackage}.${table.objectName}QueryParam;
import ${requestObjectPackage}.${table.objectName}UpdateParam;
import cn.microvideo.bds.sys.spring.MicrovideoSpringBeanUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description 请填写注释
 * @com 感动科技
 * <AUTHOR>
 * @date 2021/03/11 10:12:22
 **/
@Service
public class ${table.objectName}LocalService {
    /* *
     * <AUTHOR>
     * @description 添加
     * @date 2021/03/11 10:12:38
     * @param param
     * @return void
     **/
    public void add(${table.objectName} param) {
        MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().add(param, MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }

    /* *
    * <AUTHOR>
    * @description 根据主键修改
    * @date 2021/03/11 10:12:51
    * @param param
    * @return void
    **/
    public void update(${table.objectName} param) {
        MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().updateById(param, MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }

    /* *
     * <AUTHOR>
     * @description 删除
     * @date 2021/03/11 10:13:30
     * @param id 主键
     * @return void
     **/
    public void deleteById(String id) {
        MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().deleteById(id, MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }

    /* *
     * <AUTHOR>
     * @description 批量删除
     * @date 2021/03/11 10:13:38
     * @param
     * @param ids 主键多个逗号分隔
     * @return void
     **/
    public void batchDel(String ids) {
        MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().batchDelete(id, MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }

    /* *
     * <AUTHOR>
     * @description 根据主键查询
     * @date 2021/03/11 10:13:45
     * @param param
     * @return ${table.objectName}
     **/
    public ${table.objectName} findById(String param) {
        return MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().findById(param, MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }

    /* *
     * <AUTHOR>
     * @description 分页列表
     * @date 2021/03/11 10:13:59
     * @param param 查询条件
     * @return java.util.List<${table.objectName}>
     **/
    public List<${table.objectName}> query(${table.objectName}QueryParam param) {
        return MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().query(param, param.getPage(), MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }

    /* *
     * <AUTHOR>
     * @description 按条件查询
     * @date 2021/03/11 10:14:11
     * @param param 查询条件
     * @return ${table.objectName}
     **/
    public ${table.objectName} queryByCond(${table.objectName}QueryParam param) {
        ${table.objectName} result = null;
        List<${table.objectName}> resultList = MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().query(param, MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
        if (resultList != null && resultList.size() > 0) {
            result = resultList.get(0);
        }
        return result;
    }

    /* *
     * <AUTHOR>
     * @description 按条件查询
     * @date 2021/03/11 10:14:11
     * @param param 查询条件
     * @return List<${table.objectName}>
     **/
    public List<${table.objectName}> queryList(${table.objectName}QueryParam param) {
        return MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().query(param, MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }

    /* *
     * <AUTHOR>
     * @description 根据主键修改
     * @date 2021/03/11 10:14:30
     * @param obj 需要修改的数据
     * @return void
     **/
    public void updateByIdSelective(${table.objectName} obj) {
        ${table.objectName}UpdateParam cond = new ${table.objectName}UpdateParam();
        cond.setId(obj.getId());
        //cond 修改条件 ; obj 需要修改的数据
        MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().updateSelective(obj, cond, MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }

    /* *
     * <AUTHOR>
     * @description 清空表
     * @date 2021/03/11 10:14:30
     * @return void
     **/
    public void clearTable() {
        MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().clearTable(MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }
    /* *
     * <AUTHOR>
     * @description 更新所有数据逻辑删除标记为删除状态
     * @date 2021/03/11 10:14:30
     * @return void
     **/
    public void updateAllDelflagToInvalid() {
        MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().updateAllDelflagToInvalid(MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }
    /* *
     * <AUTHOR>
     * @description 更新逻辑删除标记是正常的数据为删除状态
     * @date 2021/03/11 10:14:30
     * @return void
     **/
    public void updateDelflagValidToInvalid() {
        MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().updateDelflagValidToInvalid(MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }
    /* *
     * <AUTHOR>
     * @description 根据条件删除
     * @date 2021/03/11 10:14:30
     * @param cond 删除条件
     * @return void
     **/
    public void deleteByCond(${table.objectName}UpdateParam cond) {
        MicrovideoSpringBeanUtil.get${table.objectName}JarServiceImpl().deleteByExample(cond,MicrovideoSpringBeanUtil.get${table.objectName}LocalAop());
    }
}