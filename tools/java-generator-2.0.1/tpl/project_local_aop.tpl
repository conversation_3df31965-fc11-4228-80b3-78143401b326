package cn.microvideo.framework;

import ${aopPackage}.A${table.objectName}Aop;
import ${entityPackage}.${table.objectName};
import ${entityPackage}.${table.objectName}Example;
import ${requestObjectPackage}.${table.objectName}QueryParam;
import ${requestObjectPackage}.${table.objectName}UpdateParam;
import cn.microvideo.framework.core.basic.page.MicrovideoPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Collections;

/**
 * @description 请填写注释
 * @com 感动科技
 * <AUTHOR>
 * @date 2021/03/11 10:09:24
 **/
@Service
public class ${table.objectName}LocalAop extends A${table.objectName}Aop {
    private static Logger logger = LoggerFactory.getLogger(${table.objectName}LocalAop.class);

    /* *
     * <AUTHOR>
     * @description 查询条件
     * @date 2021/03/11 10:09:07
     * @param cond
     * @return ${table.objectName}Example
     **/
    @Override
    public ${table.objectName}Example assemblyQueryCriteria(${table.objectName}QueryParam cond) {
        return new ${table.objectName}Example();
    }
    /**
     * @Description: 拼接sql语句(自定义查询)
     * @param: busType 业务类型
     * @param: cond 查询条件
     * @return: String
     */
    @Override
    public String assemblySql(String s, ${table.objectName}QueryParam ${table.objectName?uncap_first}) {
        return s;
    }
    /**
     * @Description: 组装查询条件(根据条件更新所有字段数据)
     * @param: cond 查询条件
     * @return: ${table.objectName}Example
     */
    @Override
    public ${table.objectName}Example assemblyUpdateAllCriteria(${table.objectName}UpdateParam ${table.objectName?uncap_first}UpdateParam) {
        return null;
    }
    /**
     * @Description: 组装查询条件(根据条件更新指定的字段数据(obj中属性非空的才会更新))
     * @param: cond 查询条件
     * @return: ${table.objectName}Example
     */
    @Override
    public ${table.objectName}Example assemblyUpdateSelectiveCriteria(${table.objectName}UpdateParam cond) {
        return new ${table.objectName}Example();
    }
    /**
     * @Description: 组装查询条件(根据条件删除)
     * @param: cond 查询条件
     * @return: ${table.objectName}Example
     */
    @Override
    public ${table.objectName}Example assemblyDeleteByExampleCriteria(${table.objectName}UpdateParam cond) {
       return new ${table.objectName}Example();
    }

    @Override
    public ${table.objectName} findById(String s) {
        return null;
    }

    @Override
    public void add(${table.objectName} ${table.objectName?uncap_first}) {
        logger.info(" mehod is add  execute");
    }

    @Override
    public void updateById(${table.objectName} ${table.objectName?uncap_first}) {
        logger.info(" mehod is updateById  execute");
    }

    @Override
    public void updateAll(${table.objectName} ${table.objectName?uncap_first}, ${table.objectName}UpdateParam ${table.objectName?uncap_first}UpdateParam) {
        logger.info(" mehod is updateAll  execute");
    }

    @Override
    public void updateSelective(${table.objectName} ${table.objectName?uncap_first}, ${table.objectName}UpdateParam ${table.objectName?uncap_first}UpdateParam) {
        logger.info(" mehod is updateSelective  execute");
    }

    @Override
    public List<${table.objectName}> query(${table.objectName}QueryParam ${table.objectName?uncap_first}QueryParam, MicrovideoPage microvideoPage) {
        logger.info(" mehod is query page  execute");
        return Collections.emptyList();
    }

    @Override
    public List<${table.objectName}> query(${table.objectName}QueryParam ${table.objectName?uncap_first}QueryParam) {
        logger.info(" mehod is query  execute");
        return Collections.emptyList();
    }

    @Override
    public List<${table.objectName}> queryCustom(String s, ${table.objectName}QueryParam ${table.objectName?uncap_first}QueryParam) {
        return Collections.emptyList();
    }

    @Override
    public void deleteById(String s) {
        logger.info(" mehod is deleteById  execute");
    }

    @Override
    public void batchDelete(String s) {
        logger.info(" mehod is batchDelete  execute");
    }

    @Override
    public int deleteByExample(${table.objectName}UpdateParam ${table.objectName?uncap_first}UpdateParam) {
        logger.info(" mehod is deleteByExample  execute");
        return 0;
    }

    @Override
    public int clearTable() {
        logger.info(" mehod is clearTable  execute");
        return 0;
    }

    @Override
    public int updateAllDelflagToInvalid() {
        logger.info(" mehod is updateAllDelflagToInvalid  execute");
        return 0;
    }

    @Override
    public int updateDelflagValidToInvalid() {
        logger.info(" mehod is updateDelflagValidToInvalid  execute");
        return 0;
    }
}
