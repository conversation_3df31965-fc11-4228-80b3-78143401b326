package ${servicePackage};

import java.util.Arrays;
import java.util.List;

import cn.microvideo.framework.core.basic.asserts.MicrovideoAssert;
import cn.microvideo.framework.core.basic.code.MicrovideoCode;
import cn.microvideo.framework.core.basic.constant.MicrovideoConstant;
import cn.microvideo.framework.core.basic.exception.MicrovideoBizException;
import cn.microvideo.framework.core.basic.page.MicrovideoPage;
import cn.microvideo.framework.core.basic.time.MicrovideoTime;
import cn.microvideo.framework.core.basic.uuid.MicrovideoUUID;
import cn.microvideo.framework.core.util.string.MicrovideoStringUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import ${entityPackage}.${table.objectName};
import ${entityPackage}.${table.objectName}Example;
import ${mapperPackage}.I${table.objectName}Mapper;
import ${table.javaPackage}.param.${table.objectName}QueryParam;
import ${table.javaPackage}.param.${table.objectName}UpdateParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 南京感动科技有限公司.
 * <#if table.tableDesc??>${table.tableDesc}</#if>服务
 * <AUTHOR>
 * @since ${currDate}
 */
@Slf4j
@Service
public class ${table.objectName}Service {

    @Autowired
    private I${table.objectName}Mapper mapper;

    /**
     *根据id查询.
     * @param  id 不能为空
     * @return ${table.objectName}
     **/
    public ${table.objectName} findById(final String id) {
        ${table.objectName} result = null;
        try {
            if (MicrovideoStringUtil.isBlank(id)) {
                log.error("类:[{}] 方法:[findById] 参数:[id] 异常信息:[id为空]", ${table.objectName}.class);
                return null;
            }
            result = mapper.selectByPrimaryKey(id);
        } catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return result;
    }

    /**
     * 添加.
     * @param obj  业务数据  不能为空
     * @return int 1为成功
     */
    public int add(final ${table.objectName} obj) {
        try {
            MicrovideoAssert.IsObjNull(obj, MicrovideoCode.OBJ_IS_NULL);
            String id = obj.getId();
            if (MicrovideoStringUtil.isBlank(id)) {
                id = MicrovideoUUID.get();
                obj.setId(id);
            }
            if (obj.getDataCreateTime() == null) {
                obj.setDataCreateTime(MicrovideoTime.getCurrDate());
            }
            if (obj.getDataUpdateTime() == null) {
                obj.setDataUpdateTime(MicrovideoTime.getCurrDate());
            }
            obj.setDataDelFlag(MicrovideoConstant.LOGICAL_DELETE_NOMAL);
            return mapper.insert(obj);
        } catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return 1;
    }

    /**
     * 根据主键更新数据.
     * @param obj 更新的数据必须要有主键ID  不能为空
     * @return int (0:修改失败数据库中数据不存在,1:修改成功 )
     */
    public int updateById(final ${table.objectName} obj) {
        try {
            if (obj == null) {
                log.error("类:[{}] 方法:[updateById] 参数:[obj] 异常信息:[obj为空]", ${table.objectName}Service.class);
                return 0;
            }
            if (MicrovideoStringUtil.isBlank(obj.getId())) {
                log.error("类:[{}] 方法:[updateById] 参数:[obj] 异常信息:[obj id 为空]", ${table.objectName}Service.class);
                return 0;
            }
            ${table.objectName} srcObj = this.findById(obj.getId());
            if (srcObj == null) {
                log.error("修改失败 数据库中数据不存在");
                return 0;
            }
            obj.setDataCreateTime(srcObj.getDataCreateTime());
            return mapper.updateByPrimaryKey(obj);
        } catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return 1;
    }

    /**
     * 根据条件更新所有字段数据.
     * @param obj 需要更新的业务数据  不能为空
     * @param cond 更新条件  不能为空
     * @return int (0:修改失败数据库中数据不存在,大于0:修改成功 )
     */
    public int updateAll(final ${table.objectName} obj, final ${table.objectName}UpdateParam cond) {
        try {
            ${table.objectName}Example example = new ${table.objectName}Example();
            ${table.objectName}Example.Criteria criteria = example.createCriteria();
            if (cond != null && MicrovideoStringUtil.isNotBlank(cond.getId())) {
                criteria.andIdEqualTo(cond.getId());
            }
            return mapper.updateByExample(obj, example);
        } catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return 1;
    }

    /**
     * 根据条件更新指定的字段数据(obj中属性非空的才会更新).
     * @param obj 需要更新的业务数据  不能为空
     * @param cond 更新条件  不能为空
     * @return int (0:修改失败数据库中数据不存在,大于0:修改成功 )
     */
    public int updateSelective(final ${table.objectName} obj, final ${table.objectName}UpdateParam cond) {
        try {
            ${table.objectName}Example example = new ${table.objectName}Example();
            ${table.objectName}Example.Criteria criteria = example.createCriteria();
            if (cond != null && MicrovideoStringUtil.isNotBlank(cond.getId())) {
                criteria.andIdEqualTo(cond.getId());
            }
            return mapper.updateByExampleSelective(obj, example);
        } catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return 1;
    }

    /**
     * 分页查询.
     * @param cond 查询条件  不能为空
     * @param page 分页条件  不能为空
     * @return List ${table.objectName}
     */
    public List<${table.objectName}> query(final ${table.objectName}QueryParam cond, MicrovideoPage page) {
        List<${table.objectName}> returnList = null;
        try {
            ${table.objectName}Example example = new ${table.objectName}Example();
            ${table.objectName}Example.Criteria criteria = example.createCriteria();
            if (cond != null && MicrovideoStringUtil.isNotBlank(cond.getId())) {
                criteria.andIdEqualTo(cond.getId());
            }
            if (page != null) {
                page.setTotalNum(mapper.countByExample(example));
            } else {
                page = new MicrovideoPage(mapper.countByExample(example), 1, 10);
            }
            example.setPage(page);
            returnList = mapper.selectByExample(example);
        } catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return returnList;
    }

    /**
     * 查询.
     * @param cond 查询条件  不能为空
     * @return List ${table.objectName}
     */
    public List<${table.objectName}> query(final ${table.objectName}QueryParam cond) {
        List<${table.objectName}> returnList = null;
        try {
            ${table.objectName}Example example = new ${table.objectName}Example();
            ${table.objectName}Example.Criteria criteria = example.createCriteria();
            if (cond != null && MicrovideoStringUtil.isNotBlank(cond.getId())) {
                criteria.andIdEqualTo(cond.getId());
            }
            example.setPage(null);
            returnList = mapper.selectByExample(example);
        } catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return returnList;
    }


    /**
     * 根据主键删除.
     * @param  id   主键id  不能为空
     * @return int (0:删除失败数据库中数据不存在,1:删除成功 )
     */
    public int deleteById(final String id) {
        try {
            ${table.objectName} srcObj = this.findById(id);
            if (srcObj == null) {
                log.error("删除异常 数据库中数据不存在");
                return 0;
            }
            return mapper.deleteByPrimaryKey(id);
        } catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return 1;
    }

    /**
     * 根据多个主键id批量删除.
     * @param objId    主键id逗号分隔  不能为空
     * @return int (0:删除失败数据库中数据不存在,大于0:删除成功 )
     */
    public int batchDelete(final String objId) {
        try {
            if (MicrovideoStringUtil.isBlank(objId)) {
                log.error("类:[{}] 方法:[batchDelete] 参数:[objId] 异常信息:[objId为空]", ${table.objectName}Service.class);
                return 0;
            }
            String[] objIds = objId.split(",");
            if (objIds.length == 0) {
                log.error("类:[{}] 方法:[batchDelete] 参数:[objId] 异常信息:[objIds length is 0]", ${table.objectName}Service.class);
                return 0;
            }
            ${table.objectName}Example example = new ${table.objectName}Example();
            example.createCriteria().andIdIn(Arrays.asList(objIds));
            return mapper.deleteByExample(example);
        } catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return 1;
    }

    /**
     * 根据条件删除.
     * @param cond     查询条件
     * @return int (0:删除失败数据库中数据不存在,大于0:删除成功 )
     */
    public int deleteByExample(final ${table.objectName}UpdateParam cond) {
        try {
            ${table.objectName}Example example = new ${table.objectName}Example();
            ${table.objectName}Example.Criteria criteria = example.createCriteria();
            if (cond != null&&MicrovideoStringUtil.isNotBlank(cond.getId())) {
                criteria.andIdEqualTo(cond.getId());
            }
            return mapper.deleteByExample(example);
        }catch (final MicrovideoBizException e) {
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
        return 1;
    }

    /**
     * 清空表(请谨慎操作).
     * @return int (0:删除失败数据库中数据不存在,大于0:删除成功 )
     */
    public int clearTable() {
        return mapper.clearTable();
    }

    /**
     * 更新所有数据逻辑删除标记为删除状态.
     * @return int (0:修改失败数据库中数据不存在,大于0:修改成功)
     */
    public int updateAllDelflagToInvalid() {
        ${table.objectName}UpdateParam obj = new ${table.objectName}UpdateParam();
        obj.setDataDelFlag(1);
        ${table.objectName}Example example = new ${table.objectName}Example();
        return mapper.updateByExampleSelective(obj, example);
    }

    /**
     * 更新逻辑删除标记是正常的数据为删除状态.
     * @return int (0:修改失败数据库中数据不存在,大于0:修改成功 )
     */
    public int updateDelflagValidToInvalid() {
        ${table.objectName}UpdateParam obj = new ${table.objectName}UpdateParam();
        obj.setDataDelFlag(1);
        ${table.objectName}Example example = new ${table.objectName}Example();
        example.createCriteria().andDataDelFlagEqualTo(0);
        return mapper.updateByExampleSelective(obj, example);
    }

}
