package cn.microvideo.demo.module.gantry.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import cn.microvideo.bds.sys.contant.MicrovideoProcessConstant;
import cn.microvideo.bds.sys.handler.MicrovideoAbstractHandlers;
import cn.microvideo.framework.core.context.MicrovideoContext;
import cn.microvideo.framework.core.http.rsp.MicroviceoHttpRsp;
import io.swagger.annotations.*;

@Api(tags = "接口")
@RestController
@RequestMapping("/${table.objectName?lower_case}")
public class ${table.objectName}BasicController extends MicrovideoAbstractHandlers {
    private static Logger logger = LoggerFactory.getLogger(${table.objectName}BasicController.class);
    @Override
    public Logger getLog() {
        return logger;
    }
    //分页查询
    public static final String PROCESS_${table.objectName?upper_case}_PAGE_LIST="${table.objectName?uncap_first}PageListProcess";
    //查询
    public static final String PROCESS_${table.objectName?upper_case}_LIST="${table.objectName?uncap_first}ListProcess";
    //添加
    public static final String PROCESS_${table.objectName?upper_case}_ADD="${table.objectName?uncap_first}AddProcess";
    //根据id更新
    public static final String PROCESS_${table.objectName?upper_case}_UPDATE_ID="${table.objectName?uncap_first}UpdateIdProcess";
    //根据id查询
    public static final String PROCESS_${table.objectName?upper_case}_FIND_ID="${table.objectName?uncap_first}FindIdProcess";
    //根据id删除
    public static final String PROCESS_${table.objectName?upper_case}_DELETE_ID="${table.objectName?uncap_first}DelIdProcess";
    //根据ids删除
    public static final String PROCESS_${table.objectName?upper_case}_DELETE_IDS="${table.objectName?uncap_first}DelIdsProcess";


    @ApiOperation(value = "列表查询", notes = "列表查询")
    @ApiResponses({@ApiResponse(code = 200, message = "请求正确")})
    @RequestMapping(value="/list" , method={RequestMethod.POST,RequestMethod.GET})
    public MicroviceoHttpRsp list(@RequestBody MicrovideoContext<Object, Object> context) {
        initContext(context);
        context.getHeader().setProcessId(MicrovideoProcessConstant.PROCESS_${table.objectName?upper_case}_LIST);
        return process(context);
    }
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @ApiResponses({@ApiResponse(code = 200, message = "请求正确")})
    @RequestMapping(value="/page/list" , method={RequestMethod.POST,RequestMethod.GET})
    public MicroviceoHttpRsp pagelist(@RequestBody MicrovideoContext<Object, Object> context) {
        initContext(context);
        context.getHeader().setProcessId(MicrovideoProcessConstant.PROCESS_${table.objectName?upper_case}_PAGE_LIST);
        return process(context);
    }
    @ApiOperation(value = "根据id查询", notes = "根据id查询")
    @ApiResponses({@ApiResponse(code = 200, message = "请求正确")})
    @RequestMapping(value="/get/id" , method={RequestMethod.POST,RequestMethod.GET})
    public MicroviceoHttpRsp findId(@RequestBody MicrovideoContext<Object, Object> context) {
        initContext(context);
        context.getHeader().setProcessId(MicrovideoProcessConstant.PROCESS_${table.objectName?upper_case}_FIND_ID);
        return process(context);
    }
    @ApiOperation(value = "添加", notes = "添加")
    @ApiResponses({@ApiResponse(code = 200, message = "请求正确")})
    @RequestMapping(value="/add" , method={RequestMethod.POST,RequestMethod.GET})
    public MicroviceoHttpRsp add(@RequestBody MicrovideoContext<Object, Object> context) {
        initContext(context);
        context.getHeader().setProcessId(MicrovideoProcessConstant.PROCESS_${table.objectName?upper_case}_ADD);
        return process(context);
    }
    @ApiOperation(value = "根据id更新", notes = "根据id更新")
    @ApiResponses({@ApiResponse(code = 200, message = "请求正确")})
    @RequestMapping(value="/edit/id" , method={RequestMethod.POST,RequestMethod.GET})
    public MicroviceoHttpRsp updateId(@RequestBody MicrovideoContext<Object, Object> context) {
        initContext(context);
        context.getHeader().setProcessId(MicrovideoProcessConstant.PROCESS_${table.objectName?upper_case}_UPDATE_ID);
        return process(context);
    }
    @ApiOperation(value = "根据id删除", notes = "根据id删除")
    @ApiResponses({@ApiResponse(code = 200, message = "请求正确")})
    @RequestMapping(value="/del/id" , method={RequestMethod.POST,RequestMethod.GET})
    public MicroviceoHttpRsp delId(@RequestBody MicrovideoContext<Object, Object> context) {
        initContext(context);
        context.getHeader().setProcessId(MicrovideoProcessConstant.PROCESS_${table.objectName?upper_case}_DELETE_ID);
        return process(context);
    }
    @ApiOperation(value = "根据ids删除", notes = "根据ids删除")
    @ApiResponses({@ApiResponse(code = 200, message = "请求正确")})
    @RequestMapping(value="/del/ids" , method={RequestMethod.POST,RequestMethod.GET})
    public MicroviceoHttpRsp delIds(@RequestBody MicrovideoContext<Object, Object> context) {
        initContext(context);
        context.getHeader().setProcessId(MicrovideoProcessConstant.PROCESS_${table.objectName?upper_case}_DELETE_IDS);
        return process(context);
    }
}