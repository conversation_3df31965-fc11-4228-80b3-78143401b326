package cn.microvideo.demo.module.gantry.action;

import cn.microvideo.bds.sys.spring.MicrovideoSpringBeanUtil;
import cn.microvideo.framework.json.util.MicrovideoJsonUtil;
import cn.microvideo.framework.string.util.MicrovideoStringUtil;
import ${entityPackage}.${table.objectName};
import ${requestObjectPackage}.${table.objectName}QueryParam;
import cn.microvideo.framework.core.action.MicrovideoAction;
import cn.microvideo.framework.core.constants.MicrovideoConstants;
import cn.microvideo.framework.core.context.MicrovideoContext;
import cn.microvideo.framework.core.basic.page.MicrovideoPage;
import cn.microvideo.framework.core.result.MicrovideoListPageResult;
import org.springframework.stereotype.Service;


/**
 * @description 分页
 * @com 感动科技
 * <AUTHOR>
 * @date 2021/03/12 14:55:41
 **/
@Service
public class ${table.objectName}PageListAction extends MicrovideoAction<Object, Object> {
    @Override
    public MicrovideoContext<Object, Object> transfer(MicrovideoContext<Object, Object> context) {
        context.setTransitionVal(MicrovideoConstants.YES);
        ${table.objectName}QueryParam param =new ${table.objectName}QueryParam();
        if(context.getReq()!=null&&context.getReq().getParam()!=null){
            param= MicrovideoJsonUtil.parseObject(MicrovideoJsonUtil.toJSONStringWithDateFormat(context.getReq().getParam(),MicrovideoJsonUtil.FULL_DATE_TIME_FORMAT),${table.objectName}QueryParam.class);
        }
        MicrovideoPage page = new MicrovideoPage();
        if(MicrovideoStringUtil.isBlank(param.getCurrentIndex())){
            param.setCurrentIndex("1");
        }
        page.setCurrentPage(Integer.parseInt(param.getCurrentIndex()));
        param.setPage(page);
        MicrovideoListPageResult<${table.objectName}> result=new MicrovideoListPageResult<>();
        result.setList(MicrovideoSpringBeanUtil.get${table.objectName}LocalService().query(param));
        result.setPage(page);
        context.setObj(result);
        return context;
    }
}
