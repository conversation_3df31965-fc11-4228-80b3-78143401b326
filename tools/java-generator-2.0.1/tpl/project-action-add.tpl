package cn.microvideo.demo.module.gantry.action;

import cn.microvideo.bds.sys.spring.MicrovideoSpringBeanUtil;
import cn.microvideo.framework.core.exception.MicrovideoBizException;
import cn.microvideo.framework.json.util.MicrovideoJsonUtil;
import org.springframework.stereotype.Service;
import cn.microvideo.framework.core.code.MicrovideoCode;
import cn.microvideo.framework.core.action.MicrovideoAction;
import cn.microvideo.framework.core.constants.MicrovideoConstants;
import cn.microvideo.framework.core.context.MicrovideoContext;

/**
 * @description 添加
 * @com 感动科技
 * <AUTHOR>
 * @date 2021/03/12 14:55:41
 **/
@Service
public class ${table.objectName}AddAction extends MicrovideoAction<Object, Object> {
    @Override
    public MicrovideoContext<Object, Object> transfer(MicrovideoContext<Object, Object> context) {
        context.setTransitionVal(MicrovideoConstants.YES);
        if(context.getForm()==null){
            throw new MicrovideoBizException(MicrovideoCode.PARAM_FROM_NULL);
        }
        ${table.objectName} data= MicrovideoJsonUtil.parseObject(MicrovideoJsonUtil.toJSONStringWithDateFormat(context.getForm(),MicrovideoJsonUtil.FULL_DATE_TIME_FORMAT),${table.objectName}.class);
        MicrovideoSpringBeanUtil.get${table.objectName}LocalService().add(data);
        return context;
    }
}
