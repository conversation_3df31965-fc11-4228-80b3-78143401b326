package ${mybatisValidationPackage};

import cn.microvideo.framework.core.basic.asserts.MicrovideoAssert;
import cn.microvideo.framework.core.basic.code.MicrovideoCode;
import cn.microvideo.framework.core.basic.exception.MicrovideoBizException;
import cn.microvideo.framework.core.basic.validation.MicrovideoMybatisValidation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import ${mapperPackage}.I${table.objectName}Mapper;

/**
 * 南京感动科技有限公司.
 * (${table.tableName}校验Mybatis Xml字段与实际表字段是否一致
 * <AUTHOR>
  * @since ${currDate}
 */
@Slf4j
@Configuration("${table.objectName}Validation")
public class ${table.objectName}Validation extends MicrovideoMybatisValidation {
    @Autowired
     private I${table.objectName}Mapper mapper;

    @Override
    public void validation() {
        try {
            mapper.checkTable();
            log.info("感动科技-校验Mybatis Xml字段与实际表(${table.tableName})字段是否一致，校验成功");
        } catch (final MicrovideoBizException e) {
            log.error("感动科技-校验Mybatis Xml字段与实际表(${table.tableName})字段是否一致，校验失败请检查表(${table.tableName})");
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        } catch (final Exception e) {
            log.error("感动科技-校验Mybatis Xml字段与实际表(${table.tableName})字段是否一致，校验失败请检查表(${table.tableName})");
            MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
        }
    }

    @Bean
    public void check${table.objectName}ValidationIns() {
        this.validation();
    }
}
