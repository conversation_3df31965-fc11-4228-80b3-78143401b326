package ${serviceImplPackage};

import java.util.List;
import org.springframework.stereotype.Service;

import ${servicePackage}.I${table.objectName}Service;
import ${entityPackage}.${table.objectName};
import ${table.javaPackage}.param.${table.objectName}QueryParam;
import ${table.javaPackage}.param.${table.objectName}UpdateParam;
import ${aopPackage}.A${table.objectName}Aop;
import cn.microvideo.framework.core.basic.page.MicrovideoPage;

/**    
 * @company 南京感动科技有限公司
 * @Title:  I${table.objectName}ServiceImpl.java   
 * @Description: 描述(接口模拟实现方法)
 * @date:   
 * @version V${version}    
 */  	
@Service
public class I${table.objectName}ServiceImpl implements I${table.objectName}Service {
	/**
	 * @Description: 根据主键查询
	 * @param:String id 主键id
	 * @param:A${table.objectName}Aop aop 代理类
	 * @return: ${table.objectName}
	 */
    @Override
	public ${table.objectName} findById(String id,A${table.objectName}Aop aop){
	    if(aop!=null){
	    	return aop.findById(id);
	    }
	    return null;
	}
	/**
	 * @Description: 添加
	 * @param:${table.objectName} obj    业务数据
	   @param:${table.objectName} aop    代理类
	 * @return: int 1:成功
	 */
    @Override
	public int add(${table.objectName} obj,A${table.objectName}Aop aop){
		if(aop!=null){
	    	aop.add(obj);
	    }
	    return 1;
	}
	/**
	 * @Description: 根据主键更新数据
	 * @param:${table.objectName} obj 更新的数据必须要有主键ID
	 * @param:A${table.objectName}Aop aop 代理类
	 * @return: int (0:修改失败数据库中数据不存在,1:修改成功 )
	 */
    @Override
	public int updateById(${table.objectName} obj,A${table.objectName}Aop aop){
		if(aop!=null){
	    	aop.updateById(obj);
	    }
	    return 1;
	}
	/**
    * @Description: 根据条件更新数据
    * @param:${table.objectName} obj 需要更新的业务数据
    * @param:${table.objectName} cond 更新条件
    * @param:A${table.objectName}Aop aop 代理类
    * @return: int (0:修改失败数据库中数据不存在,1:修改成功 )
    */
    @Override
    public int updateAll(${table.objectName} obj,${table.objectName}UpdateParam cond,A${table.objectName}Aop aop){
		if(aop!=null){
	    	aop.updateAll(obj,cond);
	    }
	    return 1;
    }
	/**
    * @Description: 根据条件更新指定的字段数据(obj中属性非空的才会更新)
    * @param:${table.objectName} obj 需要更新的业务数据
    * @param:${table.objectName} cond 更新条件
    * @param:A${table.objectName}Aop aop 代理类
    * @return: int (0:修改失败数据库中数据不存在,1:修改成功 )
    */
    @Override
    public int updateSelective(${table.objectName} obj,${table.objectName}UpdateParam cond,A${table.objectName}Aop aop){
		if(aop!=null){
	    	aop.updateSelective(obj,cond);
	    }
	    return 1;
    }
	/**
	 * @Description: 分页查询
	 * @param:${table.objectName} cond 查询条件
	 * @param:MicrovideoPage page 分页条件
	 * @param:A${table.objectName}Aop aop 代理类
	 * @return: List<${table.objectName}>
	 */
    @Override
	public List<${table.objectName}> query(${table.objectName}QueryParam cond, MicrovideoPage page,A${table.objectName}Aop aop){
       if(aop!=null){
	    	return aop.query(cond,page);
	    }
	    return null;
	}
	/**
    * @Description: 查询
    * @param:${table.objectName} cond 查询条件
    * @param:A${table.objectName}AopA${table.objectName}Aop aop 代理类
    * @return: List<${table.objectName}>
    */
    @Override
	public List<${table.objectName}> query(${table.objectName}QueryParam cond, A${table.objectName}Aop aop){
       if(aop!=null){
	    	return aop.query(cond);
	    }
	    return null;
	}
    /**
     * @Description: 自定义查询
     * @param:String  busType 业务类型
     * @param:${table.objectName} cond cond 查询条件
     * @param:A${table.objectName}Aop aop 代理类
     * @return: List<${table.objectName}>
     */
    @Override
	public List<${table.objectName}> queryCustom(String busType,${table.objectName}QueryParam cond,A${table.objectName}Aop aop){
       if(aop!=null){
	    	return aop.queryCustom(busType,cond);
	    }
	    return null;
	}
    /**
     * @Description: 根据主键删除
     * @param:String  id     主键id
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:修改失败数据库中数据不存在,1:修改成功 )
     */
    @Override
    public int deleteById(String id,A${table.objectName}Aop aop){
		if(aop!=null){
	    	aop.deleteById(id);
	    }
	    return 1;
    }
    /**
     * @Description: 根据多个主键id批量删除
     * @param:String objId    主键id逗号分隔
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:修改失败数据库中数据不存在,1:修改成功 )
     */
    @Override
    public int batchDelete(String objId,A${table.objectName}Aop aop){
		if(aop!=null){
	    	aop.batchDelete(objId);
	    }
	    return 1;
    }
     /**
      * @Description: 根据条件删除
      * @param:${table.objectName}UpdateParam cond     查询条件
      * @param:A${table.objectName}Aop aop aop 代理类
      * @return: int (0:删除失败数据库中数据不存在,大于0:删除成功 )
      */
    @Override
    public int deleteByExample(${table.objectName}UpdateParam cond,A${table.objectName}Aop aop){
		if(aop!=null){
	    	return aop.deleteByExample(cond);
	    }
	    return 0;
    }
    /**
     * @Description: 清空表(请谨慎操作)
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:删除失败数据库中数据不存在,大于0:删除成功 )
     */
    public int clearTable(A${table.objectName}Aop aop){
		if(aop!=null){
	    	return aop.clearTable();
	    }
	    return 0;
    }
    /**
     * @Description: 更新所有数据逻辑删除标记为删除状态
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功)
     */
    public int updateAllDelflagToInvalid(A${table.objectName}Aop aop){
		if(aop!=null){
	    	return aop.updateAllDelflagToInvalid();
	    }
	    return 0;
    }
    /**
     * @Description: 更新逻辑删除标记是正常的数据为删除状态
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功 )
     */
    public int updateDelflagValidToInvalid(A${table.objectName}Aop aop){
		if(aop!=null){
	    	return aop.updateDelflagValidToInvalid();
	    }
	    return 0;
    }
}
