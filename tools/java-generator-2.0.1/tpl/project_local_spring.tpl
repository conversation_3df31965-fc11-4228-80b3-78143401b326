package cn.microvideo.framework;


/**
 * spring工具
 */
@Component
public class MicrovideoSpringBeanUtil {

    /**请填写注释*/
    public static ${table.objectName}LocalService get${table.objectName}LocalService() {
        return SpringUtil.getBean(${table.objectName}LocalService.class);
    }
    /**请填写注释*/
    public static ${table.objectName}LocalAop get${table.objectName}LocalAop() {
        return (${table.objectName}LocalAop) SpringUtil.getBean(${table.objectName}LocalAop.class);
    }
    /**请填写注释*/
    public static I${table.objectName}Service get${table.objectName}JarServiceImpl() {
        return (I${table.objectName}Service) SpringUtil.getBean(I${table.objectName}ServiceImpl.class);
    }


}