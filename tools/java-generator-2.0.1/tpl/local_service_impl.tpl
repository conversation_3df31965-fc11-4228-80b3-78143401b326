package ${serviceImplPackage};

import java.util.Arrays;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import ${servicePackage}.I${table.objectName}Service;
import ${entityPackage}.${table.objectName};
import ${entityPackage}.${table.objectName}Example;
import ${mapperPackage}.I${table.objectName}Mapper;
import ${aopPackage}.A${table.objectName}Aop;
import ${table.javaPackage}.param.${table.objectName}QueryParam;
import ${table.javaPackage}.param.${table.objectName}UpdateParam;
import cn.microvideo.framework.core.exception.MicrovideoBizException;
import cn.microvideo.framework.core.asserts.MicrovideoAssert;
import cn.microvideo.framework.core.code.MicrovideoCode;
import cn.microvideo.framework.core.uuid.MicrovideoUUID;
import cn.microvideo.framework.core.constant.MicrovideoConstant;
import cn.microvideo.framework.core.util.MicrovideoTime;
import cn.microvideo.framework.core.basic.page.MicrovideoPage;

/**    
 * @company 南京感动科技有限公司
 * @Title:  I${table.objectName}ServiceImpl.java   
 * @Description: 描述(接口本地实现方法)
 * @version V${version}    
 */  	
@Service
public class I${table.objectName}ServiceImpl implements I${table.objectName}Service {
	private static Logger logger = LoggerFactory.getLogger(I${table.objectName}ServiceImpl.class);
	
	 @Autowired
	 private I${table.objectName}Mapper ${table.objectName?uncap_first}Mapper;

	/**
	 * @Description: 根据主键查询
	 * @param:String id 主键id
	 * @param:A${table.objectName}Aop aop 代理类
	 * @return: ${table.objectName}
	 */
    @Override
	public ${table.objectName} findById(String id,A${table.objectName}Aop aop){
		${table.objectName} result=null;
		try {
			if(id==null||id.trim().equals("")){
				logger.error("类:[{}] 方法:[findById] 参数:[id] 异常信息:[id为空]",I${table.objectName}ServiceImpl.class);
				return null;
			}
			result=${table.objectName?uncap_first}Mapper.selectByPrimaryKey(id);
		} catch (MicrovideoBizException e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		} catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return result;
	}
	/**
	 * @Description: 添加
	 * @param:${table.objectName} obj    业务数据
	   @param:${table.objectName} aop    代理类
	 * @return: int 1为成功
	 */
    @Override
	public int add(${table.objectName} obj,A${table.objectName}Aop aop){
		try {
			MicrovideoAssert.IsObjNull(obj, MicrovideoCode.OBJ_IS_NULL);
			String id =obj.getId();
			if(id==null||id.trim().equals("")){
                id = MicrovideoUUID.buildMd5GUID(true);
                obj.setId(id);
			}
			if(obj.getDataCreateTime()==null){
			    obj.setDataCreateTime(MicrovideoTime.getCurrDate());
			}
			if(obj.getDataUpdateTime()==null){
			    obj.setDataUpdateTime(MicrovideoTime.getCurrDate());
			}
			obj.setDataDelFlag(MicrovideoConstant.LOGICAL_DELETE_NOMAL);
			return ${table.objectName?uncap_first}Mapper.insert(obj);
		} catch (MicrovideoBizException e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		} catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
	}
	/**
	 * @Description: 根据主键更新数据
	 * @param:${table.objectName} obj 更新的数据必须要有主键ID
	 * @param:A${table.objectName}Aop aop 代理类
	 * @return: int (0:修改失败数据库中数据不存在,1:修改成功 )
	 */
    @Override
	public int updateById(${table.objectName} obj,A${table.objectName}Aop aop){
		try {
			if(obj==null){
				logger.error("类:[{}] 方法:[updateById] 参数:[obj] 异常信息:[obj为空]",I${table.objectName}ServiceImpl.class);
				return 0;
			}
			if(obj.getId()==null||obj.getId().trim().equals("")){
				logger.error("类:[{}] 方法:[updateById] 参数:[obj] 异常信息:[obj id 为空]",I${table.objectName}ServiceImpl.class);
				return 0;
			}
			${table.objectName} srcObj=this.findById(obj.getId(),aop);
			if(srcObj==null){
				logger.error("类:[{}] 方法:[updateById] 参数:[obj] 异常信息:[id:{} 修改失败，数据库中数据不存在]",I${table.objectName}ServiceImpl.class,obj.getId());
				return 0;
			}
			obj.setDataCreateTime(srcObj.getDataCreateTime());
			return ${table.objectName?uncap_first}Mapper.updateByPrimaryKey(obj);
		}catch (MicrovideoBizException e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
	}
	/**
    * @Description: 根据条件更新所有字段数据
    * @param:${table.objectName} obj 需要更新的业务数据
    * @param:${table.objectName} cond 更新条件
    * @param:A${table.objectName}Aop aop 代理类
    * @return:  int (0:修改失败数据库中数据不存在,大于0:修改成功 )
    */
    @Override
    public int updateAll(${table.objectName} obj,${table.objectName}UpdateParam cond,A${table.objectName}Aop aop){
		try {
           ${table.objectName}Example example=null;
		    if(aop!=null){
		       example=aop.assemblyUpdateAllCriteria(cond);
		    }
		    if(example==null){
				logger.error("类:[{}] 方法:[updateAll] 参数:[example] 异常信息:[查询参数为空,请实现aop(assemblyUpdateAllCriteria)方法封装更新参数]",I${table.objectName}ServiceImpl.class);
				return 0;
			}
		    if(example.getOredCriteria()==null){
				logger.error("类:[{}] 方法:[updateAll] 参数:[example.getOredCriteria()] 异常信息:[查询参数为空,请实现aop(assemblyUpdateAllCriteria)方法封装更新参数]",I${table.objectName}ServiceImpl.class);
				return 0;
			}
		    return ${table.objectName?uncap_first}Mapper.updateByExample(obj,example);
		}catch (MicrovideoBizException e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }
	/**
    * @Description: 根据条件更新指定的字段数据(obj中属性非空的才会更新)
    * @param:${table.objectName} obj 需要更新的业务数据
    * @param:${table.objectName} cond 更新条件
    * @param:A${table.objectName}Aop aop 代理类
    * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功 )
    */
    public int updateSelective(${table.objectName} obj,${table.objectName}UpdateParam cond,A${table.objectName}Aop aop){
		try {
           ${table.objectName}Example example=null;
		    if(aop!=null){
		       example=aop.assemblyUpdateSelectiveCriteria(cond);
		    }
		    if(example==null){
				logger.error("类:[{}] 方法:[updateSelective] 参数:[example] 异常信息:[查询参数为空,请实现aop(assemblyUpdateAllCriteria)方法封装更新参数]",I${table.objectName}ServiceImpl.class);
				return 0;
			}
		    if(example.getOredCriteria()==null){
				logger.error("类:[{}] 方法:[updateSelective] 参数:[example.getOredCriteria()] 异常信息:[查询参数为空,请实现aop(assemblyUpdateAllCriteria)方法封装更新参数]",I${table.objectName}ServiceImpl.class);
				return 0;
			}
		    return ${table.objectName?uncap_first}Mapper.updateByExampleSelective(obj,example);
		}catch (MicrovideoBizException e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }
	/**
	 * @Description: 分页查询
	 * @param:${table.objectName} cond 查询条件
	 * @param:MicrovideoPage page 分页条件
	 * @param:A${table.objectName}Aop aop 代理类
	 * @return: List<${table.objectName}>
	 */
    @Override
	public List<${table.objectName}> query(${table.objectName}QueryParam cond, MicrovideoPage page,A${table.objectName}Aop aop){
        List<${table.objectName}> returnList=null;
		try {
		    ${table.objectName}Example example=null;
		    if(aop!=null){
		       example=aop.assemblyQueryCriteria(cond);
		    }
		    if(example==null){
		       example=new ${table.objectName}Example();
		    }
	        if (page != null) {
	        	page.setTotalNum(${table.objectName?uncap_first}Mapper.countByExample(example));
	        }else{
	        	page=new MicrovideoPage(${table.objectName?uncap_first}Mapper.countByExample(example),1,10);
	        }
	        example.setPage(page);
	        returnList = ${table.objectName?uncap_first}Mapper.selectByExample(example);
		} catch (MicrovideoBizException e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		} catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return returnList;	
	}
	/**
    * @Description: 查询
    * @param:${table.objectName} cond 查询条件
    * @param:A${table.objectName}AopA${table.objectName}Aop aop 代理类
    * @return: List<${table.objectName}>
    */
    @Override
     public List<${table.objectName}> query(${table.objectName}QueryParam cond,A${table.objectName}Aop aop){
         List<${table.objectName}> returnList=null;
     		try {
     		    ${table.objectName}Example example=null;
     		    if(aop!=null){
     		       example=aop.assemblyQueryCriteria(cond);
     		    }
     		    if(example==null){
     		       example=new ${table.objectName}Example();
     		    }
     		    example.setPage(null);
     	        returnList = ${table.objectName?uncap_first}Mapper.selectByExample(example);
     		} catch (MicrovideoBizException e) {
     			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
     			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
     		} catch (Exception e) {
     			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
     			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
     		}
     		return returnList;
     }
    /**
     * @Description: 自定义查询
     * @param:String  busType 业务类型
     * @param:${table.objectName} cond cond 查询条件
     * @param:A${table.objectName}Aop aop 代理类
     * @return: List<${table.objectName}>
     */
    @Override
     public List<${table.objectName}> queryCustom(String busType,${table.objectName}QueryParam cond,A${table.objectName}Aop aop){
		List<${table.objectName}> returnList = null;
		try {
			String sql = null;
			if (aop != null) {
				sql = aop.assemblySql(busType, cond);
			}
			if (sql == null || sql.trim().equals("")) {
				logger.error("类:[{}] 方法:[queryCustom] 参数:[sql] 异常信息:[sql为空,请实现aop.assemblySql生成sql语句]", I${table.objectName}ServiceImpl.class);
				return null;
			}
			returnList = ${table.objectName?uncap_first}Mapper.selectData(sql);
		} catch (MicrovideoBizException e) {
			logger.error(MicrovideoCode.DB_ERROR + ":" + e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		} catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR + ":" + e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return returnList;
     }
    /**
     * @Description: 根据主键删除
     * @param:String  id     主键id
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:删除失败数据库中数据不存在,1:删除成功 )
     */
    @Override
    public int deleteById(String id,A${table.objectName}Aop aop){
		try {
			if (id == null || id.trim().equals("")) {
				logger.error("类:[{}] 方法:[deleteById] 参数:[id] 异常信息:[id为空]",I${table.objectName}ServiceImpl.class);
				return 0;
			}
			${table.objectName} srcObj=this.findById(id,aop);
			if (srcObj == null) {
				logger.error("类:[{}] 方法:[deleteById] 参数:[id] 异常信息:[id={} 数据库中数据不存在]", I${table.objectName}ServiceImpl.class,id);
				return 0;
			}
			return ${table.objectName?uncap_first}Mapper.deleteByPrimaryKey(id);
		} catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }
    /**
     * @Description: 根据多个主键id批量删除
     * @param:String objId    主键id逗号分隔
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:删除失败数据库中数据不存在,大于0:删除成功 )
     */
    @Override
    public int batchDelete(String objId,A${table.objectName}Aop aop){
		try {
			if (objId == null || objId.trim().equals("")) {
				logger.error("类:[{}] 方法:[batchDelete] 参数:[objId] 异常信息:[objId为空]", I${table.objectName}ServiceImpl.class);
				return 0;
			}
			String[] objIds = objId.split(",");
			if (objIds.length==0) {
				logger.error("类:[{}] 方法:[batchDelete] 参数:[objId] 异常信息:[objIds length is 0]", I${table.objectName}ServiceImpl.class);
				return 0;
			}
			${table.objectName}Example example = new ${table.objectName}Example();
			example.createCriteria().andIdIn(Arrays.asList(objIds));
			return ${table.objectName?uncap_first}Mapper.deleteByExample(example);
		} catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }

     /**
      * @Description: 根据条件删除
      * @param:${table.objectName}UpdateParam cond     查询条件
      * @param:A${table.objectName}Aop aop aop 代理类
      * @return: int (0:删除失败数据库中数据不存在,大于0:删除成功 )
      */
     public int deleteByExample(${table.objectName}UpdateParam cond,A${table.objectName}Aop aop){
		try {
           ${table.objectName}Example example=null;
		    if(aop!=null){
		       example=aop.assemblyDeleteByExampleCriteria(cond);
		    }
		    if(example==null){
				logger.error("类:[{}] 方法:[deleteByExample] 参数:[example] 异常信息:[查询参数为空,请实现aop(assemblyDeleteByExampleCriteria)方法封装更新参数]",I${table.objectName}ServiceImpl.class);
				return 0;
			}
		    if(example.getOredCriteria()==null){
				logger.error("类:[{}] 方法:[deleteByExample] 参数:[example.getOredCriteria()] 异常信息:[查询参数为空,请实现aop(assemblyDeleteByExampleCriteria)方法封装更新参数]",I${table.objectName}ServiceImpl.class);
				return 0;
			}
		    return ${table.objectName?uncap_first}Mapper.deleteByExample(example);
		}catch (MicrovideoBizException e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}catch (Exception e) {
			logger.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }

    /**
     * @Description: 清空表(请谨慎操作)
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:删除失败数据库中数据不存在,大于0:删除成功 )
     */
    public int clearTable(A${table.objectName}Aop aop){
		return ${table.objectName?uncap_first}Mapper.clearTable();
    }
    /**
     * @Description: 更新所有数据逻辑删除标记为删除状态
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功)
     */
    public int updateAllDelflagToInvalid(A${table.objectName}Aop aop){
		${table.objectName}UpdateParam obj = new ${table.objectName}UpdateParam();
        obj.setDataDelFlag(1);
        ${table.objectName}Example example = new ${table.objectName}Example();
        return ${table.objectName?uncap_first}Mapper.updateByExampleSelective(obj,example);
    }
    /**
     * @Description: 更新逻辑删除标记是正常的数据为删除状态
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功 )
     */
    public int updateDelflagValidToInvalid(A${table.objectName}Aop aop){
		${table.objectName}UpdateParam obj = new ${table.objectName}UpdateParam();
        obj.setDataDelFlag(1);
        ${table.objectName}Example example = new ${table.objectName}Example();
        example.createCriteria().andDataDelFlagEqualTo(0);
        return ${table.objectName?uncap_first}Mapper.updateByExampleSelective(obj,example);
    }
}