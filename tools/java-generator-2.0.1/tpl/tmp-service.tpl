package ${servicePackage};

import java.util.Arrays;
import java.util.List;

import cn.microvideo.framework.core.basic.asserts.MicrovideoAssert;
import cn.microvideo.framework.core.basic.code.MicrovideoCode;
import cn.microvideo.framework.core.basic.constant.MicrovideoConstant;
import cn.microvideo.framework.core.basic.exception.MicrovideoBizException;
import cn.microvideo.framework.core.basic.page.MicrovideoPage;
import cn.microvideo.framework.core.basic.time.MicrovideoTime;
import cn.microvideo.framework.core.basic.uuid.MicrovideoUUID;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import ${servicePackage}.${table.objectName}Service;
import ${entityPackage}.${table.objectName};
import ${entityPackage}.${table.objectName}Example;
import ${mapperPackage}.${table.objectName}Mapper;
import ${table.javaPackage}.param.${table.objectName}QueryParam;
import ${table.javaPackage}.param.${table.objectName}UpdateParam;
import lombok.extern.slf4j.Slf4j;

/**
 * @company 南京感动科技有限公司
 * @Title:  ${table.objectName}Service.java
 * @Description: 描述(接口本地实现方法)
 * @version V${version}
 */
@Slf4j
@Service
public class ${table.objectName}Service{
	 @Autowired
	 private ${table.objectName}Mapper ${table.objectName?uncap_first}Mapper;

	/**
	 * @Description: 根据主键查询
	 * @param:String id 主键id
	 * @return: ${table.objectName}
	 */
    @Override
	public ${table.objectName} findById(String id){
		${table.objectName} result=null;
		try {
			if(id==null||id.trim().equals("")){
				log.error("类:[{}] 方法:[findById] 参数:[id] 异常信息:[id为空]",${table.objectName}Service.class);
				return null;
			}
			result=${table.objectName?uncap_first}Mapper.selectByPrimaryKey(id);
		} catch (MicrovideoBizException e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		} catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return result;
	}
	/**
	 * @Description: 添加
	 * @param:${table.objectName} obj    业务数据
	 * @return: int 1为成功
	 */
    @Override
	public int add(${table.objectName} obj){
		try {
			MicrovideoAssert.IsObjNull(obj, MicrovideoCode.OBJ_IS_NULL);
			String id =obj.getId();
			if(id==null||id.trim().equals("")){
                id = MicrovideoUUID.get();
                obj.setId(id);
			}
			if(obj.getDataCreateTime()==null){
			    obj.setDataCreateTime(MicrovideoTime.getCurrDate());
			}
			if(obj.getDataUpdateTime()==null){
			    obj.setDataUpdateTime(MicrovideoTime.getCurrDate());
			}
			obj.setDataDelFlag(MicrovideoConstant.LOGICAL_DELETE_NOMAL);
			return ${table.objectName?uncap_first}Mapper.insert(obj);
		} catch (MicrovideoBizException e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		} catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
	}
	/**
	 * @Description: 根据主键更新数据
	 * @param:${table.objectName} obj 更新的数据必须要有主键ID
	 * @return: int (0:修改失败数据库中数据不存在,1:修改成功 )
	 */
    @Override
	public int updateById(${table.objectName} obj){
		try {
			if(obj==null){
				log.error("类:[{}] 方法:[updateById] 参数:[obj] 异常信息:[obj为空]",${table.objectName}Service.class);
				return 0;
			}
			if(obj.getId()==null||obj.getId().trim().equals("")){
				log.error("类:[{}] 方法:[updateById] 参数:[obj] 异常信息:[obj id 为空]",${table.objectName}Service.class);
				return 0;
			}
			${table.objectName} srcObj=this.findById(obj.getId());
			if(srcObj==null){
				log.error("类:[{}] 方法:[updateById] 参数:[obj] 异常信息:[id:{} 修改失败，数据库中数据不存在]",${table.objectName}Service.class,obj.getId());
				return 0;
			}
			obj.setDataCreateTime(srcObj.getDataCreateTime());
			return ${table.objectName?uncap_first}Mapper.updateByPrimaryKey(obj);
		}catch (MicrovideoBizException e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
	}
	/**
    * @Description: 根据条件更新所有字段数据
    * @param:${table.objectName} obj 需要更新的业务数据
    * @param:${table.objectName} cond 更新条件
    * @return:  int (0:修改失败数据库中数据不存在,大于0:修改成功 )
    */
    @Override
    public int updateAll(${table.objectName} obj,${table.objectName}UpdateParam cond){
		try {
           ${table.objectName}Example example=null;
           //编写查询条件
		    return ${table.objectName?uncap_first}Mapper.updateByExample(obj,example);
		}catch (MicrovideoBizException e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }
	/**
    * @Description: 根据条件更新指定的字段数据(obj中属性非空的才会更新)
    * @param:${table.objectName} obj 需要更新的业务数据
    * @param:${table.objectName} cond 更新条件
    * @param:A${table.objectName}Aop aop 代理类
    * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功 )
    */
    public int updateSelective(${table.objectName} obj,${table.objectName}UpdateParam cond){
		try {
           ${table.objectName}Example example=null;
	        //编写更新条件
		    return ${table.objectName?uncap_first}Mapper.updateByExampleSelective(obj,example);
		}catch (MicrovideoBizException e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }
	/**
	 * @Description: 分页查询
	 * @param:${table.objectName} cond 查询条件
	 * @param:MicrovideoPage page 分页条件
	 * @return: List<${table.objectName}>
	 */
    @Override
	public List<${table.objectName}> query(${table.objectName}QueryParam cond, MicrovideoPage page,A${table.objectName}Aop aop){
        List<${table.objectName}> returnList=null;
		try {
		    ${table.objectName}Example example=new ${table.objectName}Example();
		     //编写查询条件
	        if (page != null) {
	        	page.setTotalNum(${table.objectName?uncap_first}Mapper.countByExample(example));
	        }else{
	        	page=new MicrovideoPage(${table.objectName?uncap_first}Mapper.countByExample(example),1,10);
	        }
	        example.setPage(page);
	        returnList = ${table.objectName?uncap_first}Mapper.selectByExample(example);
		} catch (MicrovideoBizException e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		} catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return returnList;
	}
	/**
    * @Description: 查询
    * @param:${table.objectName} cond 查询条件
    * @return: List<${table.objectName}>
    */
    @Override
     public List<${table.objectName}> query(${table.objectName}QueryParam cond){
         List<${table.objectName}> returnList=null;
     		try {
     		    ${table.objectName}Example example=new ${table.objectName}Example();
		        //编写查询条件
     		    example.setPage(null);
     	        returnList = ${table.objectName?uncap_first}Mapper.selectByExample(example);
     		} catch (MicrovideoBizException e) {
     			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
     			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
     		} catch (Exception e) {
     			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
     			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
     		}
     		return returnList;
     }
    /**
     * @Description: 自定义查询
     * @param:String  busType 业务类型
     * @param:${table.objectName} cond cond 查询条件
     * @return: List<${table.objectName}>
     */
    @Override
     public List<${table.objectName}> queryCustom(String busType,${table.objectName}QueryParam cond){
		List<${table.objectName}> returnList = null;
		try {
			String sql = null;
			//编写sql
			returnList = ${table.objectName?uncap_first}Mapper.selectData(sql);
		} catch (MicrovideoBizException e) {
			log.error(MicrovideoCode.DB_ERROR + ":" + e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		} catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR + ":" + e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return returnList;
     }
    /**
     * @Description: 根据主键删除
     * @param:String  id     主键id
     * @return: int (0:删除失败数据库中数据不存在,1:删除成功 )
     */
    @Override
    public int deleteById(String id){
		try {
			${table.objectName} srcObj=this.findById(id,aop);
			if (srcObj == null) {
				log.error("类:[{}] 方法:[deleteById] 参数:[id] 异常信息:[id={} 数据库中数据不存在]", ${table.objectName}Service.class,id);
				return 0;
			}
			return ${table.objectName?uncap_first}Mapper.deleteByPrimaryKey(id);
		} catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }
    /**
     * @Description: 根据多个主键id批量删除
     * @param:String objId    主键id逗号分隔
     * @return: int (0:删除失败数据库中数据不存在,大于0:删除成功 )
     */
    @Override
    public int batchDelete(String objId){
		try {
			if (objId == null || objId.trim().equals("")) {
				log.error("类:[{}] 方法:[batchDelete] 参数:[objId] 异常信息:[objId为空]", ${table.objectName}Service.class);
				return 0;
			}
			String[] objIds = objId.split(",");
			if (objIds.length==0) {
				log.error("类:[{}] 方法:[batchDelete] 参数:[objId] 异常信息:[objIds length is 0]", ${table.objectName}Service.class);
				return 0;
			}
			${table.objectName}Example example = new ${table.objectName}Example();
			example.createCriteria().andIdIn(Arrays.asList(objIds));
			return ${table.objectName?uncap_first}Mapper.deleteByExample(example);
		} catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }

     /**
      * @Description: 根据条件删除
      * @param:${table.objectName}UpdateParam cond     查询条件
      * @return: int (0:删除失败数据库中数据不存在,大于0:删除成功 )
      */
     public int deleteByExample(${table.objectName}UpdateParam cond){
		try {
           ${table.objectName}Example example=null;
           //编写删除条件
		    return ${table.objectName?uncap_first}Mapper.deleteByExample(example);
		}catch (MicrovideoBizException e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}catch (Exception e) {
			log.error(MicrovideoCode.DB_ERROR+":"+e.getMessage());
			MicrovideoAssert.error(MicrovideoCode.DB_ERROR);
		}
		return 1;
    }

    /**
     * @Description: 清空表(请谨慎操作)
     * @return: int (0:删除失败数据库中数据不存在,大于0:删除成功 )
     */
    public int clearTable(){
		return ${table.objectName?uncap_first}Mapper.clearTable();
    }
    /**
     * @Description: 更新所有数据逻辑删除标记为删除状态
     * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功)
     */
    public int updateAllDelflagToInvalid(){
		${table.objectName}UpdateParam obj = new ${table.objectName}UpdateParam();
        obj.setDataDelFlag(1);
        ${table.objectName}Example example = new ${table.objectName}Example();
        return ${table.objectName?uncap_first}Mapper.updateByExampleSelective(obj,example);
    }
    /**
     * @Description: 更新逻辑删除标记是正常的数据为删除状态
     * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功 )
     */
    public int updateDelflagValidToInvalid(){
		${table.objectName}UpdateParam obj = new ${table.objectName}UpdateParam();
        obj.setDataDelFlag(1);
        ${table.objectName}Example example = new ${table.objectName}Example();
        example.createCriteria().andDataDelFlagEqualTo(0);
        return ${table.objectName?uncap_first}Mapper.updateByExampleSelective(obj,example);
    }
}