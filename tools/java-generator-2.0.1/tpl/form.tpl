package ${requestObjectPackage};

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import javax.validation.constraints.Max;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;

@Data
public  class ${table.objectName}FormParam  implements Serializable {
<#list table.columns as column>
<#if column.required ?? &&(column.required)&&(column.javaType?upper_case != 'DATE' && column.javaType?upper_case != 'DATETIME' && column.javaType?upper_case != 'TIMESTAMP') >
    @NotNull(message = "${column.label}不能为空")
</#if>
<#if column.required ?? &&(column.required)&&(column.javaType?upper_case == 'STRING' ) >
    @NotEmpty(message = "${column.label}不能为空")
</#if>
<#if column.max ?? &&(column.max != '0' &&column.max != '-1' &&column.max != '')>
   <#if column.javaType ?? &&(column.javaType?upper_case == 'STRING')>
    @Size(min = 0,max=${column.max} ,message = "${column.label}长度最大${column.max}")
    </#if>
    <#if column.javaType ?? &&column.maxValue ?? &&(column.javaType?upper_case == 'INTEGER')>
    @Max(value = ${column.maxValue}L,message = "${column.label}最大${column.maxValue}")
     </#if>
</#if>
    private ${column.javaType} ${column.selfName};
</#list>
}
