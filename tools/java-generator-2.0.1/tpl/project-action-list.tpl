package cn.microvideo.demo.module.gantry.action;

import cn.microvideo.bds.sys.spring.MicrovideoSpringBeanUtil;
import cn.microvideo.framework.json.util.MicrovideoJsonUtil;
import ${requestObjectPackage}.${table.objectName}QueryParam;
import cn.microvideo.framework.core.action.MicrovideoAction;
import cn.microvideo.framework.core.constants.MicrovideoConstants;
import cn.microvideo.framework.core.context.MicrovideoContext;
import org.springframework.stereotype.Service;

/**
 * @description 列表查询
 * @com 感动科技
 * <AUTHOR>
 * @date 2021/03/12 14:55:41
 **/
@Service
public class ${table.objectName}ListAction extends MicrovideoAction<Object, Object> {
    @Override
    public MicrovideoContext<Object, Object> transfer(MicrovideoContext<Object, Object> context) {
        context.setTransitionVal(MicrovideoConstants.YES);
        ${table.objectName}QueryParam param =null;
        if(context.getReq()!=null&&context.getReq().getParam()!=null){
            param= MicrovideoJsonUtil.parseObject(MicrovideoJsonUtil.toJSONStringWithDateFormat(context.getReq().getParam(),MicrovideoJsonUtil.FULL_DATE_TIME_FORMAT),${table.objectName}QueryParam.class);
        }
        context.setObj(MicrovideoSpringBeanUtil.get${table.objectName}LocalService().queryByCond(param));
        return context;
    }
}
