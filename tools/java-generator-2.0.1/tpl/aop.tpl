package ${aopPackage};

import java.util.List;
import cn.microvideo.framework.core.basic.page.MicrovideoPage;
import ${table.javaPackage}.entity.${table.objectName};
import ${table.javaPackage}.entity.${table.objectName}Example;
import ${table.javaPackage}.param.${table.objectName}QueryParam;
import ${table.javaPackage}.param.${table.objectName}UpdateParam;
/**    
 * @company 南京感动科技有限公司
 * @Title:  I${table.objectName}Aop.java   
 * @Description:
 * @author: 
 * @mobile:    
 * @date:   
 * @version V${version}    
 */  	
public abstract class A${table.objectName}Aop {
	/**
	 * @Description: 组装查询条(查询接口)
	 * @param: cond 查询条件
	 * @return: ${table.objectName}Example
	 */
	public abstract ${table.objectName}Example assemblyQueryCriteria(${table.objectName}QueryParam cond);
	/**
	 * @Description: 拼接sql语句(自定义查询)
	 * @param: busType 业务类型
	 * @param: cond 查询条件
	 * @return: String
	 */
    public abstract String assemblySql(String busType,${table.objectName}QueryParam cond);
	/**
	 * @Description: 组装查询条(根据条件更新所有字段数据)
	 * @param: cond 查询条件
	 * @return: String
	 */
    public abstract ${table.objectName}Example assemblyUpdateAllCriteria(${table.objectName}UpdateParam cond);
	/**
	 * @Description: 组装查询条(根据条件更新指定的字段数据(obj中属性非空的才会更新))
	 * @param: cond 查询条件
	 * @return: String
	 */
    public abstract ${table.objectName}Example assemblyUpdateSelectiveCriteria(${table.objectName}UpdateParam cond);
	/**
	 * @Description: 组装查询条(根据条件删除)
	 * @param: cond 查询条件
	 * @return: String
	 */
    public abstract ${table.objectName}Example assemblyDeleteByExampleCriteria(${table.objectName}UpdateParam cond);

	
	/**     
	 * @Description: 根据主键查询  
	 * @param: id 主键id
	 * @return: ${table.objectName}
	 */  
	public abstract ${table.objectName} findById(String id);
	/**     
	 * @Description: 添加
	 * @param: obj    业务数据
	 * @return: void
	 */  
	public abstract void add(${table.objectName} obj);
	/**     
	 * @Description: 根据主键更新数据 
	 * @param: obj 更新的数据必须要有主键ID
	 * @return: void
	 */  
	public abstract void updateById(${table.objectName} obj);
	/**
    * @Description: 根据条件更新所有字段数据
    * @param: obj 需要更新的业务数据
    * @param: cond 更新条件
    * @return: void
    */
    public abstract void updateAll(${table.objectName} obj,${table.objectName}UpdateParam cond);
	/**
    * @Description: 根据条件更新指定的字段数据(obj中属性非空的才会更新)
    * @param: obj 需要更新的业务数据
    * @param: cond 更新条件
    * @return: void
    */
    public abstract void updateSelective(${table.objectName} obj,${table.objectName}UpdateParam cond);
	/**     
	 * @Description: 分页查询  
	 * @param: cond 查询条件
	 * @param: page 分页条件
	 * @return: List<${table.objectName}>       
	 */  
	public abstract List<${table.objectName}> query(${table.objectName}QueryParam cond,MicrovideoPage page);
	/**
    * @Description: 查询(不带分页)
    * @param: cond 查询条件
    * @return: List<${table.objectName}>
    */
    public abstract List<${table.objectName}> query(${table.objectName}QueryParam cond);
    /**
     * @Description: 自定义查询
     * @param:  busType 业务类型
     * @param: cond 查询条件
     * @return: List<${table.objectName}>
     */
    public abstract List<${table.objectName}> queryCustom(String busType,${table.objectName}QueryParam cond);
    /**
     * @Description: 根据主键删除
     * @param:  id     主键id
     * @param: aop 代理类
     * @return: void      
     */  
    public abstract void deleteById(String id);
    /**     
     * @Description: 根据多个主键id批量删除
     * @param: objId    主键id逗号分隔
     * @param: aop 代理类
     * @return: void      
     */  
    public abstract void batchDelete(String objId);
	/**
	 * @Description: 组装查询条(根据条件删除)
	 * @param: cond 查询条件
	 * @return: String
	 */
    public abstract int deleteByExample(${table.objectName}UpdateParam cond);
    /**
     * @Description: 清空表(请谨慎操作)
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:删除失败数据库中数据不存在,大于0:删除成功 )
     */
    public abstract int clearTable();
    /**
     * @Description: 更新所有数据逻辑删除标记为删除状态
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功)
     */
    public abstract int updateAllDelflagToInvalid();
    /**
     * @Description: 更新逻辑删除标记是正常的数据为删除状态
     * @param:A${table.objectName}Aop aop aop 代理类
     * @return: int (0:修改失败数据库中数据不存在,大于0:修改成功 )
     */
    public abstract int updateDelflagValidToInvalid();
}
