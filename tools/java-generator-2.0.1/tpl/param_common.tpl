package ${requestObjectPackage};

import ${table.javaPackage}.entity.${table.objectName};

import lombok.Data;

@Data
public class ${table.objectName}CommonParam extends ${table.objectName} {
        <#list table.columns as column>
        		<#if column.javaType ?? &&(column.javaType?upper_case == 'DATE' || column.javaType?upper_case == 'DATETIME' || column.javaType?upper_case == 'TIMESTAMP'|| column.javaType?upper_case == 'STRING')>
    private String ${column.selfName}Text;
        	    </#if>
        	    <#if column.javaType ?? &&(column.javaType?upper_case == 'INT' || column.javaType?upper_case == 'INTEGER' )>
    private String ${column.selfName}Text;
                 </#if>
                 <#if column.javaType ?? &&(column.javaType?upper_case == 'LONG'  )>
    private String ${column.selfName}Text;
                   </#if>
                   <#if column.javaType ?? &&(column.javaType?upper_case == 'DOUBLE' )>
    private String ${column.selfName}Text;
                   </#if>
                   <#if column.javaType ?? &&(column.javaType?upper_case == 'FLOAT' )>
    private String ${column.selfName}Text;
                   </#if>
        	</#list>
}
