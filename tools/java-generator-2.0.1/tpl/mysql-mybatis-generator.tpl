<?xml version="1.0" encoding="UTF-8"?>  
<!DOCTYPE generatorConfiguration  
  PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"  
  "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<context id="auth" targetRuntime="MyBatis3">
		<plugin type="cn.microvideo.generator.plugin.db.MysqlPlugin" />
		<commentGenerator>
			<property name="javaFileEncoding" value="UTF-8"/>
			<property name="suppressDate" value="true" />
		</commentGenerator>

		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
			connectionURL="${url}"
			userId="${username}" password="${password}">
		</jdbcConnection>

		<javaTypeResolver>
			<property name="forceBigDecimals" value="false" />
		</javaTypeResolver>

		<javaModelGenerator targetPackage="${table.javaPackage}.entity"
			targetProject="${projectPath}">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="true" />
		</javaModelGenerator>

		<sqlMapGenerator targetPackage="mapper"
			targetProject="${mapperXmlPath}">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>

		<javaClientGenerator type="XMLMAPPER"
			targetPackage="${table.javaPackage}.mapper" targetProject="${projectPath}">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>

		<table tableName="${root.tableName}" domainObjectName="${root.objectName}" enableCountByExample="true" enableUpdateByExample="true"
		enableDeleteByExample="true"
		enableSelectByExample="true">
		</table>

	</context>
</generatorConfiguration>