package ${requestObjectPackage};

import ${table.javaPackage}.entity.${table.objectName};
import cn.microvideo.framework.core.basic.page.MicrovideoPage;

import lombok.Data;

/**
 * 南京感动科技有限公司.
 * <#if table.tableDesc??>${table.tableDesc}</#if>-查询参数
 * <AUTHOR>
 * @since ${currDate}
 */
@Data
public class ${table.objectName}QueryParam extends ${table.objectName} {
    /**
     * 分页.
     */
    private MicrovideoPage page;

    @Override
    public boolean equals(final Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}

