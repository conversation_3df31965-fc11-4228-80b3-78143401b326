<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.microvideo.framework2.project</groupId>
    <artifactId>plyh-schedule</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>plyh-schedule</name>

    <properties>
        <!-- Project revision -->
        <revision>2.0.6-SNAPSHOT</revision>
        <!-- ????????? -->
        <microvideo.framework2.version>2.0.4-SNAPSHOT</microvideo.framework2.version>
        <hutool.version>5.7.16</hutool.version>
        <jedis.version>3.7.0</jedis.version>
        <!-- Maven Plugin Versions -->
        <maven-compiler-plugin.version>3.7.0</maven-compiler-plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.target>1.8</maven.compiler.target>
        <main.basedir>${project.basedir}</main.basedir>
        <pageHelper.version>1.2.5</pageHelper.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.microvideo.framework2</groupId>
                <artifactId>microvideo-framework2-dependencies</artifactId>
                <version>${microvideo.framework2.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <!--去除5.0版本的MySql-->
                <exclusions>
                    <exclusion>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- mysql8.0驱动包 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.32</version>
            </dependency>
            <!--  app -->
            <dependency>
                <groupId>cn.microvideo.framework2.project</groupId>
                <artifactId>application-report</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--  services -->
            <dependency>
                <groupId>cn.microvideo.framework2.project</groupId>
                <artifactId>services-report</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>cn.microvideo.framework2.support</groupId>
                <artifactId>microvideo-support-swagger</artifactId>
                <version>${microvideo.framework2.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.microvideo.framework2.core</groupId>
                <artifactId>microvideo-core-basic</artifactId>
                <version>${microvideo.framework2.version}</version>
            </dependency>

            <!-- webapp-cloud??? -->
            <dependency>
                <groupId>cn.microvideo.framework2.support</groupId>
                <artifactId>microvideo-support-webapp-cloud-tomcat</artifactId>
                <version>${microvideo.framework2.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.microvideo.framework2.support</groupId>
                <artifactId>microvideo-support-webapp-cloud-tomcat-mybatisplus</artifactId>
                <version>${microvideo.framework2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.microvideo.framework2.support</groupId>
                <artifactId>microvideo-support-db-clickhouse</artifactId>
                <version>${microvideo.framework2.version}</version>
            </dependency>
            <!--hutool-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>


            <!--redis-->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>


            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.8.1</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0-M5</version>
                    <configuration>
                        <skipTests>true</skipTests>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>3.0.0-M5</version>
                    <configuration>
                        <showSuccess>false</showSuccess>
                    </configuration>
                </plugin>

                <!--       <plugin>
                           <groupId>org.apache.maven.plugins</groupId>
                           <artifactId>maven-checkstyle-plugin</artifactId>
                           <version>3.1.2</version>
                           <configuration>
                               <configLocation>style/checkstyle-rules.xml</configLocation>
                               <suppressionsLocation>style/checkstyle-suppressions.xml</suppressionsLocation>
                               <encoding>UTF-8</encoding>
                               <consoleOutput>true</consoleOutput>
                               <failsOnError>true</failsOnError>
                               <includeTestSourceDirectory>false</includeTestSourceDirectory>
                           </configuration>
                           <dependencies>
                               <dependency>
                                   <groupId>com.puppycrawl.tools</groupId>
                                   <artifactId>checkstyle</artifactId>
                                   <version>8.32</version>
                               </dependency>
                           </dependencies>
                           <executions>
                               <execution>
                                   <id>checkstyle-check</id>
                                   <phase>validate</phase>
                                   <goals>
                                       <goal>check</goal>
                                   </goals>
                               </execution>
                           </executions>
                       </plugin>-->
                <!-- <plugin>
                     <groupId>com.github.spotbugs</groupId>
                     <artifactId>spotbugs-maven-plugin</artifactId>
                     <version>4.4.2.2</version>
                     <inherited>true</inherited>
                     <dependencies>
                         <dependency>
                             <groupId>com.github.spotbugs</groupId>
                             <artifactId>spotbugs</artifactId>
                             <version>4.5.0</version>
                         </dependency>
                     </dependencies>
                     <configuration>
                         <includeFilterFile>${main.basedir}/style/spotbugs-includes.xml</includeFilterFile>
                         <excludeFilterFile>${main.basedir}/style/spotbugs-excludes.xml</excludeFilterFile>
                         <plugins>
                             <plugin>
                                 <groupId>com.h3xstream.findsecbugs</groupId>
                                 <artifactId>findsecbugs-plugin</artifactId>
                                 <version>LATEST</version>
                             </plugin>
                             <plugin>
                                 <groupId>com.mebigfatguy.fb-contrib</groupId>
                                 <artifactId>fb-contrib</artifactId>
                                 <version>7.4.7</version>
                             </plugin>
                         </plugins>
                     </configuration>
                     <executions>
                         <execution>
                             <id>spotbugs-check</id>
                             <phase>compile</phase>
                             <goals>
                                 <goal>check</goal>
                             </goals>
                         </execution>
                     </executions>
                 </plugin>-->


                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <inherited>true</inherited>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <parameters>true</parameters>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>mcirovieo-maven</id>
            <name>mcirovieo-maven</name>
            <url>http://nexus.microvideo.cn/nexus/repository/microvideo-framework-snapshot/</url>
        </repository>
        <repository>
            <id>microvideo-maven-releases</id>
            <name>microvideo-maven-releases</name>
            <url>http://nexus.microvideo.cn/nexus/repository/maven-releases/</url>
        </repository>
    </repositories>
    <modules>
        <module>application</module>
        <module>services</module>
    </modules>
</project>
