# DutyRosterController Lambda 查询示例

## 概述

本文档展示了 DutyRosterController 中使用 MyBatis-Plus LambdaQueryWrapper 的具体实现，相比传统的字符串字段名查询，Lambda 方式提供了更好的类型安全性和重构支持。

## Lambda 查询的优势

### 1. 类型安全
```java
// ❌ 传统方式 - 容易出错，字段名写错编译时不会报错
queryWrapper.like("F_VC_TITLE", title);

// ✅ Lambda 方式 - 类型安全，字段名错误编译时就会报错
queryWrapper.like(DutyRoster::getTitle, title);
```

### 2. 重构友好
当实体类字段名发生变化时，Lambda 方式会自动更新，而字符串方式需要手动查找替换。

### 3. IDE 支持
IDE 可以提供自动补全、跳转到定义等功能。

## 实际代码示例

### DutyRoster 分页查询

```java
@GetMapping(value = "/list")
public Result<IPage<DutyRoster>> queryPageList(DutyRoster dutyRoster,
                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                               HttpServletRequest req) {
    Result<IPage<DutyRoster>> result = new Result<IPage<DutyRoster>>();
    LambdaQueryWrapper<DutyRoster> queryWrapper = new LambdaQueryWrapper<DutyRoster>();
    
    // 标题模糊查询
    if (dutyRoster.getTitle() != null && !dutyRoster.getTitle().trim().isEmpty()) {
        queryWrapper.like(DutyRoster::getTitle, dutyRoster.getTitle());
    }
    
    // 组织ID精确查询
    if (dutyRoster.getOrgId() != null && !dutyRoster.getOrgId().trim().isEmpty()) {
        queryWrapper.eq(DutyRoster::getOrgId, dutyRoster.getOrgId());
    }
    
    // 组织名称模糊查询
    if (dutyRoster.getOrgName() != null && !dutyRoster.getOrgName().trim().isEmpty()) {
        queryWrapper.like(DutyRoster::getOrgName, dutyRoster.getOrgName());
    }
    
    // 按创建时间倒序排列
    queryWrapper.orderByDesc(DutyRoster::getCreateTime);
    
    Page<DutyRoster> page = new Page<DutyRoster>(pageNo, pageSize);
    IPage<DutyRoster> pageList = dutyRosterService.page(page, queryWrapper);
    result.setSuccess(true);
    result.setResult(pageList);
    return result;
}
```

### DutyRosterSchedule 分页查询

```java
@GetMapping(value = "/schedule/list")
public Result<IPage<DutyRosterSchedule>> querySchedulePageList(DutyRosterSchedule dutyRosterSchedule,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                               HttpServletRequest req) {
    Result<IPage<DutyRosterSchedule>> result = new Result<IPage<DutyRosterSchedule>>();
    LambdaQueryWrapper<DutyRosterSchedule> queryWrapper = new LambdaQueryWrapper<DutyRosterSchedule>();
    
    // 值班表ID精确查询
    if (dutyRosterSchedule.getRosterId() != null && !dutyRosterSchedule.getRosterId().trim().isEmpty()) {
        queryWrapper.eq(DutyRosterSchedule::getRosterId, dutyRosterSchedule.getRosterId());
    }
    
    // 开始时间范围查询（大于等于）
    if (dutyRosterSchedule.getStartTime() != null) {
        queryWrapper.ge(DutyRosterSchedule::getStartTime, dutyRosterSchedule.getStartTime());
    }
    
    // 结束时间范围查询（小于等于）
    if (dutyRosterSchedule.getEndTime() != null) {
        queryWrapper.le(DutyRosterSchedule::getEndTime, dutyRosterSchedule.getEndTime());
    }
    
    // 按开始时间升序排列
    queryWrapper.orderByAsc(DutyRosterSchedule::getStartTime);
    
    Page<DutyRosterSchedule> page = new Page<DutyRosterSchedule>(pageNo, pageSize);
    IPage<DutyRosterSchedule> pageList = dutyRosterScheduleService.page(page, queryWrapper);
    result.setSuccess(true);
    result.setResult(pageList);
    return result;
}
```

### 根据值班表ID查询排班时间列表

```java
@GetMapping(value = "/schedule/queryByRosterId")
public Result<List<DutyRosterSchedule>> queryScheduleByRosterId(@RequestParam(name = "rosterId", required = true) String rosterId) {
    Result<List<DutyRosterSchedule>> result = new Result<List<DutyRosterSchedule>>();
    try {
        LambdaQueryWrapper<DutyRosterSchedule> queryWrapper = new LambdaQueryWrapper<DutyRosterSchedule>();
        queryWrapper.eq(DutyRosterSchedule::getRosterId, rosterId);
        queryWrapper.orderByAsc(DutyRosterSchedule::getStartTime);
        List<DutyRosterSchedule> scheduleList = dutyRosterScheduleService.list(queryWrapper);
        result.setResult(scheduleList);
        result.setSuccess(true);
    } catch (Exception e) {
        log.error("查询失败", e.getMessage());
        result.error500("查询失败");
    }
    return result;
}
```

## 常用 Lambda 查询方法

### 条件查询方法

| 方法 | 说明 | 示例 |
|------|------|------|
| `eq` | 等于 | `queryWrapper.eq(DutyRoster::getOrgId, orgId)` |
| `ne` | 不等于 | `queryWrapper.ne(DutyRoster::getDelFlag, 1)` |
| `like` | 模糊查询 | `queryWrapper.like(DutyRoster::getTitle, title)` |
| `likeLeft` | 左模糊查询 | `queryWrapper.likeLeft(DutyRoster::getTitle, title)` |
| `likeRight` | 右模糊查询 | `queryWrapper.likeRight(DutyRoster::getTitle, title)` |
| `gt` | 大于 | `queryWrapper.gt(DutyRoster::getSort, 0)` |
| `ge` | 大于等于 | `queryWrapper.ge(DutyRosterSchedule::getStartTime, startTime)` |
| `lt` | 小于 | `queryWrapper.lt(DutyRoster::getSort, 100)` |
| `le` | 小于等于 | `queryWrapper.le(DutyRosterSchedule::getEndTime, endTime)` |
| `in` | 在...之中 | `queryWrapper.in(DutyRoster::getOrgId, orgIdList)` |
| `notIn` | 不在...之中 | `queryWrapper.notIn(DutyRoster::getOrgId, excludeOrgIds)` |
| `isNull` | 为空 | `queryWrapper.isNull(DutyRoster::getUpdateTime)` |
| `isNotNull` | 不为空 | `queryWrapper.isNotNull(DutyRoster::getCreateTime)` |

### 排序方法

| 方法 | 说明 | 示例 |
|------|------|------|
| `orderByAsc` | 升序 | `queryWrapper.orderByAsc(DutyRosterSchedule::getStartTime)` |
| `orderByDesc` | 降序 | `queryWrapper.orderByDesc(DutyRoster::getCreateTime)` |

### 逻辑连接

| 方法 | 说明 | 示例 |
|------|------|------|
| `and` | 并且 | `queryWrapper.eq(DutyRoster::getOrgId, orgId).and(w -> w.like(DutyRoster::getTitle, title))` |
| `or` | 或者 | `queryWrapper.eq(DutyRoster::getOrgId, orgId).or().like(DutyRoster::getTitle, title)` |

## 最佳实践

### 1. 空值检查
```java
// 推荐：先检查参数是否为空
if (dutyRoster.getTitle() != null && !dutyRoster.getTitle().trim().isEmpty()) {
    queryWrapper.like(DutyRoster::getTitle, dutyRoster.getTitle());
}
```

### 2. 链式调用
```java
// 推荐：使用链式调用提高代码可读性
queryWrapper.eq(DutyRoster::getOrgId, orgId)
           .like(DutyRoster::getTitle, title)
           .orderByDesc(DutyRoster::getCreateTime);
```

### 3. 复杂条件分组
```java
// 复杂条件可以使用 and/or 进行分组
queryWrapper.eq(DutyRoster::getOrgId, orgId)
           .and(wrapper -> wrapper.like(DutyRoster::getTitle, title)
                                 .or()
                                 .like(DutyRoster::getOrgName, orgName));
```

## 总结

使用 Lambda 查询方式的 DutyRosterController 具有以下优势：

1. **类型安全**：编译时检查，减少运行时错误
2. **重构友好**：字段名变更时自动更新
3. **IDE 支持**：自动补全、语法高亮、跳转定义
4. **代码可读性**：更清晰的表达查询意图
5. **维护性**：更容易理解和维护

这种实现方式符合现代 Java 开发的最佳实践，推荐在所有新项目中使用。
