<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.microvideo.module.plyh.core.mapper.HolidayDeptUserMapper">
    <select id="selectByHolidayDeptIds" resultType="cn.microvideo.module.plyh.core.vo.HolidayDeptUserDetailVO">
        select
        bphdu.F_VC_ID as id,
        bphdu.F_VC_HOLIDAY_ID as holidayId,
        bphdu.F_VC_DUTY_DEPT_ID as dutyDeptId,
        bpdu.F_VC_DEPT_ID as deptId,
        bpdu.F_VC_USER_ID as userId,
        bpdu.F_VC_USER_NAME as userName,
        bpdu.F_VC_USER_MOBILE as userMobile,
        bpdu.F_INT_SORT as sort,
        bpdu.F_VC_CREATE_BY as createBy,
        bpdu.F_DT_CREATE_TIME as createTime,
        bpdu.F_VC_UPDATE_BY as updateBy,
        bpdu.F_DT_UPDATE_TIME as updateTime
        from b_plyh_holiday_dept_user bphdu
        left join b_plyh_dept_user bpdu on
        bphdu.F_VC_DEPT_USER_ID = bpdu.F_VC_ID
        where 1=1
        <if test="holidayIds != null ">
            and bphdu.F_VC_HOLIDAY_ID in
            <foreach collection="holidayIds" item="holidayIds" index="index" open="(" close=")"
                     separator=",">
                #{holidayIds}
            </foreach>
        </if>
        <if test="deptIds != null ">
            and bphdu.F_VC_DUTY_DEPT_ID in
            <foreach collection="deptIds" item="deptIds" index="index" open="(" close=")"
                     separator=",">
                #{deptIds}
            </foreach>
        </if>
        order by bpdu.F_INT_SORT asc
    </select>



    <select id="selectByHolidayIdsWithDept" resultType="cn.microvideo.module.plyh.core.vo.HolidayDeptUserDetailVO">
        select
        bphdu.F_VC_ID as id,
        bphdu.F_VC_HOLIDAY_ID as holidayId,
        bphdu.F_VC_DUTY_DEPT_ID as dutyDeptId,
        bpdu.F_VC_DEPT_ID as deptId,
        bpdu.F_VC_USER_ID as userId,
        bpdu.F_VC_USER_NAME as userName,
        bpdu.F_VC_USER_MOBILE as userMobile,
        bpdu.F_INT_SORT as sort,
        bpdu.F_VC_CREATE_BY as createBy,
        bpdu.F_DT_CREATE_TIME as createTime,
        bpdu.F_VC_UPDATE_BY as updateBy,
        bpdu.F_DT_UPDATE_TIME as updateTime
        from b_plyh_holiday_dept_user bphdu
        left join b_plyh_dept_user bpdu on
        bphdu.F_VC_DEPT_USER_ID = bpdu.F_VC_ID
        where 1=1
        <if test="holidayIds != null and holidayIds != ''">
            and bphdu.F_VC_HOLIDAY_ID =#{holidayIds}
        </if>
        <if test="deptId != null and deptId != ''">
            and bphdu.F_VC_DUTY_DEPT_ID = #{deptId}
        </if>
        order by bpdu.F_INT_SORT asc
    </select>






</mapper>