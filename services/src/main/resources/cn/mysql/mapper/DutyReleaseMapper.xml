<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.microvideo.module.plyh.core.mapper.DutyReleaseMapper">
    <select id="pageList" resultType="cn.microvideo.module.plyh.core.vo.DutyReleaseUserVO">
        select bpdr.F_VC_ID          as id,
               bpdr.F_VC_TITLE       as title,
               bpdr.F_VC_ORG_ID      as orgId,
               bpdr.F_VC_ORG_NAME    as orgName,
               bpdr.F_VC_CREATE_BY   as createBy,
               bpdr.F_DT_CREATE_TIME as createTime,
               bpdr.F_VC_UPDATE_BY   as updateBy,
               bpdr.F_VC_UPDATE_TIME as updateTime,
               bpdr.F_VC_USER_ID     as userId,
               bpdr.F_VC_USER_NAME   as userName
        from b_plyh_duty_release bpdr
        where 1=1
        <if test="query.title != null and query.title != ''">
            and bpdr.F_VC_TITLE like CONCAT('%',#{query.title},'%')
        </if>
        <if test="query.orgId != null and query.orgId != ''">
            and bpdr.F_VC_ORG_ID = #{query.orgId}
        </if>
        <if test="query.startTime != null and query.startTime != ''">
            and DATE_FORMAT( bpdr.F_DT_CREATE_TIME, '%Y-%m-%d' ) &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            and DATE_FORMAT( bpdr.F_DT_CREATE_TIME, '%Y-%m-%d' ) &lt;= #{query.endTime}
        </if>

        order by bpdr.F_DT_CREATE_TIME desc


    </select>

</mapper>