package cn.microvideo.module.plyh.core.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 通知公示管理表
 * @Author: spring-boot
 * @Date: 2023-12-04
 * @Version: V1.0
 */
@Data
@Builder
@ApiModel(value="通知公示管理表对象", description="通知公示管理")
public class NoticePublicity {
    /**
     * UUID
     */

    @ApiModelProperty(value = "UUID")
    private String id;
    /**
     * 通知公告关联ID
     */

    @ApiModelProperty(value = "通知公告关联ID")
    private String noticeId;
    /**
     * 文件类型（0通知 1公告）
     */

    @ApiModelProperty(value = "文件类型")
    private String fileType;
    /**
     * 标题
     */

    @ApiModelProperty(value = "标题")
    private String noticeTitle;
    /**
     * 访问地址
     */

    @ApiModelProperty(value = "访问地址")
    private Object noticeUrl;
    /**
     * 单位ID
     */

    @ApiModelProperty(value = "单位ID")
    private String groupId;
    /**
     * 单位名称
     */

    @ApiModelProperty(value = "单位名称")
    private String groupName;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 更新时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 删除状态（0正常 1删除）
     */

    @ApiModelProperty(value = "删除状态（0正常 1删除）")
    private Integer deleteFlag;
    /**
     * 用户ID
     */

    @ApiModelProperty(value = "用户ID")
    private String userId;
    /**
     * 用户名称
     */

    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 预览id
     */
    @ApiModelProperty(value = "预览id")
    private String viewId;
}
