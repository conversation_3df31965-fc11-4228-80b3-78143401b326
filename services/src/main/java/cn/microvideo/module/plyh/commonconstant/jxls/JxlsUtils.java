package cn.microvideo.module.plyh.commonconstant.jxls;

import cn.microvideo.framework.core.util.jxls.command.LinkCommand;
import cn.microvideo.framework.core.util.jxls.command.MergeCommand;
import cn.microvideo.framework.core.util.jxls.helper.JxlsHelperPlus;
import lombok.extern.slf4j.Slf4j;
import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.jxls.command.EachCommand;
import org.jxls.command.ImageCommand;
import org.jxls.common.Context;
import org.jxls.transform.poi.PoiTransformer;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * Excel模板导出工具类
 *
 * <AUTHOR>
 */

@Slf4j
public final class JxlsUtils {
    /**
     * 模板目录
     */

    public static final String TEMPLATE_DIR = "excel";

    private JxlsUtils() {

    }

    // 添加自定义指令（可覆盖jxls原指令）
    static {
        XlsCommentAreaBuilder.addCommandMapping("image", ImageCommand.class);
        XlsCommentAreaBuilder.addCommandMapping("each", EachCommand.class);
        XlsCommentAreaBuilder.addCommandMapping("merge", MergeCommand.class);
        XlsCommentAreaBuilder.addCommandMapping("link", LinkCommand.class);
    }

    /**
     * 获取模板文件
     *
     * @param filename
     * @return
     */

    public static File getTemplateFile(String filename) {
        ClassLoader context = Thread.currentThread().getContextClassLoader();
        String dir = context.getResource(TEMPLATE_DIR).getPath();
        return new File(dir, filename);
    }

    /**
     * 根据数据和模板生成Excel 并输出
     *
     * @param data     数据对象
     * @param template 模板文件
     * @param output   输出流
     * @return
     */

    public static void exportTemp(Object data, File template, OutputStream output) {
        Context context = PoiTransformer.createInitialContext();
        Map<String, Object> map = object2Map(data);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            context.putVar(entry.getKey(), entry.getValue());
        }
        try (InputStream input = new FileInputStream(template)) {
            JxlsHelperPlus.getInstance().processTemplate(input, output, context);
        } catch (IOException e) {
            log.error("导出Excel错误:{}", e.getMessage());
        }
    }


    /**
     * 根据数据和模板生成Excel 并输出
     *
     * @param data     数据对象
     * @param template 模板文件(适用于流的形式)
     * @param output   输出流
     * @return
     */

    public static void export(Object data, InputStream template, OutputStream output) {
        Context context = PoiTransformer.createInitialContext();
        Map<String, Object> map = object2Map(data);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            context.putVar(entry.getKey(), entry.getValue());
        }
        try {
            JxlsHelperPlus.getInstance().processTemplate(template, output, context);
        } catch (IOException e) {
            log.error("导出Excel错误:{}", e.getMessage());
        }
    }

    /**
     * Object转Map
     *
     * @param object
     * @return
     */

    private static Map<String, Object> object2Map(Object object) {
        Class<?> clazz = object.getClass();
        List<Field> fields = new ArrayList<>();
        for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
            Field[] array = clazz.getDeclaredFields();
            if (array != null && array.length > 0) {
                fields.addAll(Arrays.asList(array));
            }
        }
        Map<String, Object> oMap = new HashMap<>(fields.size());
        for (Field field : fields) {
            try {
                int modifier = field.getModifiers();
                if (Modifier.isStatic(modifier) && Modifier.isFinal(modifier)) {
                    continue;
                }
                String key = field.getName();
                PropertyDescriptor descr = new PropertyDescriptor(key,
                        object.getClass());
                Method read = descr.getReadMethod();
                if (read != null) {
                    oMap.put(key, read.invoke(object));
                }
            } catch (IntrospectionException | InvocationTargetException
                    | IllegalAccessException | IllegalArgumentException e) {
                log.error(e.getMessage());
            }
        }
        return oMap;
    }


    /**
     * 多sheet模板导出
     *
     * @param varMap   数组集合
     * @param template 模板
     * @param output   输出流
     */
    public static void exportExcel(Map<String, Object> varMap, File template, OutputStream output) {
        Context context = new Context(varMap);
        for (Map.Entry<String, Object> entry : varMap.entrySet()) {
            context.putVar(entry.getKey(), entry.getValue());
        }
        try (InputStream input = new FileInputStream(template)) {
            JxlsHelperPlus.getInstance().setDeleteTemplateSheet(true).processTemplate(input, output, context);
        } catch (IOException e) {
            log.error("导出Excel错误:{}", e.getMessage());
        }
    }

}


