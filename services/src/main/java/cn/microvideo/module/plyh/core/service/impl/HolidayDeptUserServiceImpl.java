package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.entity.Holiday;
import cn.microvideo.module.plyh.core.entity.HolidayDeptUser;
import cn.microvideo.module.plyh.core.mapper.HolidayDeptUserMapper;
import cn.microvideo.module.plyh.core.service.IHolidayDeptUserService;
import cn.microvideo.module.plyh.core.service.IHolidayService;
import cn.microvideo.module.plyh.core.vo.AddHolidayUserVO;
import cn.microvideo.module.plyh.core.vo.HolidayDeptUserDetailVO;
import cn.microvideo.module.plyh.core.vo.HolidayDeptUserVO;
import cn.microvideo.module.plyh.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 节假日、部门关系管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Service
public class HolidayDeptUserServiceImpl extends ServiceImpl<HolidayDeptUserMapper, HolidayDeptUser> implements IHolidayDeptUserService {

    @Resource
    private IHolidayService holidayService;

    /**
     * 根据日期ID和部门ID查询人员数据
     *
     * @param holidayIds
     * @param deptIds
     * @return
     */
    @Override
    public List<HolidayDeptUserDetailVO> selectByHolidayDeptIds(List<String> holidayIds, List<String> deptIds) {
        return baseMapper.selectByHolidayDeptIds(holidayIds, deptIds);
    }

    @Override
    public List<HolidayDeptUser> selectByHolidayIdDeptIds(String holidayId, List<String> deptIds) {
        LambdaQueryWrapper<HolidayDeptUser> query = new LambdaQueryWrapper<>();
        query.in(HolidayDeptUser::getHolidayId, holidayId);
        query.in(HolidayDeptUser::getDutyDeptId, deptIds);
        return this.list(query);
    }

    /**
     * 数据批量增增
     *
     * @param holidayDeptUser
     */
    @Override
    public void saveByBatchEntity(List<AddHolidayUserVO> holidayDeptUser) {
        try {
            if (CollUtil.isEmpty(holidayDeptUser)) {
                throw new BizException(CommonConstant.ERROR_500, "未找到排班信息！");
            }
            List<HolidayDeptUser> saveList = new ArrayList<>();
            //循环处理数据
            for (AddHolidayUserVO item : holidayDeptUser) {
                //判定数据
                if (CharSequenceUtil.isBlank(item.getHolidayId())) {
                    continue;
                }
                //人员数据信息
                if (CollUtil.isEmpty(item.getDeptUserList())) {
                    continue;
                }
                List<String> deptIds = item.getDeptUserList().stream().map(HolidayDeptUserVO::getDeptId).collect(Collectors.toList());
                //判定数据是否已经保存（先清除）
                deleteHolidayDeptUser(item.getHolidayId(), deptIds);
                //获取需要保存的数据
                saveList.addAll(selectSaveData(item.getHolidayId(), item.getDeptUserList()));
            }
            //批量保存数据
            if (CollUtil.isNotEmpty(saveList)) {
                this.saveBatch(saveList);
            }
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());

        }

    }

    /**
     * @param holidayIds
     * @param deptId
     * @return
     */
    @Override
    public List<HolidayDeptUserDetailVO> selectByHolidayIdsWithDept(String holidayIds, String deptId) {
        try {
            return baseMapper.selectByHolidayIdsWithDept(holidayIds, deptId);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     *删除数据
     * @param holidayIds
     */
    @Override
    public void deleteByHolidayIds(List<String> holidayIds) {
        LambdaQueryWrapper<HolidayDeptUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HolidayDeptUser::getHolidayId, holidayIds);
        this.remove(queryWrapper);
    }

    /**
     * 根据部门ID查询数据
     * @param deptId
     * @return
     */
    @Override
    public List<HolidayDeptUser> selectByDeptId(String deptId) {
        LambdaQueryWrapper<HolidayDeptUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HolidayDeptUser::getDutyDeptId, deptId);
        return this.list(queryWrapper);
    }

    /**
     *
     * @param holidayIds
     * @param deptId
     * @return
     */
    @Override
    public List<HolidayDeptUser> selectByHolidayDeptId(List<String> holidayIds, String deptId) {
        LambdaQueryWrapper<HolidayDeptUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HolidayDeptUser ::getHolidayId,holidayIds);
        queryWrapper.eq(HolidayDeptUser ::getDutyDeptId,deptId);
        return this.list(queryWrapper);
    }


    /**
     * 构造数据
     *
     * @param holidayId
     * @param deptUserList
     * @return
     */
    private List<HolidayDeptUser> selectSaveData(String holidayId, List<HolidayDeptUserVO> deptUserList) {
        try {
            List<HolidayDeptUser> list = new ArrayList<>();
            //获取日期
            Holiday holiday = holidayService.getById(holidayId);
            //计算年份和月份
            if (null == holiday) {
                throw new BizException(CommonConstant.ERROR_500, "未找到排班日期主体信息！");
            }
            //年份
            Integer year = null;
            //月份
            Integer month = null;
            if (null != holiday.getHolidayData()) {
                year = DateUtil.year(holiday.getHolidayData());
                month = DateUtil.month(holiday.getHolidayData());
            }
            //遍历循环获取数据
            for (HolidayDeptUserVO item : deptUserList) {
                if (CollUtil.isNotEmpty(item.getUserList())) {
                    //处理数据
                    for (HolidayDeptUser itemVo : item.getUserList()) {
                        //UUID
                        itemVo.setId(IdUtil.simpleUUID());
                        //部门ID
                        itemVo.setDutyDeptId(item.getDeptId());
                        //节假日ID
                        itemVo.setHolidayId(holidayId);
                        //年份
                        itemVo.setDutyYear(year);
                        //月份
                        itemVo.setDutyMonth(month);
                        //回显数据
                        list.add(itemVo);
                    }
                }
            }
            return list;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 先清除数据
     *
     * @param holidayId
     * @param deptIds
     */
    private void deleteHolidayDeptUser(String holidayId, List<String> deptIds) {
        LambdaQueryWrapper<HolidayDeptUser> query = new LambdaQueryWrapper<HolidayDeptUser>();
        query.eq(HolidayDeptUser::getHolidayId, holidayId);
        query.in(HolidayDeptUser::getDutyDeptId, deptIds);
        this.remove(query);
    }

}
