package cn.microvideo.module.plyh.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "AddHolidayUserVO对象", description = "数据新增实体")
public class AddHolidayUserVO {
    @ApiModelProperty(value = "日期ID")
    private String holidayId;
    @ApiModelProperty(value = "部门ID和人员数据")
    private List<HolidayDeptUserVO> deptUserList;
}
