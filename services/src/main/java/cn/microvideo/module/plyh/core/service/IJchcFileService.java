package cn.microvideo.module.plyh.core.service;

import cn.microvideo.module.plyh.core.entity.JchcFile;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * @Description: 文件管理表
 * @Author: spring-boot
 * @Date: 2023-08-31
 * @Version: V1.0
 */
public interface IJchcFileService extends IService<JchcFile> {

    /**
     * 保存数据
     *
     * @param jchcFile
     */
    public void saveByEntity(JchcFile jchcFile);


    /**
     * 更新数据
     *
     * @param jchcFile
     */
    public void updateByEntity(JchcFile jchcFile);

    /**
     * 上传附件
     *
     * @param origin
     * @return
     * @throws IOException
     */
    public JchcFile uploadDocView(MultipartFile origin, JchcFile jchcFile);

    /**
     * 下载文件
     *
     * @param id
     * @param response
     */
    public void download(String id, HttpServletResponse response);

    /**
     * 根据Code下载文件
     * @param fileCode
     * @param response
     */

    public void downloadByCode(String fileCode, HttpServletResponse response);
    /**
     * 实体章授权拍照上传
     *
     * @param origin
     * @param file
     * @param session
     * @return
     */
    public JchcFile uploadForAuthEntitySignet(MultipartFile origin, JchcFile file, HttpSession session);


    /**
     * 根据formId查询数据
     *
     * @param id
     * @return
     */
    public List<JchcFile> selectByFormId(String id);

    /**
     * 上传附件
     *
     * @param origin
     * @return
     * @throws IOException
     */
    public JchcFile uploadFile(MultipartFile origin, JchcFile jchcFile);




}
