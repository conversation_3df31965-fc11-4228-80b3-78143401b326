package cn.microvideo.module.plyh.core.vo;

import lombok.*;

/**
 * <b>描述:</b>
 *
 * <p>QS节点数据</p>
 *
 * @version 1.0
 * <AUTHOR>
 * @Date 2019年9月25日下午4:20:51
 * @since JDK1.8
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class QsNode {

	/**
	 * ID
	 */
	private String id;
	
	/**
	 * 名称
	 */
	private String name;
	
	/**
	 * 编码
	 */
	private String code;
	
	/**
	 * 全称
	 */
	private String fullName;
	/**
	 * 父节点ID
	 */
	private String parentId;
	/**
	 * 是否父节点
	 */
	private boolean parent;
	/**
	 * 排序号
	 */
	private int orderNo;
	/**
	 * 组织类型
	 */
	private String type;
	
	/**
	 * 单位类型标识
	 */
	private String categoryId;
	/**
	 * 单位类型名称
	 */
	private String categoryName;
	/**
	 * 单位简称
	 */
	private String extendName;
}
