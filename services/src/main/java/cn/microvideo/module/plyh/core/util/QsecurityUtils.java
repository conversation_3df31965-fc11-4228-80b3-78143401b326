package cn.microvideo.module.plyh.core.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.module.plyh.core.vo.Nav;
import cn.microvideo.module.plyh.core.vo.QsNode;
import cn.microvideo.module.plyh.core.vo.ZtreeNode;
import cn.microvideo.qsc.client.api.QsApi;
import cn.microvideo.qsc.client.entity.ModuleInfo;
import cn.microvideo.qsc.client.entity.OrganizationInfo;
import cn.microvideo.qsc.client.entity.Template;
import cn.microvideo.qsc.client.entity.User;
import cn.microvideo.qsc.client.entity.xml.Organizations;
import cn.microvideo.qsc.client.entity.xml.ParentUsers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>描述:</b>
 *
 * <p>QS工具类</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2019年4月10日下午6:39:10
 * @since JDK1.8
 */
@Slf4j
@Component
public final class QsecurityUtils {
    /**
     * 应用类型
     */
    public static final String TYPE_APP = "A";
    /**
     * 模块类型
     */
    public static final String TYPE_MOD = "M";
    /**
     * 模块类别 信息
     */
    public static final String CLASSIFY_INFO = "info";
    /**
     * 模块类别 信息
     */
    public static final String CLASSIFY_WORK = "work";
    /**
     * 虚拟机构
     */
    public static final String QS_TYPE_VO = "vo";
    /**
     * 部门
     */
    public static final String QS_TYPE_OU = "ou";
    /**
     * 单位
     */
    public static final String QS_TYPE_O = "o";
    /**
     * QS组织根节点ID
     */
    public static final String QS_ROOT_ID = "qs00001";
    /**
     * 开发单位编码
     */
    public static final String DEV_UNIT_CODE = "100115";
    /**
     * 外部单位编码
     */
    public static final String OUTSIDE_UNIT_CODE = "1007";
    /**
     * 外部单位ID
     */
    public static final String OUTSIDE_UNIT_ID = "ff808181609fee0e01613067082701b0";

    /**
     * 本部公司
     */
    public static final String QS_KGBB_ID = "ArgQgB5u2viP4IcHHhaNl9x7PCrcvK65";

    /**
     * 门户任务服务
     */
//    private static TaskService taskService;

    /**
     * 注入门户任务服务对象
     *
     * @param taskService the taskService to set
     */
//    @Autowired
//    public void setTaskService(TaskService taskService) {
//        QsecurityUtils.taskService = taskService;
//    }


    /**
     * 单位组织架构树
     *
     * @param id 上级单位标识
     * @return
     */
    public static List<ZtreeNode> zTreeNodes(String id) {
        return listChildNodes(id).stream()
                .sorted((a, b) -> a.getOrderNo() - b.getOrderNo())
                .map(sub -> {
                    String name = sub.getName();
                    ZtreeNode zNode = new ZtreeNode();
                    zNode.setId(sub.getId());
                    zNode.setName(name);
                    zNode.setExtendName(sub.getExtendName());
                    //公司本部可点击
                    boolean home = name.contains("本部");
                    if (!home && QS_TYPE_OU.equals(sub.getType())) {
                        zNode.setParent(false);
                        zNode.setNocheck(false);
                    }
                    zNode.setParentId(id);
                    zNode.putExtAttr("type", sub.getType());
                    return zNode;
                }).collect(Collectors.toList());
    }

    /**
     * 根据人名获取数据
     *
     * @param userName
     * @return
     */
    public static List<ZtreeNode> selectByUserName(String userName) {
        List<ZtreeNode> nodeList = new ArrayList<>();
        ParentUsers userList = QsApi.getApi().findPersonsByName(userName);
        for (User user : userList.getUsers().getUser()) {
            ZtreeNode userNode = new ZtreeNode(user.getUuid(), user.getName());
            if (CollUtil.isNotEmpty(user.getOrgList())) {
                userNode.setParentId(user.getOrgList().get(0).getOuid());
                userNode.putExtAttr("deptId", user.getOrgList().get(0).getOuid());
                userNode.putExtAttr("deptName", user.getOrgList().get(0).getOuname());
                userNode.putExtAttr("groupId", user.getOrgList().get(0).getOid());
                userNode.putExtAttr("groupName", user.getOrgList().get(0).getOname());
                //获取信息
                OrganizationInfo organ = QsApi.getApi().findOrganizationById(user.getOrgList().get(0).getOuid()).getOrganizationInfo();
                userNode.putExtAttr("extendName", organ.getExtendName());
            }
            userNode.setNocheck(false);
            userNode.setParent(false);
            userNode.putExtAttr("account", user.getUid());
            userNode.putExtAttr("phone", user.getMobile());


            nodeList.add(userNode);
        }
        return nodeList;
    }

    /**
     * 获取QS组织架构树（如果是公司本部则找本部人员，避免人员组织树错区）
     *
     * @param id 节点ID
     * @return
     */
    public static List<ZtreeNode> zTreeQSecurityByGroupId(String id) {
        if(CharSequenceUtil.equals(QS_ROOT_ID,id)){
            //获取当前单位人员
            id = QS_KGBB_ID;
        }
        List<ZtreeNode> nodeList = new ArrayList<>();
        Organizations organizations = QsApi.getApi().findOrganizationsByPid(id).getOrganizationinfos();

        if (null!=organizations) {
            List<OrganizationInfo> deptList = organizations.getOrganizationInfoList();
            if (CollUtil.isNotEmpty(deptList)) {
                for (OrganizationInfo dept : deptList) {
                    //去除外部单位 开发单位
                    String code = dept.getoCode();
                    if (code.startsWith(OUTSIDE_UNIT_CODE)
                        //|| code.startsWith(DEV_UNIT_CODE)
                    ) {
                        continue;
                    }
                    ZtreeNode deptNode = new ZtreeNode(dept.getId(), dept.getName());
                    deptNode.setParentId(id);
                    deptNode.putExtAttr("type", dept.getType());
                    nodeList.add(deptNode);
                }
            }
        }
        OrganizationInfo organ = QsApi.getApi().findOrganizationById(id).getOrganizationInfo();
        if (QS_TYPE_OU.equals(organ.getType()) || nodeList.isEmpty()) {
            List<User> userList = QsApi.getApi().getPersonsByDid(id);
            for (User user : userList) {
                ZtreeNode userNode = new ZtreeNode(user.getUuid(), user.getName());
                userNode.setParentId(id);
                userNode.setNocheck(false);
                userNode.setParent(false);
                userNode.putExtAttr("account", user.getUid());
                userNode.putExtAttr("deptName", organ.getName());
                userNode.putExtAttr("phone", user.getMobile());
                userNode.putExtAttr("deptId", organ.getId());
                userNode.putExtAttr("extendName", organ.getExtendName());
                if (CollUtil.isNotEmpty(user.getOrgList())) {
                    userNode.putExtAttr("groupId", user.getOrgList().get(0).getOid());
                    userNode.putExtAttr("groupName", user.getOrgList().get(0).getOname());
                } else {
                    userNode.putExtAttr("groupId", "");
                    userNode.putExtAttr("groupName", "");
                }

                nodeList.add(userNode);
            }
        }
        return nodeList;
    }




    /**
     * 获取QS组织架构树
     *
     * @param id 节点ID
     * @return
     */
    public static List<ZtreeNode> zTreeQSecurity(String id) {
        List<ZtreeNode> nodeList = new ArrayList<>();
        Organizations organizations = QsApi.getApi().findOrganizationsByPid(id).getOrganizationinfos();

        if (organizations != null) {
            List<OrganizationInfo> deptList = organizations.getOrganizationInfoList();
            if (deptList != null) {
                for (OrganizationInfo dept : deptList) {
                    //去除外部单位 开发单位
                    String code = dept.getoCode();
                    if (code.startsWith(OUTSIDE_UNIT_CODE)
                        //|| code.startsWith(DEV_UNIT_CODE)
                    ) {
                        continue;
                    }
                    ZtreeNode deptNode = new ZtreeNode(dept.getId(), dept.getName());
                    deptNode.setParentId(id);
                    deptNode.putExtAttr("type", dept.getType());
                    nodeList.add(deptNode);
                }
            }
        }
        OrganizationInfo organ = QsApi.getApi().findOrganizationById(id).getOrganizationInfo();
        if (QS_TYPE_OU.equals(organ.getType()) || nodeList.isEmpty()) {
            List<User> userList = QsApi.getApi().getPersonsByDid(id);
            for (User user : userList) {
                ZtreeNode userNode = new ZtreeNode(user.getUuid(), user.getName());
                userNode.setParentId(id);
                userNode.setNocheck(false);
                userNode.setParent(false);
                userNode.putExtAttr("account", user.getUid());
                userNode.putExtAttr("deptName", organ.getName());
                userNode.putExtAttr("phone", user.getMobile());
                userNode.putExtAttr("deptId", organ.getId());
                userNode.putExtAttr("extendName", organ.getExtendName());
                if (CollUtil.isNotEmpty(user.getOrgList())) {
                    userNode.putExtAttr("groupId", user.getOrgList().get(0).getOid());
                    userNode.putExtAttr("groupName", user.getOrgList().get(0).getOname());
                } else {
                    userNode.putExtAttr("groupId", "");
                    userNode.putExtAttr("groupName", "");
                }

                nodeList.add(userNode);
            }
        }
        return nodeList;
    }

    /**
     * 获取下级单位
     *
     * @param parentId 父单位ID
     * @return
     */
    public static List<QsNode> listChildNodes(String parentId) {
        List<QsNode> nodes = new ArrayList<>(32);
        listChildNodes(parentId, nodes);
        return nodes;
    }

    /**
     * 获取下级单位
     *
     * @param parentId 父单位ID
     * @param type     节点类型 ou|o|""
     * @return
     */
    public static List<QsNode> listChildNodes(String parentId, String type) {
        List<QsNode> nodes = new ArrayList<>(32);
        listChildNodes(parentId, type, nodes);
        return nodes;
    }

    /**
     * 获取子单位，穿透虚拟机构
     *
     * @param parentId 父单位ID
     * @param nodeList 子节点集合
     */
    public static void listChildNodes(String parentId, List<QsNode> nodeList) {
        listChildNodes(parentId, "", nodeList);
    }

    /**
     * 获取子单位，穿透虚拟机构
     *
     * @param parentId 父单位ID
     * @param type     节点类型 ou|o|""
     * @param nodeList 子节点集合
     */
    public static void listChildNodes(String parentId, String type, List<QsNode> nodeList) {
        //过滤掉外部单位
        if (OUTSIDE_UNIT_ID.equals(parentId)) {
            return;
        }
        //查询单位信息
        List<OrganizationInfo> children = QsApi.getApi().findOrgListByParentId(parentId, type, "false", "false");
        if (children == null || children.isEmpty()) {
            return;
        }
        for (OrganizationInfo child : children) {
            String code = child.getoCode();
            //去除外部单位 开发单位
            if (code.startsWith(OUTSIDE_UNIT_CODE)
                    || code.startsWith(DEV_UNIT_CODE)) {
                continue;
            }
            if (QS_TYPE_O.equals(child.getType())) {
                QsNode node = new QsNode();
                String name = child.getName();
                String shortName = child.getShortNameExt1();
                if (StringUtils.isBlank(shortName)) {
                    shortName = name;
                }
                node.setId(child.getId());
                node.setName(shortName);
                node.setFullName(name);
                node.setExtendName(child.getExtendName());
                node.setCode(code);
                node.setType(QS_TYPE_O);
                node.setParent(true);
                Template template = child.getTp();
                if (template != null) {
                    node.setCategoryId(template.getId());
                    node.setCategoryName(template.getName());
                }
                node.setOrderNo(Integer.parseInt(child.getSequence()));
                nodeList.add(node);
            } else if (QS_TYPE_VO.equals(child.getType())) {
                listChildNodes(child.getId(), type, nodeList);
            } else if (QS_TYPE_OU.equals(child.getType())) {
                QsNode node = new QsNode();
                String name = child.getName();
                String shortName = child.getShortNameExt1();
                if (StringUtils.isBlank(shortName)) {
                    shortName = name;
                }
                node.setId(child.getId());
                node.setName(shortName);
                node.setFullName(name);
                node.setExtendName(child.getExtendName());
                node.setCode(code);
                node.setType(QS_TYPE_OU);
                node.setParent(false);
                node.setOrderNo(Integer.parseInt(child.getSequence()));
                nodeList.add(node);
            } else {
                continue;
            }
        }
    }

    /**
     * 根据节点ID获取上级单位
     *
     * @param id
     * @return
     */
    public static QsNode getParentNodeGroup(String id) {
        QsNode node = getNode(id);
        return getParentNodeGroup(node);
    }

    /**
     * 获取上级单位，不包含虚拟机构/部门
     *
     * @param node 当前节点
     * @return 上级节点
     */
    public static QsNode getParentNodeGroup(QsNode node) {
        Objects.requireNonNull(node);
        String parentId = node.getParentId();
        String nulls = "null";
        if (StringUtils.isBlank(parentId) || nulls.equals(parentId)) {
            return null;
        }
        QsNode parent = getNode(parentId);
        if (QS_TYPE_O.equals(parent.getType())) {
            return parent;
        } else {
            return getParentNodeGroup(parent);
        }
    }

    /**
     * 获取节点路径，不包含顶层单位
     *
     * @param id     节点ID
     * @param path   初始路径名
     * @param simple 是否使用简称
     * @return
     */
    public static String getNodePath(String id, String path, boolean simple) {
        QsNode node = getNode(id);
        QsNode parentNode = getParentNodeGroup(node);
        if (parentNode == null || QS_ROOT_ID.equals(parentNode.getId())) {
            return path;
        } else {
            String name = simple ? parentNode.getName() : parentNode.getFullName();
            return getNodePath(parentNode.getId(), name.concat("/").concat(path), simple);
        }
    }

    /**
     * 获取QS中的节点(单位/部门)
     *
     * @param id 节点标识
     * @return
     */
    public static QsNode getNode(String id) {
        Objects.requireNonNull(id);
        OrganizationInfo node = QsApi.getApi().findOrganizationById(id).getOrganizationInfo();
        if (node == null) {
            throw new IllegalArgumentException(String.format("参数错误，找不到节点信息:%s", id));
        }
        String name = node.getName();
        String shortName = node.getShortNameExt1();
        if (StringUtils.isBlank(shortName)) {
            shortName = name;
        }
        return QsNode.builder().id(node.getId())
                .name(shortName)
                .fullName(name)
                .code(node.getoCode())
                .type(node.getType())
                .parentId(node.getParent())
                .build();
    }

    /**
     * 获取一级单位
     *
     * @param id 节点ID
     * @return
     */
    public static QsNode getOneNode(String id) {
        Objects.requireNonNull(id);
        OrganizationInfo node = QsApi.getApi().findOrganizationById(id).getOrganizationInfo();
        if (node == null) {
            throw new IllegalArgumentException(String.format("参数错误，找不到节点信息:%s", id));
        }
        //一级单位编码长度
        int oneLen = 6;
        String code = node.getoCode();
        if (code != null && code.length() > oneLen) {
            node = QsApi.getApi().findOrgByOCode(code.substring(0, 6));
        }
        String name = node.getName();
        String shortName = node.getShortNameExt1();
        if (StringUtils.isBlank(shortName)) {
            shortName = name;
        }
        return QsNode.builder().id(node.getId())
                .name(shortName)
                .fullName(name)
                .code(node.getoCode())
                .type(node.getType())
                .parentId(node.getParent())
                .build();
    }

    /**
     * 获取子单位
     *
     * @param parentId 父单位标识
     * @param isAll    是否包含子孙单位
     * @return
     */
    public static List<QsNode> listSubGroup(String parentId, boolean isAll) {
        return listSubGroup(parentId, "o", isAll);
    }

    /**
     * 获取子单位，不包含虚拟机构
     *
     * @param parentId 父单位标识
     * @param type     节点类型
     * @param isAll    是否包含子孙单位
     * @return
     */
    public static List<QsNode> listSubGroup(String parentId, String type, boolean isAll) {
        List<OrganizationInfo> list = QsApi.getApi().findOrgListByParentId(parentId, type, String.valueOf(isAll), String.valueOf(false));
        return list.stream()
                .filter(p -> !p.getoCode().startsWith(OUTSIDE_UNIT_CODE))
                .sorted((a, b) -> Integer.parseInt(a.getSequence()) - Integer.parseInt(b.getSequence())).map(node -> {
                    String name = node.getName();
                    String shortName = node.getShortNameExt1();
                    if (StringUtils.isBlank(shortName)) {
                        shortName = name;
                    }
                    return QsNode.builder().id(node.getId())
                            .name(shortName)
                            .fullName(name)
                            .code(node.getoCode())
                            .type(node.getType())
                            .parentId(node.getParent())
                            .build();
                }).collect(Collectors.toList());
    }

    /**
     * -获取不包含自己的部门人员zTree树节点
     *
     * @param orgId  单位ID
     * @param deptId 部门ID
     * @param userId 人员ID
     * @return 不包含自己的部门人员树节点
     */
    public static List<ZtreeNode> zTreeDeptUsersNoSelf(String orgId, String deptId, String userId) {
        List<User> users = QsApi.getApi().getPersonsByDid(deptId);
        return users.stream().filter(p -> !p.getUuid().equals(userId)).map(u -> {
            ZtreeNode node = new ZtreeNode(u.getUuid(), u.getName());
            node.setNocheck(false);
            node.setParent(false);
            node.setParentId("-1");
            node.putExtAttr("groupId", orgId);
            return node;
        }).collect(Collectors.toList());
    }

    /**
     * 获取与当前用户的部门同级的部门
     *
     * @param user 当前用户
     * @return 同级部门
     */
    public static List<ZtreeNode> zTreeDepts(MicrovideoSessionUser user) {
        String deptId = user.getDeptId();
        QsNode node = getNode(deptId);
        return listChildNodes(node.getParentId(), QS_TYPE_OU).stream().map(n -> {
            ZtreeNode zNode = new ZtreeNode(n.getId(), n.getName());
            zNode.setNocheck(false);
            zNode.setParent(false);
            zNode.setParentId("-1");
            return zNode;
        }).collect(Collectors.toList());
    }

    /**
     * 获取人员权限代码集合
     *
     * @param user 人员对象
     * @return 权限代码集合
     */
    public static Set<String> getAuths(MicrovideoSessionUser user) {
        List<String> auths = QsApi.getApi().authentication(user.getAccount(), user.getDeptId());
        return new HashSet<>(auths);
    }

    /**
     * 判断人员是否拥有某些权限
     *
     * @param auths 权限集合
     * @param user  人员对象
     * @return 鉴权结果
     */
    public static Map<String, Boolean> hasAuths(List<String> auths, MicrovideoSessionUser user) {
        if (auths == null || auths.isEmpty()) {
            return Collections.emptyMap();
        }
        Set<String> list = getAuths(user);
        Map<String, Boolean> map = new HashMap<>();
        for (String auth : auths) {
            map.put(auth, list.contains(auth));
        }
        return map;
    }

    /**
     * 转换为导航对象
     *
     * @param module   模块对象
     * @param userList 用户模块集合
     * @return 导航
     */
    private static Nav toNav(ModuleInfo module, List<ModuleInfo> userList) {
        Nav nav = new Nav();
        nav.setId(module.getId());
        nav.setCode(module.getCode());
        nav.setName(module.getName());
        nav.setIcon(module.getImageUrl1());
        nav.setIcon2(module.getImageUrl2());
        nav.setSort(module.getDesktopSeq());
        nav.setClassify(module.getClassify());
        nav.setUrl(module.getUrl());
        //用户有模块并且传进来的模块在里面，那就是有权限
        nav.setHas(userList != null && userList.stream().anyMatch(pre -> nav.getCode().equalsIgnoreCase(pre.getCode())));
        return nav;
    }

    /**
     * 获取用户导航菜单
     *
     * @param user 会话用户
     * @return 菜单集合
     */
    public static List<Nav> listUserNavs(MicrovideoSessionUser user) {
        //获取所有模块
        List<ModuleInfo> allList = QsApi.getApi().findAllModuleOnlyDesktop("sxjt");
        //获取用户模块
        List<ModuleInfo> userList = QsApi.getApi().findAllModuleByUidOnlyDesktop(user.getAccount(), "all", user.getDeptId());

        //获取用户待办数字
//        List<TaskCount> taskList = taskService.listTaskCount(user);
//        Map<String, Integer> countMap = taskList.stream().collect(Collectors.toMap(key ->
//                String.format("%s-%s", key.getApp(), key.getModule()), TaskCount::getCount));
        List<Nav> navs = new ArrayList<>();
        //转换为应用/模块两层结构
        //所有的
        for (ModuleInfo m : allList) {
            //非应用
            if (!TYPE_APP.equalsIgnoreCase(m.getType())) {
                continue;
            }
            //转成用户权限的导航模块
            Nav nav = toNav(m, userList);
            //所有的
            for (ModuleInfo c : allList) {
                //筛选出work模块
                if (nav.getId().equals(c.getMaster()) &&
                        CLASSIFY_WORK.equals(c.getClassify())) {
                    Nav child = toNav(c, userList);
                    String key = String.format("%s-%s", nav.getId(), c.getCode());
//                    child.setCount(countMap.getOrDefault(key, 0));
                    nav.addChild(child);
                } else {
                    continue;
                }
            }
            if (!nav.getChildren().isEmpty()) {
                navs.add(nav);
            }
        }

        return navs;
    }

    /**
     * 获取用户导航菜单
     *
     * @param user 会话用户
     * @return 菜单集合
     */
    public static List<Nav> listUserNavs4v(MicrovideoSessionUser user) {
        //获取所有模块
        List<ModuleInfo> allList = QsApi.getApi().findAllModuleOnlyDesktop("sxjt");
        //获取用户模块
        List<ModuleInfo> userList = QsApi.getApi().findAllModuleByUidOnlyDesktop(user.getAccount(), "all", user.getDeptId());
        //获取用户待办数字
//        List<TaskCount> taskList = taskService.listTaskCount(user);
//        if (!taskList.isEmpty()) {
//            //构造map，key是app-moudle,value是任务count
//            Map<String, Integer> countMap = taskList.stream().collect(Collectors.toMap(key ->
//                    String.format("%s-%s", key.getApp(), key.getModule()), TaskCount::getCount));
//        }
        List<Nav> navs = new ArrayList<>();
        for (ModuleInfo c : allList) {
            if (CLASSIFY_INFO.equals(c.getClassify())) {
                Nav navs4v = toNav(c, userList);
                if (navs4v.isHas()) {
                    navs.add(navs4v);
                }
            }
        }
        return navs;

    }

    /**
     * 获取登录人员密码
     *
     * @param user 人员对象
     * @return 权限代码集合
     */
    public static String getUserPass(MicrovideoSessionUser user) {
        cn.microvideo.qsc.client.entity.xml.User qsUser = QsApi.getApi().getPersonByUid(user.getAccount());
        return qsUser.getPassWord();
    }


}
