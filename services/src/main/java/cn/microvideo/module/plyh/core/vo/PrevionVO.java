package cn.microvideo.module.plyh.core.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class PrevionVO {
    private String previousAssigneeId;

    private String previousAssigneeName;

    private String newAssigneeId;

    private String newAssigneeName;

    private String opinion;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private String procId;

    private String taskId;
    protected String bizKey;

    private String executionId;

    private String procDefId;

    private String type;

    private String typeId;

}
