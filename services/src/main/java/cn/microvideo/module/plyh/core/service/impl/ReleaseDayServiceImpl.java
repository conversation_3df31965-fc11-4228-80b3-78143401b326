package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.entity.Holiday;
import cn.microvideo.module.plyh.core.entity.ReleaseDay;
import cn.microvideo.module.plyh.core.mapper.ReleaseDayMapper;
import cn.microvideo.module.plyh.core.service.IReleaseDayService;
import cn.microvideo.module.plyh.core.vo.ReleaseHolidayVO;
import cn.microvideo.module.plyh.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 发布记录日期关联表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Service
public class ReleaseDayServiceImpl extends ServiceImpl<ReleaseDayMapper, ReleaseDay> implements IReleaseDayService {

    @Override
    public void addByHoliday(String id, String holidayIds) {
        try {
            List<ReleaseDay> releaseDays = new ArrayList<>();
            String holidayId[] = holidayIds.split(",");
            if (null != holidayId) {
                ReleaseDay releaseDay = null;
                for (String item : holidayId) {
                    releaseDay = new ReleaseDay();
                    //UUID
                    releaseDay.setId(IdUtil.simpleUUID());
                    //节假日ID
                    releaseDay.setHolidayId(item);
                    //发布Id
                    releaseDay.setReleaseId(id);
                    //新增数据
                    releaseDays.add(releaseDay);
                }
            }
            //保存数据
            if (CollUtil.isNotEmpty(releaseDays)) {
                this.saveBatch(releaseDays);
            }

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 获取数据关系列表
     * @param ids
     * @return
     */
    @Override
    public List<ReleaseHolidayVO> selectByReleaseIdsHoliday(List<String> ids) {
        return baseMapper.selectByReleaseIdsHoliday(ids);
    }

    /**
     * 根据formId删除数据
     *
     * @param formId
     */
    @Override
    public void removeByFormId(String formId) {
        LambdaQueryWrapper<ReleaseDay> query = new LambdaQueryWrapper<>();
        query.eq(ReleaseDay ::getReleaseId,formId);
        this.remove(query);
    }

    @Override
    public List<Holiday> selectByReleaseId(String id) {
        return baseMapper.selectByReleaseId(id);
    }

    /**
     *
     * @param holidayIds
     * @return
     */
    @Override
    public List<ReleaseDay> selectByHolidayIds(List<String> holidayIds) {
        LambdaQueryWrapper<ReleaseDay> query = new LambdaQueryWrapper<>();
        query.in(ReleaseDay ::getHolidayId,holidayIds);
        return this.list(query);
    }
}
