package cn.microvideo.module.plyh.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 值班表查询返回VO

 * @since 2025-01-16
 */
@Data
@ApiModel(value = "DutyRosterVO对象", description = "值班表查询返回VO")
public class DutyRosterVO {

    /**
     * 值班表ID
     */
    @ApiModelProperty(value = "值班表ID")
    private String id;

    /**
     * 值班表标题
     */
    @ApiModelProperty(value = "值班表标题")
    private String title;

    /**
     * 所属组织ID
     */
    @ApiModelProperty(value = "所属组织ID")
    private String orgId;

    /**
     * 所属组织名称
     */
    @ApiModelProperty(value = "所属组织名称")
    private String orgName;

    /**
     * 排班时间字符串（用于接收SQL查询结果）
     */
    private String scheduleTimesStr;

    /**
     * 排班时间列表
     */
    @ApiModelProperty(value = "排班时间列表")
    private List<LocalDate> scheduleTimes;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;

    /**
     * 获取排班时间列表
     * 如果 scheduleTimes 为空，则从 scheduleTimesStr 解析
     */
    public List<LocalDate> getScheduleTimes() {
        if (scheduleTimes == null && scheduleTimesStr != null && !scheduleTimesStr.trim().isEmpty()) {
            scheduleTimes = Arrays.stream(scheduleTimesStr.split(","))
                    .filter(s -> s != null && !s.trim().isEmpty() && !"null".equals(s.trim()))
                    .map(s -> LocalDate.parse(s.trim()))
                    .collect(Collectors.toList());
        }
        return scheduleTimes != null ? scheduleTimes : new ArrayList<>();
    }
}
