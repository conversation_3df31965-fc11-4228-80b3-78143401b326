package cn.microvideo.module.plyh.core.service;

import cn.microvideo.module.plyh.core.entity.DutyRoster;
import cn.microvideo.module.plyh.core.vo.DutyRosterAddRequest;
import cn.microvideo.module.plyh.core.vo.DutyRosterEditRequest;
import cn.microvideo.module.plyh.core.vo.DutyRosterVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 值班标题主表 服务类
 *

 * @since 2025-06-11
 */
public interface IDutyRosterService extends IService<DutyRoster> {

    /**
     * 按月份分页查询值班表及排班时间
     *
     * @param page  分页参数
     * @param month 查询月份 (格式: yyyy-MM)
     * @return 分页结果
     */
    IPage<DutyRosterVO> queryPageListByMonth(Page<DutyRosterVO> page, String month);

    /**
     * 添加值班表及排班时间
     *
     * @param request 添加请求对象
     * @return 值班表实体
     */
    DutyRoster addDutyRosterWithSchedules(DutyRosterAddRequest request);

    /**
     * 编辑值班表及排班时间
     *
     * @param request 编辑请求对象
     * @return 值班表实体
     */
    DutyRoster editDutyRosterWithSchedules(DutyRosterEditRequest request);


    /**
     * 删除值班表及排班时间
     *
     * @param idList 编辑请求对象
     */

    void removeBatch(List<String> idList);




    /**
     * 查询值班表及排班时间
     *
     * @param id  主键ID
     */
    DutyRosterVO info(String id);

}

