package cn.microvideo.module.plyh.core.vo;

import cn.microvideo.module.plyh.core.entity.DeptUser;
import cn.microvideo.module.plyh.core.entity.HolidayDeptUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class HolidayDayVO {
    @ApiModelProperty(value = "部门ID")
    private String deptId;
    @ApiModelProperty(value = "部门名称")
    private String deptName;
    @ApiModelProperty(value = "部门列表")
    private List<HolidayDeptUserDetailVO> deptUserList;
}
