package cn.microvideo.module.plyh.core.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.vo.AddBatchHolidayVO;
import cn.microvideo.module.plyh.core.vo.NoticePublicity;
import cn.microvideo.module.plyh.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MultiValuedMap;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class FileHttpUtils {
    public static int BYTE_LEN = 10240;

    @Resource
    private RestTemplate restTemplate;

    /**
     * 根据Header 请求并返回具体内容
     *
     * @param url     请求路径
     * @param headers head的参数
     * @return 返回结果
     */
    public String getSendHeaderBody(String url, HttpHeaders headers) {
        try {
            // 将请求头部和参数合成一个请求
            HttpEntity<MultiValuedMap<String, Object>> httpEntity = new HttpEntity<>(headers);
            // 发送get请求
            ResponseEntity<String> getForEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);

            if (getForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, getForEntity.toString());
            }
            return getForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 根据header 和param 查询接口并返回具体内容
     *
     * @param url     访问路径
     * @param headers header 数据
     * @param params  param数据
     * @return 返回结果
     */
    public String getSendHeaderParamBody(String url, HttpHeaders headers, Map<String, Object> params) {
        try {
            url = formatUrl(url, params);
            // 将请求头部和参数合成一个请求
            HttpEntity<MultiValuedMap<String, String>> httpEntity = new HttpEntity<>(headers);
            // 发送get请求
            ResponseEntity<String> getForEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);

            if (getForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, getForEntity.toString());
            }
            return getForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * get请求并返回 body 内容
     *
     * @param url    请求地址
     * @param params param参数
     * @return 返回结果
     */
    public String getSendParamBody(String url, Map<String, Object> params) {
        try {
            url = formatUrl(url, params);
            // 发送get请求
            ResponseEntity<String> getForEntity = restTemplate.getForEntity(url, String.class, params);
            if (getForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, getForEntity.toString());
            }
            return getForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * psot请求并返回内容
     * 根据 body 查询数据
     *
     * @param url     访问路径
     * @return
     */
    public String postSendBodyAPPList(String url, List<NoticePublicity> params) {
        try {
            // 发送get请求
            HttpHeaders headers = new HttpHeaders();
            // 设置请求类型
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 将请求头部和参数合成一个请求
            HttpEntity<List<NoticePublicity>> httpEntity = new HttpEntity<>(params, headers);
            // 发送post请求
            ResponseEntity<String> postForEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            if (postForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, postForEntity.toString());
            }
            return postForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * psot请求并返回内容
     * 根据 body 查询数据
     *
     * @param url     访问路径
     * @return
     */
    public String postSendBodyAttList(String url, AddBatchHolidayVO addBatchHolidayVO) {
        try {
            // 发送get请求
            HttpHeaders headers = new HttpHeaders();
            // 设置请求类型
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 将请求头部和参数合成一个请求
            HttpEntity<AddBatchHolidayVO> httpEntity = new HttpEntity<>(addBatchHolidayVO, headers);
            // 发送post请求
            ResponseEntity<String> postForEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            if (postForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, postForEntity.toString());
            }
            return postForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * get不带任何参数的请求
     *
     * @param url
     * @return
     */
    public String getSendBody(String url) {
        try {
            // 发送get请求
            ResponseEntity<String> getForEntity = restTemplate.getForEntity(url, String.class);
            if (getForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, getForEntity.toString());
            }
            return getForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * get请求并返回 内容
     *
     * @param url    请求地址
     * @param params param参数
     * @return 返回结果
     */
    public ResponseEntity<String> getSendParamRes(String url, Map<String, Object> params) {
        try {
            url = formatUrl(url, params);
            // 发送get请求
            ResponseEntity<String> getForEntity = restTemplate.getForEntity(url, String.class, params);
            return getForEntity;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 格式化get请求地址
     *
     * @param url
     * @param params
     */
    public String formatUrl(String url, Map<String, Object> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        params.entrySet().stream().forEach(o -> builder.queryParam(o.getKey(), o.getValue()));
        return builder.build().encode().toString();
    }

    /**
     * psot请求并返回内容
     * 根据 body 查询数据
     *
     * @param url     访问路径
     * @param bodyMap body 传参
     * @return
     */
    public String postSendBody(String url, HashMap<String, Object> bodyMap) {
        try {
            // 发送get请求
            HttpHeaders headers = new HttpHeaders();
            // 设置请求类型
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 将请求头部和参数合成一个请求
            HttpEntity<HashMap<String, Object>> httpEntity = new HttpEntity<>(bodyMap, headers);
            // 发送post请求
            ResponseEntity<String> postForEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            if (postForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, postForEntity.toString());
            }
            return postForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * post请求raw传参
     *
     * @param url
     * @param headers
     * @param jsonObject
     * @return
     */
    public String postSendHeaderBodyRaw(String url, HttpHeaders headers, JSONObject jsonObject) {
        try {
            // 发送post请求
            JSONObject json = restTemplate
                    .postForObject(url, new HttpEntity<>(jsonObject, headers), JSONObject.class);

            log.info(json.toString());
            return json.toString();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }


    /**
     * psot请求并返回内容
     *
     * @param url     访问路径
     * @param headers header 内容
     * @param bodyMap body内容
     * @return 返回结果
     */
    public String postSendHeaderBody(String url, HttpHeaders headers, MultiValueMap<String, Object> bodyMap) {
        try {
            // 将请求头部和参数合成一个请求
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(bodyMap, headers);
            // 发送post请求
            ResponseEntity<String> postForEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            if (postForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, postForEntity.toString());
            }
            return postForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * post根据header内容获取结果
     * 根据header获取数据
     *
     * @param url     访问路径
     * @param headers header内容
     * @return 返回结果
     */
    public String postSendHeader(String url, HttpHeaders headers) {
        try {
            // 将请求头部和参数合成一个请求
            HttpEntity<HashMap<String, Object>> httpEntity = new HttpEntity<>(headers);
            // 发送post请求
            ResponseEntity<String> postForEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            if (postForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, postForEntity.toString());
            }
            return postForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * post上传单个文件
     *
     * @param url      访问路径
     * @param file     上传文件
     * @param fileName 文件名
     * @return
     */
    public String postSendFile(String url, MultipartFile file, String fileName, Map<String, Object> body) {
        try {
            //封装请求的表头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            //构造请求体，使用LinkedMultiValueMap
            MultiValueMap<String, Object> resultMap = new LinkedMultiValueMap<>();

            ByteArrayResource byteArrayResource = new ByteArrayResource(file.getBytes()) {
                @Override
                public long contentLength() {
                    return file.getSize();
                }

                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };
            resultMap.add(fileName, byteArrayResource);
            //封装请求参数
            formatBody(resultMap, body);
            //HttpEntity封装整个请求报文
            HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(resultMap, headers);
            //请求接口
            ResponseEntity<String> postForEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            if (postForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, postForEntity.toString());
            }
            //返回请求结果
            return postForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * post 传输多个文件
     *
     * @param url      请求路径
     * @param files    文件
     * @param fileName 文件名
     * @param body     文件内容
     * @return 返回结果
     */
    public String postSendFiles(String url, List<MultipartFile> files, String fileName, Map<String, Object> body) {
        try {
            //封装请求的表头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            //构造请求体，使用LinkedMultiValueMap
            MultiValueMap<String, Object> resultMap = new LinkedMultiValueMap<>();
            for (MultipartFile file : files) {
                ByteArrayResource byteArrayResource = new ByteArrayResource(file.getBytes()) {
                    @Override
                    public long contentLength() {
                        return file.getSize();
                    }

                    @Override
                    public String getFilename() {
                        return file.getOriginalFilename();
                    }
                };
                resultMap.add(fileName, byteArrayResource);
            }
            //封装请求参数
            formatBody(resultMap, body);
            //HttpEntity封装整个请求报文
            HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(resultMap, headers);
            //请求接口
            ResponseEntity<String> postForEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
            if (postForEntity.getStatusCodeValue() != HttpStatus.OK.value()) {
                throw new BizException(CommonConstant.ERROR_500, postForEntity.toString());
            }
            return postForEntity.getBody();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }


    /**
     * 封装请求参数
     *
     * @param param
     * @param body
     */
    public void formatBody(MultiValueMap<String, Object> param, Map<String, Object> body) {
        if (CollUtil.isNotEmpty(body)) {
            for (Map.Entry<String, Object> entry : body.entrySet()) {
                param.add(entry.getKey(), entry.getValue());
            }
        }
    }


}
