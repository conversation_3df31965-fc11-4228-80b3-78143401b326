package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.entity.JchcFile;
import cn.microvideo.module.plyh.core.mapper.JchcFileMapper;
import cn.microvideo.module.plyh.core.service.IJchcFileService;
import cn.microvideo.module.plyh.core.util.FileHttpUtils;
import cn.microvideo.module.plyh.core.util.MinioUtil;
import cn.microvideo.module.plyh.core.util.ThreadPool;
import cn.microvideo.module.plyh.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 文件管理表
 * @Author: spring-boot
 * @Date: 2023-08-31
 * @Version: V1.0
 */
@Service
public class JchcFileServiceImpl extends ServiceImpl<JchcFileMapper, JchcFile> implements IJchcFileService {

    @Resource
    private MinioUtil minIoUtil;

    @Resource
    private FileHttpUtils fileHttpUtils;

    /**
     * 文档转换服务地址
     */
    @Value("${idocview.url}")
    private String viewUrl;

    /**
     * 保存数据
     *
     * @param jchcFile
     */
    @Override
    public void saveByEntity(JchcFile jchcFile) {
        //UUID
        jchcFile.setId(IdUtil.simpleUUID());
        //上传时间
        jchcFile.setUploadTime(new Date());
        //保存数据
        this.save(jchcFile);

    }

    /**
     * 更新数据
     *
     * @param jchcFile
     */
    @Override
    public void updateByEntity(JchcFile jchcFile) {
        this.updateByEntity(jchcFile);
    }

    /**
     * 文件上传
     *
     * @param origin
     * @return
     */
    public JchcFile upload(MultipartFile origin, JchcFile sysFile) {
        try {
            //校验文件编码
            if (CharSequenceUtil.isNotBlank(sysFile.getFileCode()) && CollUtil.isNotEmpty(selectByFileCode(sysFile.getFileCode()))) {
                throw new BizException(CommonConstant.ERROR_500, "文件code已经存在！");
            }
            sysFile.setId(IdUtil.simpleUUID());
            //上传文件
            String fileName = minIoUtil.upload(origin);
            //文件名称
            sysFile.setFileName(origin.getOriginalFilename());
            //文件类型
            sysFile.setContentType(origin.getContentType());
            //文件浏览路径
            sysFile.setObjectName(fileName);
            //文件大小
            sysFile.setTotalSpace((int) origin.getSize());
            //文件类型
            sysFile.setContentType(origin.getContentType());
            //上传时间
            sysFile.setUploadTime(new Date());
            this.save(sysFile);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return sysFile;
    }

    /**
     * 上传文件
     *
     * @param code
     * @param code
     * @return
     */
    public List<JchcFile> selectByFileCode(String code) {
        LambdaQueryWrapper<JchcFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(JchcFile::getFileCode, code);
        return this.list(queryWrapper);
    }

    /**
     * 上传文件和上传Idocview
     *
     * @param origin
     */
    @Override
    public JchcFile uploadDocView(MultipartFile origin, JchcFile jchcFile) {
        try {
            //上传文件
            jchcFile = upload(origin, jchcFile);
            //上传到IdocView
            upload2Idocview(jchcFile, origin);
            return jchcFile;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 上传文件和上传Idocview
     *
     * @param origin
     */
    @Override
    public JchcFile uploadFile(MultipartFile origin, JchcFile jchcFile) {
        try {
            //上传文件
            jchcFile = upload(origin, jchcFile);
            return jchcFile;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 将附件上传至IDOCV服务器进行预转换
     *
     * @param file
     */
    private void upload2Idocview(JchcFile file, MultipartFile origin) {
        ThreadPool.execute(() -> {
            try {
                String filename = origin.getOriginalFilename();
                String baseName = FilenameUtils.getBaseName(filename);
                String extension = FilenameUtils.getExtension(filename);
                String encodeName = URLEncoder.encode(baseName.concat("(点击下载).").concat(extension), StandardCharsets.UTF_8.name());
                String url = viewUrl + "/doc/upload?name=" + encodeName;

                Map<String, Object> body = new HashMap<>();
                body.put("token", "testtoken");

                String result = fileHttpUtils.postSendFile(url, origin, "file", body);

                if (CharSequenceUtil.isNotBlank(result)) {
                    JSONObject json = JSONUtil.parseObj(result);
                    if (json != null) {
                        file.setIdocviewUuid(json.getStr("uuid"));
                        baseMapper.updateById(file);
                    }
                }

            } catch (Exception e) {
                throw new BizException(CommonConstant.ERROR_500, e.getMessage());
            }

        });
    }

    /**
     * 文件下载
     *
     * @param id
     * @param response
     */
    @Override
    public void download(String id, HttpServletResponse response) {
        try {
            JchcFile sysFile = this.getById(id);
            if (null == sysFile) {
                throw new BizException(CommonConstant.ERROR_500, "未找到文件数据！");
            }
            //判定是否上传到文件存储
            if (CharSequenceUtil.isBlank(sysFile.getObjectName())) {
                throw new BizException(CommonConstant.ERROR_500, "未找到可下载文件！");
            }
            minIoUtil.download(sysFile.getFileName(), sysFile.getObjectName(), response);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 文件下载
     *
     * @param fileCode
     * @param response
     */
    @Override
    public void downloadByCode(String fileCode, HttpServletResponse response) {
        try {
            LambdaQueryWrapper<JchcFile> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(JchcFile::getFileCode, fileCode);
            JchcFile sysFile = this.getOne(queryWrapper);
            if (null == sysFile) {
                throw new BizException(CommonConstant.ERROR_500, "未找到文件数据！");
            }
            //判定是否上传到文件存储
            if (CharSequenceUtil.isBlank(sysFile.getObjectName())) {
                throw new BizException(CommonConstant.ERROR_500, "未找到可下载文件！");
            }
            minIoUtil.download(sysFile.getFileName(), sysFile.getObjectName(), response);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 实体章授权拍照上传
     *
     * @param origin
     * @param sysFile
     * @param session
     * @return
     */
    @Override
    public JchcFile uploadForAuthEntitySignet(MultipartFile origin, JchcFile sysFile, HttpSession session) {
        try {
            sysFile.setId(IdUtil.simpleUUID());
            //上传文件
            String fileName = minIoUtil.upload(origin);
            //文件名称
            sysFile.setFileName(origin.getOriginalFilename());
            //文件类型
            sysFile.setContentType(origin.getContentType());
            //文件浏览路径
            sysFile.setObjectName(fileName);
            //文件名
            sysFile.setIdocviewUuid(minIoUtil.preview(fileName));
            //文件大小
            sysFile.setTotalSpace((int) origin.getSize());
            //文件类型
            sysFile.setContentType(origin.getContentType());
            this.save(sysFile);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return sysFile;
    }

    /**
     * 根据formId 查询数据
     *
     * @param id
     * @return
     */
    @Override
    public List<JchcFile> selectByFormId(String id) {
        LambdaQueryWrapper<JchcFile> query = new LambdaQueryWrapper<JchcFile>();
        query.eq(JchcFile::getFormId, id);
        return this.list(query);
    }


}
