package cn.microvideo.module.plyh.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 发布记录日期关联表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Data
@TableName("b_plyh_release_day")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "b_plyh_release_day对象", description = "发布记录日期关联表")
public class ReleaseDay {

    /**
     * UUID
     */

    @ApiModelProperty(value = "UUID")
    @TableId("F_VC_ID")
    private String id;
    /**
     * 发布ID
     */

    @ApiModelProperty(value = "发布ID")
    @TableField("F_VC_RELEASE_ID")
    private String releaseId;
    /**
     * 日期ID
     */

    @ApiModelProperty(value = "日期ID")
    @TableField("F_VC_HOLIDAY_ID")
    private String holidayId;
}
