package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.commonconstant.Contant;
import cn.microvideo.module.plyh.core.entity.DeptUser;
import cn.microvideo.module.plyh.core.entity.Holiday;
import cn.microvideo.module.plyh.core.entity.HolidayDeptUser;
import cn.microvideo.module.plyh.core.mapper.DeptUserMapper;
import cn.microvideo.module.plyh.core.service.IDeptUserService;
import cn.microvideo.module.plyh.core.service.IHolidayDeptUserService;
import cn.microvideo.module.plyh.core.service.IHolidayService;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.vo.BatchDeptUserVO;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.module.plyh.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 值班部门人员管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Service
public class DeptUserServiceImpl extends ServiceImpl<DeptUserMapper, DeptUser> implements IDeptUserService {

    @Resource
    private UserInfoUtil userInfoUtil;

    @Resource
    private IHolidayDeptUserService holidayDeptUserService;

    @Resource
    private IHolidayService holidayService;


    @Override
    public List<DeptUser> selectByFormId(String id) {
        LambdaQueryWrapper<DeptUser> query = new LambdaQueryWrapper<DeptUser>();
        //排序
        query.orderByAsc(DeptUser::getSort);
        //查询父ID
        query.eq(DeptUser::getDeptId, id);
        //删除标记
        query.eq(DeptUser::getDelFlag, Contant.DEL_FLAG_0);
        return this.list(query);
    }

    /**
     * 根据formId批量获取数据
     *
     * @param ids
     * @return
     */
    @Override
    public List<DeptUser> selectByFormIds(List<String> ids) {
        LambdaQueryWrapper<DeptUser> query = new LambdaQueryWrapper();
        //排序
        query.orderByAsc(DeptUser::getSort);
        //获取数据
        query.in(DeptUser::getDeptId, ids);
        //删除标记
        query.eq(DeptUser::getDelFlag, Contant.DEL_FLAG_0);
        return this.list(query);
    }

    /**
     * 批量新增数据
     *
     * @param batchDeptUserVO
     */
    @Override
    public void addBatchUsers(BatchDeptUserVO batchDeptUserVO) {
        try {
            MicrovideoUserVO microvideoUserVO = userInfoUtil.getHttpSessionUser();
            if (null == microvideoUserVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前登录人信息！");
            }
            //数据校验
            if (CharSequenceUtil.isBlank(batchDeptUserVO.getDeptId())) {
                throw new BizException(CommonConstant.ERROR_500, "部门ID不能为空!");
            }
            Map<String, List<HolidayDeptUser>> holidayDeptUserMap = new HashMap<>();
            //获取当前往后排班的日期
            List<Holiday> holidayList = holidayService.selectByDateTime(DateUtil.format(new Date(), Contant.DATA_DAY), null);
            if (CollUtil.isNotEmpty(holidayList)) {
                List<String> holidayIds = holidayList.stream().map(Holiday::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(holidayIds)) {
                    //获取后面的排班数据
                    //获取所删除人员是否已经排过班
                    List<HolidayDeptUser> holidayDeptUsers = holidayDeptUserService.selectByHolidayDeptId(holidayIds, batchDeptUserVO.getDeptId());
                    if (CollUtil.isNotEmpty(holidayDeptUsers)) {
                        //封装数据关系
                        holidayDeptUserMap = holidayDeptUsers.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getDeptUserId())).collect(Collectors.groupingBy(HolidayDeptUser::getDeptUserId));
                    }
                }
            }
            //人员数据校验
            if (CollUtil.isEmpty(batchDeptUserVO.getDeptUsers())) {
                throw new BizException(CommonConstant.ERROR_500, "人员数据不能为空!");
            }
            //获取错误信息
            StringBuffer errLog = new StringBuffer();
            //获取已经排版的人员数据
            List<DeptUser> haveUserList = selectByDeptIdList(batchDeptUserVO.getDeptId());
            //处理新增数据
            List<DeptUser> addList = addDeptUser(haveUserList, batchDeptUserVO.getDeptUsers(), batchDeptUserVO.getDeptId(), microvideoUserVO);

            //需要删除的人员信息
            List<DeptUser> deleteList = deleteDeptUser(haveUserList, batchDeptUserVO.getDeptUsers(), microvideoUserVO, holidayDeptUserMap, errLog, holidayList);
            //如果存在删除排过班的数据
            if(CharSequenceUtil.isNotBlank(errLog)){
                throw new BizException(CommonConstant.TEXT_ERROR_CODE, errLog.toString());
            }
            //新增数据
            if (CollUtil.isNotEmpty(addList)) {
                this.saveBatch(addList);
            }
            //删除数据
            if (CollUtil.isNotEmpty(deleteList)) {
                this.updateBatchById(deleteList);
            }


        } catch (Exception e) {
            throw new BizException(CommonConstant.TEXT_ERROR_CODE, e.getMessage());
        }

    }

    /**
     * 获取需要删除的数据列表
     *
     * @param haveUserList
     * @param deptUsers
     * @param microvideoUserVO
     * @return
     */
    private List<DeptUser> deleteDeptUser(List<DeptUser> haveUserList, List<DeptUser> deptUsers, MicrovideoUserVO microvideoUserVO, Map<String, List<HolidayDeptUser>> holidayDeptUserMap, StringBuffer errLog, List<Holiday> holidayList) {
        List<DeptUser> list = new ArrayList<>();
        //构造map
        Map<String, String> map = selectByDeptIdMap(deptUsers);
        for (DeptUser item : haveUserList) {
            if (CharSequenceUtil.isBlank(map.get(item.getUserId()))) {
                //判定是否排班(已经排班则不允许删除)
                List<HolidayDeptUser> deptUserList = holidayDeptUserMap.get(item.getId());
                if (CollUtil.isNotEmpty(deptUserList) && CollUtil.isNotEmpty(holidayList)) {
                    Map<String, List<HolidayDeptUser>> holidayMap = deptUserList.stream().filter(itemUser -> CharSequenceUtil.isNotBlank(itemUser.getHolidayId())).collect(Collectors.groupingBy(HolidayDeptUser::getHolidayId));
                    //获取具体的排班天数
                    for (Holiday itemVO : holidayList) {
                        if (CollUtil.isNotEmpty(holidayMap.get(itemVO.getId()))) {
                            errLog.append(item.getUserName()+"-->"+DateUtil.format(itemVO.getHolidayData(),Contant.DATA_DAY)+"存在排班,请先撤销！<br>");
                        }
                    }
                    continue;
                }
                //更新时间
                item.setUpdateTime(new Date());
                //更新人
                item.setUpdateBy(microvideoUserVO.getId());
                //删除标记
                item.setDelFlag(Contant.DEL_FLAG_1);
                list.add(item);
            }
        }
        return list;
    }

    /**
     * 获取新增的列表
     *
     * @return
     */
    public List<DeptUser> addDeptUser(List<DeptUser> sourceList, List<DeptUser> addList, String deptId, MicrovideoUserVO microvideoUserVO) {
        List<DeptUser> list = new ArrayList<>();
        //构造map
        Map<String, String> map = selectByDeptIdMap(sourceList);
        for (DeptUser item : addList) {
            if (CharSequenceUtil.isBlank(map.get(item.getUserId()))) {
                //UUID
                item.setId(IdUtil.simpleUUID());
                //部门ID
                item.setDeptId(deptId);
                //创建人
                item.setCreateBy(microvideoUserVO.getId());
                //创建时间
                item.setCreateTime(new Date());
                //更新时间
                item.setUpdateTime(new Date());
                list.add(item);
            }

        }
        return list;
    }

    /**
     * 根据部门Id获取数据列表
     *
     * @param deptId
     * @return
     */
    public List<DeptUser> selectByDeptIdList(String deptId) {
        LambdaQueryWrapper<DeptUser> query = new LambdaQueryWrapper<>();
        query.eq(DeptUser::getDeptId, deptId);
        //删除标记
        query.eq(DeptUser::getDelFlag, Contant.DEL_FLAG_0);
        return this.list(query);
    }

    /**
     * 根据部门Id获取数据列表
     *
     * @param addList
     * @return
     */
    public Map<String, String> selectByDeptIdMap(List<DeptUser> addList) {
        Map<String, String> map = new HashMap<>();
        if (CollUtil.isNotEmpty(addList)) {
            for (DeptUser item : addList) {
                map.put(item.getUserId(), item.getUserId());
            }
        }
        return map;
    }

    /**
     * 根据formId清除数据
     *
     * @param formId
     */
    @Override
    public void deleteByFormId(String formId) {
        LambdaQueryWrapper<DeptUser> query = new LambdaQueryWrapper<DeptUser>();
        query.eq(DeptUser::getDeptId, formId);
        this.remove(query);
    }

    /**
     * 保存数据
     *
     * @param id
     * @param deptUsers
     */
    @Override
    public void saveByFormIdUsers(String id, List<DeptUser> deptUsers) {
        try {
            if (CollUtil.isNotEmpty(deptUsers)) {
                for (DeptUser item : deptUsers) {
                    //UUID
                    item.setId(IdUtil.simpleUUID());
                    //关联部门ID
                    item.setDeptId(id);
                    //创建时间
                    item.setCreateTime(new Date());
                    //更新时间
                    item.setUpdateTime(new Date());
                }
                this.saveBatch(deptUsers);
            }

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }


    }
}
