package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.commonconstant.Contant;
import cn.microvideo.module.plyh.core.entity.DutyRelease;
import cn.microvideo.module.plyh.core.entity.Holiday;
import cn.microvideo.module.plyh.core.mapper.DutyReleaseMapper;
import cn.microvideo.module.plyh.core.service.IDutyReleaseService;
import cn.microvideo.module.plyh.core.service.IHolidayService;
import cn.microvideo.module.plyh.core.service.IReleaseDayService;
import cn.microvideo.module.plyh.core.util.FaceProperties;
import cn.microvideo.module.plyh.core.util.FaceTaskUtils;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.vo.*;
import cn.microvideo.module.plyh.exception.BizException;
import cn.microvideo.qsc.client.api.QsApi;
import cn.microvideo.qsc.client.entity.User;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 值班发布记录表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Service
public class DutyReleaseServiceImpl extends ServiceImpl<DutyReleaseMapper, DutyRelease> implements IDutyReleaseService {
    @Resource
    private UserInfoUtil userInfoUtil;

    @Resource
    private IReleaseDayService releaseDayService;

    @Resource
    private FaceProperties faceProperties;

    @Resource
    private FaceTaskUtils faceTaskUtils;

    @Resource
    private IHolidayService holidayService;

    /**
     * 发布到通知通告
     *
     * @param dutyRelease
     */
    @Override
    public void addRelease(AddDutyReleaseVO dutyRelease) {
        try {
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if (null == userVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前用户信息！");
            }
            //保存发布数据
            dutyRelease.setId(IdUtil.simpleUUID());
            //单位ID
            dutyRelease.setOrgId(userVO.getGroupId());
            //单位名称
            dutyRelease.setOrgName(userVO.getGroupName());
            //创建人
            dutyRelease.setCreateBy(userVO.getId());
            //创建时间
            dutyRelease.setCreateTime(new Date());
            //更新时间
            dutyRelease.setUpdateTime(new Date());
            //发布人Id
            dutyRelease.setUserId(userVO.getId());
            //发布人姓名
            dutyRelease.setUserName(userVO.getName());
            //部门ID
            dutyRelease.setDeptId(userVO.getDeptId());
            //部门名称
            dutyRelease.setDeptName(userVO.getDeptName());
            //保存发布日期关联表
            if (CharSequenceUtil.isNotBlank(dutyRelease.getDayIds())) {
                releaseDayService.addByHoliday(dutyRelease.getId(), dutyRelease.getDayIds());
            }
            //保存数据
            this.save(dutyRelease);
            //推送数据
            sendPortal(userVO, dutyRelease);
            //发布到门户（预留）
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 信息发布推送(根据)
     */
    public void sendPortal(MicrovideoUserVO userVO, AddDutyReleaseVO dutyRelease) {
        try {
            List<NoticePublicity> list = new ArrayList<>();
            //获取单位下的人员列表
            List<User> userList = QsApi.getApi().findPersonByOrganizationId(userVO.getGroupId());
            //封装数据
            if (CollUtil.isNotEmpty(userList)) {
                for (User item : userList) {
                    //添加数据
                    list.add(NoticePublicity.builder()
                            .noticeTitle(dutyRelease.getTitle()) //标题
                            .noticeId(dutyRelease.getId())       //通知
                            .groupId(userVO.getGroupId())
                            .groupName(userVO.getGroupName())
                            .noticeUrl(String.format(faceProperties.getNoticeUrl(), dutyRelease.getId()))
                            .fileType(Contant.NOTICE_TYPE_0)
                            .userId(item.getUuid())
                            .userName(item.getName())
                            .build()
                    );
                }
            }
            //推送数据
            faceTaskUtils.addNoticeBatch(list);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 数据分页
     *
     * @param page
     * @param dutyRelease
     * @return
     */
    @Override
    public IPage<DutyReleaseUserVO> pageList(Page<DutyReleaseUserVO> page, DutyReleaseSearchVO dutyRelease) {
        try {
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            //获取当前登录用户的数据信息
            dutyRelease.setOrgId(userVO.getGroupId());
            //查询数据列表
            IPage<DutyReleaseUserVO> pageList = baseMapper.pageList(page, dutyRelease);
            //格式化数据
            formatHoliday(pageList.getRecords());
            return pageList;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 格式化时间数据
     *
     * @param records
     */
    private void formatHoliday(List<DutyReleaseUserVO> records) {
        if (CollUtil.isNotEmpty(records)) {
            List<String> releaseIds = records.stream().map(DutyReleaseUserVO::getId).collect(Collectors.toList());
            //获取分组数据
            Map<String, List<ReleaseHolidayVO>> mapList = new HashMap<>();
            //查询关联数据
            if (CollUtil.isNotEmpty(releaseIds)) {
                //查询数据
                List<ReleaseHolidayVO> releaseHolidayVOS = releaseDayService.selectByReleaseIdsHoliday(releaseIds);
                if (CollUtil.isNotEmpty(releaseHolidayVOS)) {
                    mapList = releaseHolidayVOS.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getReleaseId())).collect(Collectors.groupingBy(ReleaseHolidayVO::getReleaseId));
                }
            }
            //回显日期
            for (DutyReleaseUserVO item : records) {
                item.setHolidayList(mapList.get(item.getId()));
            }
        }
    }

    /**
     * 数据删除
     */
    public void removeByCancelId(String id) {
        try {
            //获取数据
            DutyRelease dutyRelease = this.getById(id);
            if (null == dutyRelease) {
                throw new BizException(CommonConstant.ERROR_500, "未找到任务实体！");
            }
            //撤销发布
            faceTaskUtils.cancelNoticeBatch(id);
            //删除关联
            releaseDayService.removeByFormId(id);
            //删除发布数据
            this.removeById(id);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }


    }

    /**
     * @param id
     * @return
     */
    @Override
    public ReleaseHolidayDetailVO getByReleaseId(String id) {
        try {
            //根据Id获取主体数据
            DutyRelease dutyRelease = this.getById(id);
            if (null == dutyRelease) {
                throw new BizException(CommonConstant.ERROR_500, "未找到任务实体！");
            }
            //更新访问量
            dutyRelease = updateVisitsNumber(dutyRelease);
            //复制数据
            ReleaseHolidayDetailVO releaseHolidayDetailVO = BeanUtil.copyProperties(dutyRelease, ReleaseHolidayDetailVO.class);
            //获取日期数据
            List<Holiday> list = releaseDayService.selectByReleaseId(id);
            //日期详情列表
            releaseHolidayDetailVO.setHolidayMonthVO(holidayService.selectByHolidays(list, dutyRelease.getOrgId()));
            //返回数据
            return releaseHolidayDetailVO;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 更新数据
     *
     * @param dutyRelease
     */
    public DutyRelease updateVisitsNumber(DutyRelease dutyRelease) {
        if (null != dutyRelease.getVisitsNumber()) {
            dutyRelease.setVisitsNumber(dutyRelease.getVisitsNumber() + Contant.INT_NUMBER_1);
        } else {
            dutyRelease.setVisitsNumber(Contant.INT_NUMBER_1);
        }
        //更新数据
        this.updateById(dutyRelease);
        //返回数据
        return dutyRelease;
    }

}
