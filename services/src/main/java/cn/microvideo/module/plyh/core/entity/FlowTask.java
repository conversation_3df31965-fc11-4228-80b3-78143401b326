package cn.microvideo.module.plyh.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 用印流程数据关系表
 * @Author: spring-boot
 * @Date: 2023-09-05
 * @Version: V1.0
 */
@Data
@TableName("t_plyh_flow_task")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "t_plyh_flow_task对象", description = "用印流程数据关系表")
public class FlowTask {

    /**
     * UUID
     */

    @ApiModelProperty(value = "UUID")
    @TableId("F_VC_UUID")
    private String uuid;
    /**
     * 用印申请UUID
     */

    @ApiModelProperty(value = "用印申请UUID")
    @TableField("F_VC_SIGNET_UUID")
    private String signetUuid;
    /**
     * 任务ID
     */

    @ApiModelProperty(value = "任务ID")
    @TableField("F_VC_TASK_ID")
    private String taskId;
    /**
     * 任务Key
     */

    @ApiModelProperty(value = "任务Key")
    @TableField("F_VC_TASK_KEY")
    private String taskKey;
    /**
     * 处理人ID
     */

    @ApiModelProperty(value = "处理人ID")
    @TableField("F_VC_HANDLE_USER_ID")
    private String handleUserId;
    /**
     * 处理人姓名
     */

    @ApiModelProperty(value = "处理人姓名")
    @TableField("F_VC_HANDLE_USER_NAME")
    private String handleUserName;
    /**
     * 处理时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "处理时间")
    @TableField("F_DT_HANDLE_TIME")
    private Date handleTime;
    /**
     * 节点编码（running、comping)
     */

    @ApiModelProperty(value = "节点编码（running、comping)")
    @TableField("F_VC_NODE_CODE")
    private String nodeCode;
    /**
     * 节点状态（办理中、完结、驳回）
     */

    @ApiModelProperty(value = "节点状态（办理中、完结、驳回）")
    @TableField("F_VC_NODE_STATUS")
    private String nodeStatus;

    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    @TableField("F_VC_CREATE_BY")
    private String createBy;
    /**
     * 创建时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @TableField("F_DT_CREATE_TIME")
    private Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    @TableField("F_VC_UPDATE_BY")
    private String updateBy;
    /**
     * 更新时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    @TableField("F_DT_UPDATE_TIME")
    private Date updateTime;

    /**
     * 节点分组
     */

    @ApiModelProperty(value = "节点分组")
    @TableField("F_VC_GROUP_ID")
    private String groupId;
}
