package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.commonconstant.Contant;
import cn.microvideo.module.plyh.commonconstant.Enum.ActivityEnum;
import cn.microvideo.module.plyh.core.entity.BpmManage;
import cn.microvideo.module.plyh.core.entity.FlowTask;
import cn.microvideo.module.plyh.core.mapper.BpmManageMapper;
import cn.microvideo.module.plyh.core.service.IBpmManageService;
import cn.microvideo.module.plyh.core.service.IFlowTaskService;
import cn.microvideo.module.plyh.core.util.FileHttpUtils;
import cn.microvideo.module.plyh.core.vo.*;
import cn.microvideo.module.plyh.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: bpm流程调用表
 * @Author: spring-boot
 * @Date: 2023-09-05
 * @Version: V1.0
 */
@Service
@Slf4j
public class BpmManageServiceImpl extends ServiceImpl<BpmManageMapper, BpmManage> implements IBpmManageService {

    @Resource
    private FileHttpUtils httpUtils;

    @Resource
    private IFlowTaskService signetTaskService;


//    @Resource
//    private ISignetService signetService;

    @Value("${bpm.server.url}")
    private String url;

    @Override
    public StratReturnVO startBpm(String formData, String signetId, MicrovideoSessionUser user) {
        try {
            //查询流程需要的数据
            BpmManage bpmManage = selectByBpmStartVO();
            if (null == bpmManage) {
                throw new BizException(CommonConstant.ERROR_500, "请配置流程数据！");
            }
            //用户ID
            bpmManage.setCurrentUserId(user.getId());
            //用户姓名
            bpmManage.setCurrentUserName(user.getName());
            //第一个节点的用户
            bpmManage.setFormData(formData);
            //启动流程
            //获取返回内容
            String data = postByString(url.concat("/start"), JSONUtil.parseObj(bpmManage));
            //返回成功数据对象
            StratReturnVO stratReturnVO = JSONUtil.toBean(data, StratReturnVO.class);
            //处理任务信息
            stratReturnVO.setTaskUuid(formatTask(stratReturnVO, signetId, null));
            return stratReturnVO;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 处理任务流程关系
     */
    public String formatTask(StratReturnVO stratReturnVO, String signetId, String groupUuid) {
        try {
            //用于启动流程返回的拟稿的数据UUid
            String taskUuid = null;
            List<FlowTask> list = new ArrayList<>();
            FlowTask signetTask = null;
            //循环保存节点数据
            if (CollUtil.isNotEmpty(stratReturnVO.getFlowCurrentStepList())) {
                //创建分组ID
                String groupId = IdUtil.simpleUUID();
                for (FlowCurrListVO flow : stratReturnVO.getFlowCurrentStepList()) {
                    //判定数据是否已经存在(存在则跳过，否则保存)
                    if (isHappen(signetId, flow.getFlowCurrentStep(), flow.getFlowCurrentStepKey(), flow.getFlowCurrentUser(), groupUuid)) {
                        continue;
                    }
                    //创建实体
                    signetTask = new FlowTask();
                    //UUID
                    signetTask.setUuid(IdUtil.simpleUUID());
                    //taskId
                    signetTask.setTaskId(flow.getFlowCurrentStep());
                    //存储任务用户关系
                    signetTask.setTaskKey(flow.getFlowCurrentStepKey());
                    //节点状态
                    signetTask.setNodeStatus(flow.getFlowCurrentStepName());
                    //节点编码
                    signetTask.setNodeCode(stratReturnVO.getState());
                    //处理人ID
                    signetTask.setHandleUserId(flow.getFlowCurrentUser());
                    //用印UUID
                    signetTask.setSignetUuid(signetId);
                    //创建时间
                    signetTask.setCreateTime(new Date());
                    //更新时间
                    signetTask.setUpdateTime(new Date());
                    //处理人
                    signetTask.setHandleUserName(flow.getFlowCurrentUserName());
                    //绑定分组关系
                    signetTask.setGroupId(groupId);
                    //赋值数据
                    list.add(signetTask);
                    //用户提交第一次返回的数据ID
                    taskUuid = signetTask.getUuid();
                }
            }
            //保存数据
            if (CollUtil.isNotEmpty(list)) {
                signetTaskService.saveBatch(list);
            }
            return taskUuid;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 判断数据是否存在
     *
     * @param signId
     * @param taskId
     * @param taskKey
     * @return
     */
    public boolean isHappen(String signId, String taskId, String taskKey, String userId, String groupUuid) {
        LambdaQueryWrapper<FlowTask> query = new LambdaQueryWrapper<>();
        query.eq(FlowTask::getSignetUuid, signId);
        query.eq(FlowTask::getTaskId, taskId);
        //防止不可以一直相互转办
        if (CharSequenceUtil.isNotBlank(groupUuid)) {
            query.eq(FlowTask::getGroupId, groupUuid);
        }
        query.eq(FlowTask::getTaskKey, taskKey);
        query.eq(FlowTask::getHandleUserId, userId);
        List<FlowTask> list = signetTaskService.list(query);
        log.info("判定是否是同一节点---->" + list.toString());
        if (CollUtil.isEmpty(list)) {
            return false;
        } else {
            return true;
        }
    }


    /**
     * 流程统一的审批流程
     *
     * @param agreeActiviotyVO
     */
    @Override
    public StratReturnVO agreeActivity(AgreeActiviotyVO agreeActiviotyVO, MicrovideoSessionUser user) {
        try {
            //请求完成接口
            //办理人ID
            agreeActiviotyVO.setCurrentUserId(user.getId());
            //办理人姓名
            agreeActiviotyVO.setCurrentUserName(user.getName());
            //完成请求
            String data = postByString(url.concat("/completeFlow"), JSONUtil.parseObj(agreeActiviotyVO));
            //解析返回内容
            StratReturnVO stratReturnVO = JSONUtil.toBean(data, StratReturnVO.class);
            //添加任务
            formateActivityData(stratReturnVO, agreeActiviotyVO, user);
            return stratReturnVO;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 处理用户审批权限
     *
     * @param stratReturnVO
     */
    private void formateFreeJumpData(StratReturnVO stratReturnVO, AgreeActiviotyVO agreeActiviotyVO, MicrovideoSessionUser user) {

        //创建节点数据
        if (!ActivityEnum.FINISH.getCode().equals(stratReturnVO.getState())) {
            formatTask(stratReturnVO, agreeActiviotyVO.getUuid(), null);
            //处理节点
            signetTaskService.dealFreeJumpNode(agreeActiviotyVO.getTaskUuid(), ActivityEnum.COMPING.getCode(), user);
        } else {
            //处理节点
            signetTaskService.dealNode(agreeActiviotyVO.getTaskUuid(), ActivityEnum.FINISH.getCode(), user);
        }

    }

    /**
     * 处理用户审批权限
     *
     * @param stratReturnVO
     */
    private void formateActivityData(StratReturnVO stratReturnVO, AgreeActiviotyVO agreeActiviotyVO, MicrovideoSessionUser user) {

        //创建节点数据
        if (!ActivityEnum.FINISH.getCode().equals(stratReturnVO.getState())) {
            //查询出数据是否是一批
            FlowTask signetTask = signetTaskService.getById(agreeActiviotyVO.getTaskUuid());
            if (null == signetTask) {
                formatTask(stratReturnVO, agreeActiviotyVO.getUuid(), null);
            } else {
                formatTask(stratReturnVO, agreeActiviotyVO.getUuid(), signetTask.getGroupId());
            }

            //处理节点
            signetTaskService.dealNode(agreeActiviotyVO.getTaskUuid(), ActivityEnum.COMPING.getCode(), user);
        } else {
            //处理节点
            signetTaskService.dealNode(agreeActiviotyVO.getTaskUuid(), ActivityEnum.FINISH.getCode(), user);
        }

    }

    /**
     * 处理用户审批权限
     *
     * @param stratReturnVO
     */
    private void formateTerminateData(StratReturnVO stratReturnVO, AgreeActiviotyVO agreeActiviotyVO, MicrovideoSessionUser user) {

        //创建节点数据
        if (!ActivityEnum.FINISH.getCode().equals(stratReturnVO.getState())) {
            if (CharSequenceUtil.isBlank(agreeActiviotyVO.getTransferUserVO().getUserId())) {
                throw new BizException(CommonConstant.ERROR_500, "未找到转办人！");
            }
            formatTaskTerminate(stratReturnVO, agreeActiviotyVO.getUuid(), agreeActiviotyVO.getTransferUserVO());
            //处理节点
            signetTaskService.dealNode(agreeActiviotyVO.getTaskUuid(), ActivityEnum.COMPING.getCode(), user);
        } else {
            //处理节点
            signetTaskService.dealNode(agreeActiviotyVO.getTaskUuid(), ActivityEnum.FINISH.getCode(), user);
        }

    }

    /**
     * 处理转办数据
     *
     * @param stratReturnVO
     * @param uuid
     */
    private void formatTaskTerminate(StratReturnVO stratReturnVO, String uuid, TransferUserVO vo) {
        List<FlowTask> taskList = new ArrayList<>();
        FlowTask flowTask = null;
        //循环保存节点数据
        if (CollUtil.isNotEmpty(stratReturnVO.getFlowCurrentStepList())) {
            for (FlowCurrListVO flow : stratReturnVO.getFlowCurrentStepList()) {
                //创建实体
                flowTask = new FlowTask();
                //UUID
                flowTask.setUuid(IdUtil.simpleUUID());
                //taskId
                flowTask.setTaskId(flow.getFlowCurrentStep());
                //存储任务用户关系
                flowTask.setTaskKey(flow.getFlowCurrentStepKey());
                //节点状态
                flowTask.setNodeStatus(flow.getFlowCurrentStepName());
                //节点编码
                flowTask.setNodeCode(stratReturnVO.getState());
                //处理人ID
                flowTask.setHandleUserId(vo.getUserId());
                //用印UUID
                flowTask.setSignetUuid(uuid);
                //创建时间
                flowTask.setCreateTime(new Date());
                //更新时间
                flowTask.setUpdateTime(new Date());
                //处理人
                flowTask.setHandleUserName(vo.getUserName());
                //赋值数据
                taskList.add(flowTask);
                //用户提交第一次返回的数据ID
            }
        }
        //保存数据
        if (CollUtil.isNotEmpty(taskList)) {
            signetTaskService.saveBatch(taskList);
        }
    }

    /**
     * 根据流程实例ID查询下面配置的未执行环节及用户信息
     *
     * @param procId
     * @return
     */
    @Override
    public List<ActivityProIdUserVO> smsUsersByProcId(String procId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("procId", procId);
            //请求完成接口
            String data = getByString(url.concat("/smsUsersByProcId"), params);
            //返回解析的数据
            return JSONUtil.toList(data, ActivityProIdUserVO.class);

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 获取可退回节点用户
     *
     * @param backNodeVO
     * @return
     */
    @Override
    public List<ActivityProIdUserVO> getBackNodeUser(BackNodeVO backNodeVO) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("taskId", backNodeVO.getTaskId());
            params.put("taskDefKey", backNodeVO.getTaskDefKey());
            //请求完成接口
            String data = getByString(url.concat("/getBackNodeUser"), params);
            //返回解析的数据
            return JSONUtil.toList(data, ActivityProIdUserVO.class);

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 获取可退回节点用户
     *
     * @param taskId
     * @return
     */
    @Override
    public List<ActivityBackVO> getBackNodes(String taskId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("taskId", taskId);
            //请求完成接口
            String data = getByString(url.concat("/getBackNode"), params);
            //返回解析的数据
            return JSONUtil.toList(data, ActivityBackVO.class);

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    @Override
    public void freeJump(AgreeActiviotyVO agreeActiviotyVO, MicrovideoSessionUser user) {
        try {
            //请求完成接口
            agreeActiviotyVO.setCurrentUserId(user.getId());
            agreeActiviotyVO.setCurrentUserName(user.getName());
            //完成请求
            String data = postByString(url.concat("/freeJump"), JSONUtil.parseObj(agreeActiviotyVO));
            //解析返回内容
            StratReturnVO stratReturnVO = JSONUtil.toBean(data, StratReturnVO.class);
            //添加任务
            formateFreeJumpData(stratReturnVO, agreeActiviotyVO, user);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 会签退回
     *
     * @param agreeActiviotyVO
     * @param user
     */
    @Override
    public void backHq(AgreeActiviotyVO agreeActiviotyVO, MicrovideoSessionUser user) {
        try {
            //请求完成接口
            agreeActiviotyVO.setCurrentUserId(user.getId());
            agreeActiviotyVO.setCurrentUserName(user.getName());
            //完成请求
            String data = postByString(url.concat("/back/hq"), JSONUtil.parseObj(agreeActiviotyVO));
            //解析返回内容
            StratReturnVO stratReturnVO = JSONUtil.toBean(data, StratReturnVO.class);
            //添加任务
            formateFreeJumpData(stratReturnVO, agreeActiviotyVO, user);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 根据proId 查询流程进度
     *
     * @param proId
     * @return
     */
    @Override
    public List<NodeOpinionVO> nodeOpinion(String proId) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("procId", proId);
            //请求完成接口
            String data = getByString(url.concat("/nodeOpinion"), params);
            //返回解析的数据
            return JSONUtil.toList(data, NodeOpinionVO.class);

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 查询当前用户下所有的用户节点
     *
     * @param
     * @return
     */
    @Override
    public List<NodeUserVO> queryAllUserByModelKey() {
        try {
            //获取流程实例ID
            BpmManage bpmManage = this.selectByBpmStartVO();
            Map<String, Object> params = new HashMap<>();
            params.put("modelKey", bpmManage.getDefKey());
            //请求完成接口
            String data = getByString(url.concat("/queryAllUserByModelKey"), params);
            //返回解析的数据
            return JSONUtil.toList(data, NodeUserVO.class);

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 撤销
     *
     * @param cancelVO
     * @param user
     * @return
     */
    @Override
    public StratReturnVO cancel(AgreeActiviotyVO cancelVO, MicrovideoSessionUser user) {
        try {
            //撤销用户ID
            cancelVO.setUserId(user.getId());
            //撤销用户
            cancelVO.setUserName(user.getName());
            //请求完成接口
            //完成请求
            String data = postByString(url.concat("/cx"), JSONUtil.parseObj(cancelVO));
            //解析返回内容
            StratReturnVO stratReturnVO = JSONUtil.toBean(data, StratReturnVO.class);
            //添加任务
            formateActivityData(stratReturnVO, cancelVO, user);
            return stratReturnVO;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 任务中断-直接结束流程
     *
     * @param cancelVO
     * @param user
     */
    @Override
    public void terminate(AgreeActiviotyVO cancelVO, MicrovideoSessionUser user) {
        try {
            //撤销用户ID
            cancelVO.setUserId(user.getId());
            //撤销用户
            cancelVO.setUserName(user.getName());
            //请求完成接口
            //完成请求
            String data = postByString(url.concat("/terminate"), JSONUtil.parseObj(cancelVO));
            //解析返回内容
            StratReturnVO stratReturnVO = JSONUtil.toBean(data, StratReturnVO.class);
            //添加任务
            formateTerminateData(stratReturnVO, cancelVO, user);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 任务转办
     *
     * @param cancelVO
     * @param user
     */
    @Override
    public void transfer(AgreeActiviotyVO cancelVO, MicrovideoSessionUser user) {
        try {
            //撤销用户ID
            cancelVO.setCurrentUserId(user.getId());
            //撤销用户
            cancelVO.setCurrentUserName(user.getName());
            //请求完成接口
            //完成请求
            String data = postByString(url.concat("/transfer"), JSONUtil.parseObj(cancelVO));
            //解析返回内容
            StratReturnVO stratReturnVO = JSONUtil.toBean(data, StratReturnVO.class);
            //添加任务
            formateActivityData(stratReturnVO, cancelVO, user);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    @Override
    public String smsUsersByTaskId(String id) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("taskId", id);
            //请求完成接口
            String data = getByString(url.concat("/smsUsersByTaskId"), params);
            //返回解析的数据
            return data;

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 获取审批节点信息
     *
     * @return
     */
    @Override
    public List<ActivityProIdUserVO> getSubNode(SubNodeVO subNodeVO) {
        try {
            //如果taskId不存在则根据流程实例ID查询数据，否则通过taskId查询数据
            if (CharSequenceUtil.isBlank(subNodeVO.getTaskId())) {
                //获取全部节点数据
                List<NodeUserVO> list = queryAllUserByModelKey();
                //如果未找到数据
                if (CollUtil.isEmpty(list)) {
                    throw new BizException(CommonConstant.ERROR_500, "未找到流程节点信息！");
                }
                //解析第一个节点的数据
                return formatNodeUserList(list);
            } else {
                //扩展字段
                if (ObjectUtil.isEmpty(subNodeVO.getParams())) {
                    subNodeVO.setParams(new JSONObject());
                }
                //请求完成接口
                String data = postByString(url.concat("/getSubNode"), JSONUtil.parseObj(subNodeVO));
                return JSONUtil.toList(data, ActivityProIdUserVO.class);
            }


        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 格式化返回的节点数据
     *
     * @return
     */
    public List<ActivityProIdUserVO> formatNodeUserList(List<NodeUserVO> nodeUserVO) {
        List<ActivityProIdUserVO> list = new ArrayList<>();
        if (CollUtil.isNotEmpty(nodeUserVO)) {
            for (NodeUserVO nodeUser : nodeUserVO) {
                if (nodeUser.isFirst()) {
                    ActivityProIdUserVO activityProIdUserVO = new ActivityProIdUserVO();
                    activityProIdUserVO.setTaskId(nodeUser.getNodeId());
                    activityProIdUserVO.setTaskName(nodeUser.getNodeName());
                    if (CollUtil.isNotEmpty(nodeUser.getSpecMap())) {
                        List<ActivityUserVO> extObj = new ArrayList<>();
                        for (Map.Entry<String, String> entry : nodeUser.getSpecMap().entrySet()) {
                            ActivityUserVO activityUserVO = new ActivityUserVO();
                            activityUserVO.setId(entry.getKey());
                            activityUserVO.setFullname(entry.getKey());
                            activityUserVO.setUserNo(entry.getKey());
                            activityUserVO.setFullname(entry.getValue());
                            extObj.add(activityUserVO);
                        }
                        //获取第一个节点用户的数据
                        activityProIdUserVO.setExtObj(extObj);
                    }
                    list.add(activityProIdUserVO);
                }
            }
        }
        return list;
    }


    /**
     * 获取流程数据
     *
     * @return
     */
    public BpmManage selectByBpmStartVO() {
        try {
            LambdaQueryWrapper<BpmManage> query = new LambdaQueryWrapper<>();
            query.eq(BpmManage::getSoleKey, Contant.SOLE_KEY);
            List<BpmManage> list = this.list(query);
            if (CollUtil.isEmpty(list)) {
                throw new BizException(CommonConstant.ERROR_500, "请配置流程数据！");
            }
            return list.get(0);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * post请求接口
     *
     * @return
     */
    public String postByString(String serverUrl, JSONObject jsonObject) {
        try {
            //封装header
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
//            headers.add("mobile", "15755371521");
            //获取返回内容
            String result = httpUtils.postSendHeaderBodyRaw(serverUrl, headers, jsonObject);
            //解析参数
            if (CharSequenceUtil.isBlank(result)) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            //解析返回数据对象
            BPMResult result1 = JSONUtil.toBean(result, BPMResult.class);
            if (null == result1 || null == result1.getData()) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            if (Contant.HTTP_OK_0 != result1.getCode() && Contant.HTTP_OK_200 != result1.getCode()) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            //判定流程是否启动成功
            if (CharSequenceUtil.isBlank(result1.getData())) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            //返回数据
            return result1.getData();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * get请求接口
     *
     * @return
     */
    public String getByString(String serverUrl, Map<String, Object> params) {
        try {
            //封装header
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
//            headers.add("mobile", "15755371521");
            //获取返回内容
            String result = httpUtils.getSendHeaderParamBody(serverUrl, headers, params);
            //解析参数
            if (CharSequenceUtil.isBlank(result)) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            Map<String, Object> xmlData = XmlUtil.xmlToMap(result);
            //解析返回数据对象
            if (CollUtil.isEmpty(xmlData)) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            if (!ObjectUtil.equal(Contant.STR_HTTP_OK_0, xmlData.get("code")) && !ObjectUtil.equal(Contant.STR_HTTP_OK_200, xmlData.get("code"))) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            //判定流程是否启动成功
            if (ObjectUtil.isEmpty(xmlData.get("data"))) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            //返回数据
            return xmlData.get("data").toString();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }
}
