package cn.microvideo.module.plyh.core.util;


import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.ss.usermodel.*;

public abstract class PoiUtil {
    /**
     * [简要描述]：获取金额单元格样式</br>
     * [详细描述]：仿宋、居右</br>
     * [作者]：Administrator(2018-12-11)</br>
     *
     * @param workbook
     * @return
     */
    public static CellStyle getMoneyStyle(Workbook workbook) {
        // 生成正文样式
        CellStyle styleContent = workbook.createCellStyle();
        // 设置这些样式,居右
        styleContent.setAlignment(HorizontalAlignment.CENTER);
        styleContent.setBorderBottom(BorderStyle.THIN);
        styleContent.setBorderLeft(BorderStyle.THIN);
        styleContent.setBorderRight(BorderStyle.THIN);
        styleContent.setBorderTop(BorderStyle.THIN);
        // 生成一个字体
        Font font = workbook.createFont();
        font.setFontName("仿宋");
        font.setFontHeightInPoints((short) 11);
        styleContent.setFont(font);

        return styleContent;
    }

    /**
     * [简要描述]：加粗金额</br>
     * [详细描述]：</br>
     * [作者]：Administrator(2018-12-21)</br>
     *
     * @param workbook
     * @return
     */
    public static CellStyle getMoneyStyleB(Workbook workbook) {
        // 生成正文样式
        CellStyle styleContent = workbook.createCellStyle();
        // 设置这些样式,居右
        styleContent.setAlignment(HorizontalAlignment.RIGHT);
        styleContent.setBorderBottom(BorderStyle.THIN);
        styleContent.setBorderLeft(BorderStyle.THIN);
        styleContent.setBorderRight(BorderStyle.THIN);
        styleContent.setBorderTop(BorderStyle.THIN);
        // 生成一个字体
        Font font = workbook.createFont();
        font.setFontName("仿宋");
        font.setFontHeightInPoints((short) 11);
        font.setFontHeight(HSSFFont.U_DOUBLE);
        styleContent.setFont(font);
        // 粗体显示

        return styleContent;
    }

    /**
     * [简要描述]：获取正文内容样式</br>
     * [详细描述]：仿宋，自动换行，不加粗，居左</br>
     * [作者]：Administrator(2018-12-11)</br>
     *
     * @param workbook
     * @return
     */
    public static CellStyle getContentStyle(Workbook workbook) {
        // 生成正文样式
        CellStyle styleContent = workbook.createCellStyle();
        // 设置这些样式
        styleContent.setAlignment(HorizontalAlignment.CENTER);
        styleContent.setBorderBottom(BorderStyle.THIN);
        styleContent.setBorderLeft(BorderStyle.THIN);
        styleContent.setBorderRight(BorderStyle.THIN);
        styleContent.setBorderTop(BorderStyle.THIN);
        styleContent.setVerticalAlignment(VerticalAlignment.CENTER);
        // 生成一个字体
        Font font = workbook.createFont();
        font.setFontName("黑体");
        font.setFontHeightInPoints((short) 13);
        styleContent.setFont(font);
        // 自动换行
        styleContent.setWrapText(true);

        return styleContent;
    }

    public static CellStyle getTitleStyle(Workbook workbook) {
        // 生成正文样式
        CellStyle styleContent = workbook.createCellStyle();
        // 设置这些样式
/*        styleContent.setAlignment(CellStyle.ALIGN_LEFT);
        styleContent.setBorderBottom(CellStyle.BORDER_THIN);
        styleContent.setBorderLeft(CellStyle.BORDER_THIN);
        styleContent.setBorderRight(CellStyle.BORDER_THIN);
        styleContent.setBorderTop(CellStyle.BORDER_THIN);*/
        // 生成一个字体
        Font font = workbook.createFont();
        font.setFontName("仿宋");
        font.setFontHeightInPoints((short) 11);
        styleContent.setFont(font);
        // 自动换行
        styleContent.setWrapText(true);

        return styleContent;
    }

    public static CellStyle getTitleRightStyle(Workbook workbook) {
        // 生成正文样式
        CellStyle styleContent = workbook.createCellStyle();
        // 设置这些样式
        styleContent.setAlignment(HorizontalAlignment.RIGHT);
       /* styleContent.setBorderBottom(CellStyle.BORDER_THIN);
        styleContent.setBorderLeft(CellStyle.BORDER_THIN);
        styleContent.setBorderRight(CellStyle.BORDER_THIN);
        styleContent.setBorderTop(CellStyle.BORDER_THIN);*/
        // 生成一个字体
        Font font = workbook.createFont();
        font.setFontName("仿宋");
        font.setFontHeightInPoints((short) 11);
        styleContent.setFont(font);
        // 自动换行
        styleContent.setWrapText(true);

        return styleContent;
    }

    /**
     * [简要描述]：生成标题样式</br>
     * [详细描述]：居左，加粗</br>
     * [作者]：Administrator(2018-12-11)</br>
     *
     * @param workbook
     * @return
     */
    public static CellStyle getTitleLeftStyle(Workbook workbook) {
        // 生成父级科目样式
        CellStyle styleFkm = workbook.createCellStyle();
        // 设置这些样式
        styleFkm.setAlignment(HorizontalAlignment.CENTER);
        styleFkm.setBorderBottom(BorderStyle.THIN);
        styleFkm.setBorderLeft(BorderStyle.THIN);
        styleFkm.setBorderRight(BorderStyle.THIN);
        styleFkm.setBorderTop(BorderStyle.THIN);
        // 生成一个字体
        Font fontFkm = workbook.createFont();
        fontFkm.setFontName("仿宋");
        fontFkm.setFontHeightInPoints((short) 11);
        // 粗体显示
        fontFkm.setFontHeight(HSSFFont.U_DOUBLE);
        styleFkm.setFont(fontFkm);

        return styleFkm;
    }

    /**
     * [简要描述]：获取标题样式</br>
     * [详细描述]：加粗、居中</br>
     * [作者]：Administrator(2018-12-11)</br>
     *
     * @param workbook
     * @return
     */
    public static CellStyle getTitleCenterStyle(Workbook workbook) {
        // 生成父级科目样式
        CellStyle styleFkm = workbook.createCellStyle();
        // 设置这些样式
        styleFkm.setAlignment(HorizontalAlignment.CENTER);
        styleFkm.setBorderBottom(BorderStyle.THIN);
        styleFkm.setBorderLeft(BorderStyle.THIN);
        styleFkm.setBorderRight(BorderStyle.THIN);
        styleFkm.setBorderTop(BorderStyle.THIN);
        styleFkm.setVerticalAlignment(VerticalAlignment.CENTER);
        // 生成一个字体
        Font fontFkm = workbook.createFont();
        fontFkm.setFontName("仿宋");
        fontFkm.setFontHeightInPoints((short) 18);
        // 粗体显示
        fontFkm.setBold(true);
        styleFkm.setFont(fontFkm);

        return styleFkm;
    }

    /**
     * [简要描述]：获取标题样式</br>
     * [详细描述]：加粗、居中</br>
     * [作者]：Administrator(2018-12-11)</br>
     *
     * @param workbook
     * @return
     */
    public static CellStyle getZdTitleCenterStyle(Workbook workbook) {
        // 生成父级科目样式
        CellStyle styleFkm = workbook.createCellStyle();
        // 设置这些样式
        styleFkm.setAlignment(HorizontalAlignment.CENTER);
        styleFkm.setBorderBottom(BorderStyle.THIN);
        styleFkm.setBorderLeft(BorderStyle.THIN);
        styleFkm.setBorderRight(BorderStyle.THIN);
        styleFkm.setBorderTop(BorderStyle.THIN);
        // 生成一个字体
        Font fontFkm = workbook.createFont();
        fontFkm.setFontName("微软雅黑");
        fontFkm.setFontHeightInPoints((short) 11);
        // 粗体显示
        fontFkm.setFontHeight(HSSFFont.U_DOUBLE);
        styleFkm.setFont(fontFkm);

        return styleFkm;
    }

    /**
     * [简要描述]：只有底线</br>
     * [详细描述]：左对齐</br>
     * [作者]：aaron.wang(2018-12-18)</br>
     *
     * @param workbook
     * @return
     */
    public static CellStyle getBellowLineStyle(Workbook workbook) {
        // 生成正文样式
        CellStyle styleContent = workbook.createCellStyle();
        // 设置这些样式
        styleContent.setAlignment(HorizontalAlignment.LEFT);
        styleContent.setBorderBottom(BorderStyle.THIN);
        styleContent.setBorderLeft(BorderStyle.NONE);
        styleContent.setBorderRight(BorderStyle.NONE);
        styleContent.setBorderTop(BorderStyle.NONE);
        // 生成一个字体
        Font font = workbook.createFont();
        font.setFontName("仿宋");
        font.setFontHeightInPoints((short) 11);
        styleContent.setFont(font);

        return styleContent;
    }

    /**
     * [简要描述]：只有底线</br>
     * [详细描述]：右对齐</br>
     * [作者]：aaron.wang(2018-12-18)</br>
     *
     * @param workbook
     * @return
     */
    public static CellStyle getBellowLineStyleRight(Workbook workbook) {
        // 生成正文样式
        CellStyle styleContent = workbook.createCellStyle();
        // 设置这些样式
        styleContent.setAlignment(HorizontalAlignment.RIGHT);
        styleContent.setBorderBottom(BorderStyle.THIN);
        styleContent.setBorderLeft(BorderStyle.NONE);
        styleContent.setBorderRight(BorderStyle.NONE);
        styleContent.setBorderTop(BorderStyle.NONE);
        // 生成一个字体
        Font font = workbook.createFont();
        font.setFontName("仿宋");
        font.setFontHeightInPoints((short) 11);
        styleContent.setFont(font);

        return styleContent;
    }
}
