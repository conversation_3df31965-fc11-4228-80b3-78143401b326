package cn.microvideo.module.plyh.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: bpm流程调用表
 * @Author: spring-boot
 * @Date: 2023-09-05
 * @Version: V1.0
 */
@Data
@TableName("t_bpm_manage")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "t_bpm_manage对象", description = "bpm流程调用表")
public class BpmManage {
    /**
     * UUID
     */

    @ApiModelProperty(value = "UUID")
    @TableId("F_VC_UUID")
    private String uuid;
    /**
     * 流程定义ID
     */

    @ApiModelProperty(value = "流程定义ID")
    @TableField("F_VC_DEF_ID")
    private String defId;
    /**
     * 流程定义key
     */

    @ApiModelProperty(value = "流程定义key")
    @TableField("F_VC_DEF_KEY")
    private String defKey;
    /**
     * 模型ID
     */

    @ApiModelProperty(value = "模型ID")
    @TableField("F_VC_MODEL_ID")
    private String modelId;
    /**
     * 分类名称
     */

    @ApiModelProperty(value = "分类名称")
    @TableField("F_VC_TYPE_KEY")
    private String typeKey;
    /**
     * 附件ID
     */

    @ApiModelProperty(value = "附件ID")
    @TableField("F_VC_ATTACHMENT_ID")
    private String attachmentId;
    /**
     * 当前人员
     */

    @ApiModelProperty(value = "当前人员")
    @TableField("F_VC_CURRENT_USER_NAME")
    private String currentUserName;
    /**
     * 草稿ID
     */

    @ApiModelProperty(value = "草稿ID")
    @TableField("F_VC_DRAFT_ID")
    private String draftId;
    /**
     * 外挂表单
     */

    @ApiModelProperty(value = "外挂表单")
    @TableField("F_VC_EXTERNAL_URL")
    private String externalUrl;
    /**
     * 表单JSON
     */

    @ApiModelProperty(value = "表单JSON")
    @TableField("F_VC_FORM_DATA")
    private String formData;
    /**
     * 表单ID
     */

    @ApiModelProperty(value = "表单ID")
    @TableField("F_VC_FORM_ID")
    private String formId;
    /**
     * 分类ID
     */

    @ApiModelProperty(value = "分类ID")
    @TableField("F_VC_GROUP_ID")
    private String groupId;
    /**
     * 节点处理信息
     */

    @ApiModelProperty(value = "节点处理信息")
    @TableField("F_VC_NODE_INFOS")
    private String nodeInfos;
    /**
     * 意见
     */

    @ApiModelProperty(value = "意见")
    @TableField("F_VC_OPINION")
    private String opinion;
    /**
     * 类型标题
     */

    @ApiModelProperty(value = "类型标题")
    @TableField("F_VC_TYPE_TITLE")
    private String typeTitle;
    /**
     * 当前人员id
     */

    @ApiModelProperty(value = "当前人员id")
    @TableField("F_VC_CURRENT_USER_ID")
    private String currentUserId;
    /**
     * 流程引用key
     */

    @ApiModelProperty(value = "流程引用key")
    @TableField("F_VC_SOLE_KEY")
    private String soleKey;
}
