package cn.microvideo.module.plyh.core.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 值班部门人员管理表
 * @Author: spring-boot
 * @Date:   2024-02-23
 * @Version: V1.0
 */
@Data
@TableName("b_plyh_dept_user")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="b_plyh_dept_user对象", description="值班部门人员管理表")
public class DeptUser {
    
	/**UUID*/
	
    @ApiModelProperty(value = "UUID")
    @TableId("F_VC_ID")
	private String id;
	/**部门ID*/
	
    @ApiModelProperty(value = "部门ID")
    @TableField("F_VC_DEPT_ID")
	private String deptId;
	/**用户ID*/
	
    @ApiModelProperty(value = "用户ID")
    @TableField("F_VC_USER_ID")
	private String userId;
	/**用户姓名*/
	
    @ApiModelProperty(value = "用户姓名")
    @TableField("F_VC_USER_NAME")
	private String userName;
	/**用户手机*/
	
    @ApiModelProperty(value = "用户手机")
    @TableField("F_VC_USER_MOBILE")
	private String userMobile;
	/**排序*/
	
    @ApiModelProperty(value = "排序")
    @TableField("F_INT_SORT")
	private Integer sort;
	/**创建人*/
	
    @ApiModelProperty(value = "创建人")
    @TableField("F_VC_CREATE_BY")
	private String createBy;
	/**创建时间*/
	
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @TableField("F_DT_CREATE_TIME")
	private Date createTime;
	/**更新人*/
	
    @ApiModelProperty(value = "更新人")
    @TableField("F_VC_UPDATE_BY")
	private String updateBy;
	/**更新时间*/
	
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    @TableField("F_DT_UPDATE_TIME")
	private Date updateTime;
    /**删除标记（0正常 1删除）*/

    @ApiModelProperty(value = "删除标记（0正常 1删除）")
    @TableField("F_INT_DEL_FLAG")
    private Integer delFlag;

}
