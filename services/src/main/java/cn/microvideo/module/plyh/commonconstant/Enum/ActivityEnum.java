package cn.microvideo.module.plyh.commonconstant.Enum;


public enum ActivityEnum {

    ACT_KEY("yy", "流程实例"),
    APPLY("Apply", "申请"),
    RUNNING("running", "待办"),
    COMPING("comping", "已办"),
    FINISH("end", "结束");


    private String code;
    private String name;

    ActivityEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (ActivityEnum value : ActivityEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }


}
