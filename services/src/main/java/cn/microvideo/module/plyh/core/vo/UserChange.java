package cn.microvideo.module.plyh.core.vo;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <b>描述:</b>
 *
 * <p>用户切换单位数据类</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2020年7月1日上午10:13:42
 * @since JDK1.8
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode(of = {"deptId", "orgaId"})
public class UserChange {

    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 部门名
     */
    private String deptName;
    /**
     * 单位ID
     */
    private String orgaId;
    /**
     * 单位名
     */
    private String orgaName;

}
