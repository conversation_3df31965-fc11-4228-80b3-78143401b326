package cn.microvideo.module.plyh.core.service;

import cn.microvideo.module.plyh.core.entity.DutyDept;
import cn.microvideo.module.plyh.core.vo.DutyDeptUserVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 排班部门管理表
 * @Author: spring-boot
 * @Date:   2024-02-23
 * @Version: V1.0
 */
public interface IDutyDeptService extends IService<DutyDept> {


    /**
     *数据新增
     */
    public void saveByEntity(DutyDept dutyDept);


    /**
     * 更新数据
     * @param dutyDept
     * @return
     */
    public boolean updateByEntity(DutyDept dutyDept);

    /**
     * 根据formId获取数据
     * @param id
     * @return
     */
    public DutyDeptUserVO getByFormId(String id);

    /**
     * 数据分页查询
     * @param page
     * @param dutyDept
     * @return
     */
    public IPage<DutyDeptUserVO> pageList(Page<DutyDeptUserVO> page, DutyDept dutyDept);


    /**
     * 根据formID删除数据
     * @param id
     */
    public void removeByFormId(String id);


    /**
     * 全量查询
     * @param dutyDept
     * @return
     */
    public List<DutyDeptUserVO> queryList(DutyDept dutyDept);


    /**
     * 新增数据
     * @param dutyDeptUserVO
     */
    public void addWithUsers(DutyDeptUserVO dutyDeptUserVO);

    /**
     *更新数据
     * @param dutyDeptUserVO
     */
    public void updateWithUsers(DutyDeptUserVO dutyDeptUserVO);


    /**
     * 查询当前单位下的部门
     * @param orgId
     * @param holidayIds
     * @return
     */
    public List<DutyDept> selectByOrgIdFormIds(String orgId,List<String> holidayIds);

    /**
     * 获取当前单位下所有的值班部门
     * @param orgId
     * @return
     */
    public List<DutyDept> selectByOrgId(String orgId,Integer delFlag);


    /**
     * 根据holidayIds获取关联的部门
     * @return
     */
    public List<DutyDept> selectByHolidayId(String orgId,List<String> holidayIds);

    /**
     * 如果新增的部门
     * @param orgId
     * @param holidayIds
     * @return
     */
    public List<DutyDept> selectByHolidayIdWithUserDept(String orgId,List<String> holidayIds);


    void moveItem(List<String> orderedIds);

}
