package cn.microvideo.module.plyh.core.vo;

import cn.microvideo.module.plyh.core.entity.DutyRelease;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "AddDutyReleaseVO", description = "AddDutyReleaseVO")
public class AddDutyReleaseVO extends DutyRelease {
    @ApiModelProperty(value = "关联日期Id,多个用,隔开")
    private String dayIds;
}
