package cn.microvideo.module.plyh.core.vo;

import cn.microvideo.module.plyh.core.entity.DutyDept;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ActivityUserVO对象", description = "节点人员数据")
public class HolidayMonthVO {
    @ApiModelProperty(value = "日期、部门、人员列表")
    private List<HolidayDetailVO> holidayList;
    @ApiModelProperty(value = "部门列表")
    private List<DutyDept> deptList;
}
