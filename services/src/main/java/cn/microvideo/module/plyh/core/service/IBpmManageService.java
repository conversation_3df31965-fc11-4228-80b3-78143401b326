package cn.microvideo.module.plyh.core.service;

import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.module.plyh.core.entity.BpmManage;
import cn.microvideo.module.plyh.core.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: bpm流程调用表
 * @Author: spring-boot
 * @Date: 2023-09-05
 * @Version: V1.0
 */
public interface IBpmManageService extends IService<BpmManage> {
    /**
     * 启动流程并返回数据对象
     *
     * @return
     */
    public StratReturnVO startBpm(String formData, String signetId, MicrovideoSessionUser user);

    /**
     * 同意审批流程
     *
     * @param agreeActiviotyVO
     */
    public StratReturnVO agreeActivity(AgreeActiviotyVO agreeActiviotyVO, MicrovideoSessionUser user);

    /**
     * 根据流程实例ID查询下面配置的未执行环节及用户信息
     *
     * @param procId
     * @return
     */
    public List<ActivityProIdUserVO> smsUsersByProcId(String procId);


    /**
     * 获取可退回节点用户
     *
     * @param backNodeVO
     * @return
     */
    public List<ActivityProIdUserVO> getBackNodeUser(BackNodeVO backNodeVO);

    /**
     * 获取审批节点
     *
     * @return
     */
    public List<ActivityProIdUserVO> getSubNode(SubNodeVO subNodeVO);

    /**
     * 根据taskId获取可退回的节点
     *
     * @param taskId
     * @return
     */
    public List<ActivityBackVO> getBackNodes(String taskId);

    /**
     * 任务跳转（退回可以用这个)
     *
     * @param agreeActiviotyVO
     */
    public void freeJump(AgreeActiviotyVO agreeActiviotyVO, MicrovideoSessionUser user);

    /**
     * 会签退回
     *
     * @param agreeActiviotyVO
     * @param user
     */
    public void backHq(AgreeActiviotyVO agreeActiviotyVO, MicrovideoSessionUser user);


    /**
     * 根据流程ID查询数据
     *
     * @param proId
     * @return
     */
    public List<NodeOpinionVO> nodeOpinion(String proId);

    /**
     * 根据流程节点获取节点中的人员数据
     *
     * @param
     * @return
     */
    public List<NodeUserVO> queryAllUserByModelKey();

    /**
     * 撤销接口
     *
     * @param cancelVO
     * @param user
     * @return
     */
    public StratReturnVO cancel(AgreeActiviotyVO cancelVO, MicrovideoSessionUser user);


    /**
     * 任务中断-直接结束流程（暂不能用）
     *
     * @param cancelVO
     * @param user
     */
    public void terminate(AgreeActiviotyVO cancelVO, MicrovideoSessionUser user);

    /**
     * 任务转办
     *
     * @param cancelVO
     * @param user
     */
    public void transfer(AgreeActiviotyVO cancelVO, MicrovideoSessionUser user);

    /**
     * @param id
     */
    public String smsUsersByTaskId(String id);


}
