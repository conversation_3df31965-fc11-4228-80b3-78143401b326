package cn.microvideo.module.plyh.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ActivityUserVO对象", description = "节点人员数据")
public class ActivityUserVO {
    //用户编号
    @ApiModelProperty(value = "用户编号")
    private String userNo;
    //用户名
    @ApiModelProperty(value = "用户编号")
    private String fullname;
    //管理员
    @ApiModelProperty(value = "是否是管理员")
    private boolean admin;
    //用户id
    @ApiModelProperty(value = "用户ID")
    private String id;
    //用户编号2
    @ApiModelProperty(value = "用户编号2")
    private String userNo2;
    //状态
    @ApiModelProperty(value = "状态")
    private boolean status;
}
