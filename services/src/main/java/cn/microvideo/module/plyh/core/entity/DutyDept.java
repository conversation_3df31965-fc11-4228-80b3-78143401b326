package cn.microvideo.module.plyh.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 排班部门管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Data
@TableName("b_plyh_duty_dept")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "b_plyh_duty_dept对象", description = "排班部门管理表")
public class DutyDept {

    /**
     * UUID
     */

    @ApiModelProperty(value = "UUID")
    @TableId("F_VC_ID")
    private String id;
    /**
     * 排班部门管理名称
     */

    @TableField("F_VC_DEPT_NAME")
    private String deptName;
    /**
     * 单位ID
     */

    @ApiModelProperty(value = "单位ID")
    @TableField("F_VC_ORG_ID")
    private String orgId;
    /**
     * 单位名称
     */

    @ApiModelProperty(value = "单位名称")
    @TableField("F_VC_ORG_NAME")
    private String orgName;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    @TableField("F_VC_CREATE_BY")
    private String createBy;
    /**
     * 创建时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @TableField("F_DT_CREATE_TIME")
    private Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    @TableField("F_VC_UPDATE_BY")
    private String updateBy;
    /**
     * 更新时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    @TableField("F_DT_UPDATE_TIME")
    private Date updateTime;

    /**
     * 删除标记（0正常 1删除）
     */

    @ApiModelProperty(value = "删除标记（0正常 1删除）")
    @TableField("F_INT_DEL_FLAG")
    private Integer delFlag;

    @ApiModelProperty(value = "删除标记（1正常展示  0不展示）")
    @TableField("F_INT_DISPLAY_PHONE")
    private Integer displayPhone;


    @ApiModelProperty(value = "排序")
    @TableField("F_INT_SORT")
    private Integer sort;
}
