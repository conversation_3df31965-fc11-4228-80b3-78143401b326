package cn.microvideo.qsc.client.utils;

import com.thoughtworks.xstream.XStream;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class XStreamManager {

    private static Map<String, XStream> map = new ConcurrentHashMap();

    public static XStream getxStream(String key ){
        return map.get(key);
    }

    public static void setXStream(String key,XStream stream){
        map.put(key,stream);
    }


}
