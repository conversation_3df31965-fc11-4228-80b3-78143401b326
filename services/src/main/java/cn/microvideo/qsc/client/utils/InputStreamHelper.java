package cn.microvideo.qsc.client.utils;

import javax.net.ssl.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Iterator;
import java.util.Map;

/**
 * 根据web服务获取流
 *
 * <AUTHOR>
 */
public class InputStreamHelper {

    private final static int itimeout = 60 * 1000;


    public static InputStream getInputStream(String service, RequestMethod method) {
        return getInputStream(service, method, null, "");
    }

    private static final class DefaultTrustManager implements X509TrustManager {

        public void checkClientTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {

        }

        public void checkServerTrusted(X509Certificate[] arg0, String arg1) throws CertificateException {
        }

        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }

    }


    /**
     * 根据web服务获取流
     *
     * @param service 服务地址
     * @param method  请求方法
     * @return InputStream
     */
    public static InputStream getInputStream(String service, RequestMethod method, Map<String, String> params,
                                             String xml) {
        HttpURLConnection conn = null;
        InputStream in = null;
        OutputStream outputStream = null;
        try {


            String strParam = codeParam(params, service);
            if (strParam.length() > 0) {
                service = service + (service.contains("?") ? "&" : "?") + strParam;
            }
            conn = getConnection(service);
//			System.out.println(service);
            conn.setConnectTimeout(2000);
            conn.setReadTimeout(itimeout);
            conn.setRequestMethod(method.getValue());
            conn.setRequestProperty("Connection", "close");


            if (method == RequestMethod.POST && xml.length() > 0) {
                byte[] bb = xml.getBytes();
                conn.setDoInput(true);
                conn.setDoOutput(true);// 如果通过post提交数据，必须设置允许对外输出数据
                conn.setRequestProperty("Content-Type", "text/xml; charset=UTF-8");

                //conn.setRequestProperty("Content-Length", String.valueOf(bb.length));
                outputStream = conn.getOutputStream();
                outputStream.write(xml.getBytes("UTF-8"));

            }

            conn.connect();
            in = conn.getInputStream();

            HttpConnectionManager.setConnection(conn);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {

                try {
                    outputStream.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return in;
    }


    public static String getXMLStr(String service, RequestMethod method, Map<String, String> params,
                                   String xml) {
        HttpURLConnection conn = null;

        OutputStream outputStream = null;
        String res = null;
        try {


            String strParam = codeParam(params, service);
            if (strParam.length() > 0) {
                service = service + (service.contains("?") ? "&" : "?") + strParam;
            }
            conn = getConnection(service);
//			System.out.println(service);
            conn.setConnectTimeout(2000);
            conn.setReadTimeout(itimeout);
            conn.setRequestMethod(method.getValue());

            if (method == RequestMethod.POST && xml.length() > 0) {
                byte[] bb = xml.getBytes();
                conn.setDoInput(true);
                conn.setDoOutput(true);// 如果通过post提交数据，必须设置允许对外输出数据
                conn.setRequestProperty("Content-Type", "text/xml; charset=UTF-8");

                //conn.setRequestProperty("Content-Length", String.valueOf(bb.length));
                outputStream = conn.getOutputStream();
                outputStream.write(xml.getBytes("UTF-8"));

            }

            conn.connect();
            //in = conn.getInputStream();
            StringBuilder response;

            try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                String inputLine;
                response = new StringBuilder();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
            }

            return response.toString();


            //HttpConnectionManager.setConnection(conn);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {

                try {
                    outputStream.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return "";
    }


    /**
     * 根据web服务获取json数据
     *
     * @param service 服务地址
     * @param method  请求方法
     * @return InputStream
     * <AUTHOR>
     * @date 20160811
     * @since 编码格式固定utf-8 杜静 20160825
     */
    public static String loadJSON(String service, RequestMethod method, Map<String, String> params, String xml) {
        StringBuilder json = new StringBuilder();
        try {
            String strParam = codeParam(params, service);
            // 去除method == RequestMethod.GET
            if (strParam.length() > 0) {
                service = service + (service.contains("?") ? "&" : "?") + strParam;
            }
            URLConnection yc = getConnection(service);
            if (method == RequestMethod.POST && xml.length() > 0) {
                byte[] bb = xml.getBytes();
                yc.setDoInput(true);
                yc.setDoOutput(true);// 如果通过post提交数据，必须设置允许对外输出数据
                if (xml.contains("<")) {
                    yc.setRequestProperty("Content-Type", "application/xml; charset=UTF-8");
                } else {
                    yc.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
                }

                yc.setRequestProperty("Content-Length", String.valueOf(bb.length));
                yc.getOutputStream().write(xml.getBytes("UTF-8"));
            }
            String httpState = yc.getHeaderField(0);
            if (!httpState.contains("200")) {
                return null;
            }
            // ((HttpURLConnection) yc).setRequestMethod(method.getValue());
            BufferedReader in = new BufferedReader(new InputStreamReader(yc.getInputStream(), "utf-8"));
            String inputLine = null;
            try {
                while ((inputLine = in.readLine()) != null) {
                    json.append(inputLine);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            in.close();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return json.toString();
    }

    /**
     * 获取http 连接
     *
     * @param service
     * @return
     * @throws IOException
     * <AUTHOR>
     * @date 2021年1月28日 下午7:22:03
     */
    private static HttpURLConnection getConnection(String service) throws IOException {
        HttpURLConnection conn = null;
        service = service.replaceAll("\\+", "%2B");
        URL url = new URL(service);
        if (service.startsWith("https://")) {
            SSLContext ctx = null;
            try {
                ctx = SSLContext.getInstance("TLS");
                ctx.init(new KeyManager[0], new TrustManager[]{new DefaultTrustManager()}, new SecureRandom());
            } catch (KeyManagementException | NoSuchAlgorithmException e) {
                e.printStackTrace();
            }
            SSLSocketFactory ssf = null;
            if (ctx != null) {
                ssf = ctx.getSocketFactory();
            }
            HttpsURLConnection httpsConn = (HttpsURLConnection) url.openConnection();
            httpsConn.setSSLSocketFactory(ssf);
            httpsConn.setHostnameVerifier(new HostnameVerifier() {
                public boolean verify(String arg0, SSLSession arg1) {
                    return true;
                }
            });
            conn = httpsConn;
        } else {
            conn = (HttpURLConnection) url.openConnection();
        }
        return conn;
    }

    private static String codeParam(Map<String, String> params, String service) {
        StringBuilder buidler = new StringBuilder();
        // 添加token信息
        addTokenInfo(buidler, service);
        if (params != null) {
            Iterator<String> keys = params.keySet().iterator();
            String key;
            while (keys.hasNext()) {
                key = keys.next();
                String v = params.get(key);
//				if (StringUtils.isNotBlank(v)) {
                buidler.append(key).append("=").append(v).append("&");
//				}
            }

            if (buidler.length() > 0)
                buidler.setLength(buidler.length() - 1);
        }
        return buidler.toString();
    }

    private static void addTokenInfo(StringBuilder buidler, String service) {
        if (service.contains("service/getToken")) {
            return;
        }
        String accessToken = QSAccessToken.getInstance().getToken();
        if (accessToken != null) {
            buidler.append("access_token").append("=").append(accessToken).append("&");
        }
    }

    /**
     * 根据web服务获取json数据
     *
     * @param service   服务地址
     * @param method    请求方法
     * @param params    传参
     * @param headerMap 头部信息
     * @return 执行结果
     * <AUTHOR>
     * @date 20230808
     */
    public static String loadJSON(String service, RequestMethod method, Map<String, String> params, String xml,
                                  Map<String, String> headerMap) {
        StringBuilder json = new StringBuilder();
        try {
            String strParam = codeParam(params, service);
            // 去除method == RequestMethod.GET
            if (strParam.length() > 0) {
                service = service + (service.contains("?") ? "&" : "?") + strParam;
            }
            URLConnection yc = getConnection(service);
            if (method == RequestMethod.POST && xml.length() > 0) {
                byte[] bb = xml.getBytes();
                yc.setDoInput(true);
                yc.setDoOutput(true);// 如果通过post提交数据，必须设置允许对外输出数据
                if (xml.contains("<")) {
                    yc.setRequestProperty("Content-Type", "application/xml; charset=UTF-8");
                } else {
                    yc.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
                }
                yc.setRequestProperty("Content-Length", String.valueOf(bb.length));
            }
            if (headerMap != null && headerMap.size() > 0) {
                for (Object o : headerMap.keySet()) {
                    yc.setRequestProperty((String) o, headerMap.get(o));
                }
            }
            if (method == RequestMethod.POST && xml.length() > 0) {
                yc.getOutputStream().write(xml.getBytes("UTF-8"));
            }

            String httpState = yc.getHeaderField(0);
            if (!httpState.contains("200")) {
                return null;
            }
            // ((HttpURLConnection) yc).setRequestMethod(method.getValue());
            BufferedReader in = new BufferedReader(new InputStreamReader(yc.getInputStream(), "utf-8"));
            String inputLine = null;
            try {
                while ((inputLine = in.readLine()) != null) {
                    json.append(inputLine);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            in.close();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return json.toString();
    }
}
