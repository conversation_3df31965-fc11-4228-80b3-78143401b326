<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.microvideo.module.plyh.core.mapper.DutyRosterMapper">

    <!-- 按月份分页查询值班表及排班时间 -->
    <select id="queryPageListByMonth" resultType="cn.microvideo.module.plyh.core.vo.DutyRosterVO">
        SELECT
            dr.F_VC_ID as id,
            dr.<PERSON>_VC_TITLE as title,
            dr.F_VC_ORG_ID as orgId,
            dr.F_VC_ORG_NAME as orgName,
            dr.F_INT_SORT as sort,
            GROUP_CONCAT(
                drs.F_DT_SCHEDULE_TIME
                ORDER BY drs.F_DT_SCHEDULE_TIME ASC
                SEPARATOR ','
            ) as scheduleTimesStr
        FROM t_duty_rosters dr
        INNER JOIN t_duty_roster_schedules drs ON dr.F_VC_ID = drs.F_VC_ROSTER_ID
        WHERE dr.F_INT_DEL_FLAG = 0
        AND dr.F_VC_ORG_ID = #{orgId}
        AND DATE_FORMAT(drs.F_DT_SCHEDULE_TIME, '%Y-%m') = #{month}
        GROUP BY dr.F_VC_ID, dr.F_VC_TITLE, dr.F_VC_ORG_ID, dr.F_VC_ORG_NAME, dr.F_INT_SORT, dr.F_DT_CREATE_TIME
        ORDER BY dr.F_DT_CREATE_TIME DESC
    </select>

</mapper>
