<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.microvideo.module.plyh.core.mapper.DutyDeptMapper">
    <select id="pageList" resultType="cn.microvideo.module.plyh.core.vo.DutyDeptUserVO">
        select bpdd.F_VC_ID as id,
        bpdd.F_VC_DEPT_NAME as deptName,
        bpdd.F_VC_ORG_ID as orgId,
        bpdd.F_VC_ORG_NAME as orgName,
        bpdd.F_VC_CREATE_BY as createBy,
        bpdd.F_DT_CREATE_TIME as createTime,
        bpdd.F_VC_UPDATE_BY as updateBy,
        bpdd.F_DT_UPDATE_TIME as updateTime,
        bpdd.F_INT_DEL_FLAG as delFlag
        from b_plyh_duty_dept bpdd
        where bpdd.F_INT_DEL_FLAG = 0
        <if test="query.deptName != null and query.deptName != ''">
            and bpdd.F_VC_DEPT_NAME like CONCAT('%',#{query.deptName},'%')
        </if>
        <if test="query.orgId != null and query.orgId != ''">
            and bpdd.F_VC_ORG_ID = #{query.orgId}
        </if>
        order by bpdd.F_DT_CREATE_TIME desc
    </select>

    <select id="queryList" resultType="cn.microvideo.module.plyh.core.vo.DutyDeptUserVO">
        select bpdd.F_VC_ID as id,
        bpdd.F_VC_DEPT_NAME as deptName,
        bpdd.F_VC_ORG_ID as orgId,
        bpdd.F_VC_ORG_NAME as orgName,
        bpdd.F_VC_CREATE_BY as createBy,
        bpdd.F_DT_CREATE_TIME as createTime,
        bpdd.F_VC_UPDATE_BY as updateBy,
        bpdd.F_DT_UPDATE_TIME as updateTime,
        bpdd.F_INT_DEL_FLAG as delFlag
        from b_plyh_duty_dept bpdd
        where bpdd.F_INT_DEL_FLAG = 0
        <if test="query.deptName != null and query.deptName != ''">
            and tpmr.F_VC_DEPT_NAME like CONCAT('%',#{query.deptName},'%')
        </if>
        <if test="query.orgId != null and query.orgId != ''">
            and bpdd.F_VC_ORG_ID = #{query.orgId}
        </if>
        order by bpdd.F_DT_CREATE_TIME desc
    </select>


    <select id="selectByHolidayId" resultType="cn.microvideo.module.plyh.core.entity.DutyDept">
        select bpdd.F_VC_ID as id,
        bpdd.F_VC_DEPT_NAME as deptName,
        bpdd.F_VC_ORG_ID as orgId,
        bpdd.F_VC_ORG_NAME as orgName,
        bpdd.F_VC_CREATE_BY as createBy,
        bpdd.F_DT_CREATE_TIME as createTime,
        bpdd.F_VC_UPDATE_BY as updateBy,
        bpdd.F_DT_UPDATE_TIME as updateTime,
        bpdd.F_INT_DEL_FLAG as delFlag
        from b_plyh_duty_dept bpdd
        where bpdd.F_VC_ORG_ID = #{orgId}
        and bpdd.F_VC_ID in (
        select bphdu.F_VC_DUTY_DEPT_ID
        from b_plyh_holiday_dept_user bphdu
        where bphdu.F_VC_HOLIDAY_ID
        in
        <foreach collection="holidayIds" item="holidayIds" index="index" open="(" close=")"
                 separator=",">
            #{holidayIds}
        </foreach>
        )
        order by bpdd.F_DT_CREATE_TIME asc
    </select>

    <select id="selectByHolidayIdWithUserDept" resultType="cn.microvideo.module.plyh.core.entity.DutyDept">
        select bpdd.F_VC_ID as id,
        bpdd.F_VC_DEPT_NAME as deptName,
        bpdd.F_VC_ORG_ID as orgId,
        bpdd.F_VC_ORG_NAME as orgName,
        bpdd.F_VC_CREATE_BY as createBy,
        bpdd.F_DT_CREATE_TIME as createTime,
        bpdd.F_VC_UPDATE_BY as updateBy,
        bpdd.F_DT_UPDATE_TIME as updateTime,
        bpdd.F_INT_DEL_FLAG as delFlag
        from b_plyh_duty_dept bpdd
        where bpdd.F_VC_ORG_ID = #{orgId}
        and bpdd.F_INT_DEL_FLAG =1
        and bpdd.F_VC_ID in (
        select bphdu.F_VC_DUTY_DEPT_ID
        from b_plyh_holiday_dept_user bphdu
        where bphdu.F_VC_HOLIDAY_ID
        in
        <foreach collection="holidayIds" item="holidayIds" index="index" open="(" close=")"
                 separator=",">
            #{holidayIds}
        </foreach>
        )
        union
        
        select bpdd.F_VC_ID as id,
        bpdd.F_VC_DEPT_NAME as deptName,
        bpdd.F_VC_ORG_ID as orgId,
        bpdd.F_VC_ORG_NAME as orgName,
        bpdd.F_VC_CREATE_BY as createBy,
        bpdd.F_DT_CREATE_TIME as createTime,
        bpdd.F_VC_UPDATE_BY as updateBy,
        bpdd.F_DT_UPDATE_TIME as updateTime,
        bpdd.F_INT_DEL_FLAG as delFlag
        from b_plyh_duty_dept bpdd
        where bpdd.F_VC_ORG_ID = #{orgId}
        and bpdd.F_INT_DEL_FLAG =0

        order by createTime asc
    </select>

    <select id="findTopByOrderBySortOrderDesc" resultType="int">
        SELECT
            MAX(F_INT_SORT)
        FROM
            b_plyh_duty_dept
        WHERE
            F_VC_ORG_ID = #{groupId,jdbcType=VARCHAR}


    </select>
</mapper>