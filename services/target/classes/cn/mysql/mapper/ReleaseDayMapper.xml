<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.microvideo.module.plyh.core.mapper.ReleaseDayMapper">
    <select id="selectByReleaseIdsHoliday" resultType="cn.microvideo.module.plyh.core.vo.ReleaseHolidayVO">
        select bprd.F_VC_RELEASE_ID as releaseId,
        bph.F_DT_HOLIDAY_DATA as holidayData,
        bph.F_INT_HOLIDAY_TYPE as holidayType,
        bph.F_VC_HOLIDAY_NAME as holidayName
        from b_plyh_release_day bprd
        left join b_plyh_holiday bph on
        bprd.F_VC_HOLIDAY_ID = bph.F_VC_ID
        where 1=1
        <if test="ids != null ">
            and bprd.F_VC_RELEASE_ID in
            <foreach collection="ids" item="ids" index="index" open="(" close=")"
                     separator=",">
                #{ids}
            </foreach>
        </if>
        order by bph.F_DT_HOLIDAY_DATA asc

    </select>


    <select id="selectByReleaseId" resultType="cn.microvideo.module.plyh.core.entity.Holiday">
        select bph.F_VC_ID            as id,
               bph.F_DT_HOLIDAY_DATA  as holidayData,
               bph.F_INT_HOLIDAY_TYPE as holidayType,
               bph.F_VC_HOLIDAY_NAME  as holidayName
        from b_plyh_holiday bph
        where bph.F_VC_ID in (
            select bprd.F_VC_HOLIDAY_ID
            from b_plyh_release_day bprd
            where bprd.F_VC_RELEASE_ID = #{id})
        order by bph.F_DT_HOLIDAY_DATA asc

    </select>


</mapper>