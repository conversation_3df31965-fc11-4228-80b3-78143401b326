package cn.microvideo.qsc.client.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.HttpURLConnection;

@Slf4j
public class HttpConnectionManager {


    // 使用 ThreadLocal 存储每个线程的 HttpURLConnection
    private static final ThreadLocal<HttpURLConnection> connectionHolder = ThreadLocal.withInitial(() -> null);

    // 获取当前线程的 HttpURLConnection
    public static HttpURLConnection getConnection() {
        return connectionHolder.get();
    }

    // 设置当前线程的 HttpURLConnection
    public static void setConnection(HttpURLConnection connection) {
        connectionHolder.set(connection);
    }

    // 清除当前线程的 HttpURLConnection（可选，通常在 finally 块中调用）
    public static void clearConnection() {
        connectionHolder.remove();
    }


    // 辅助方法：确保连接被关闭
    public static void closeConnection() {
        HttpURLConnection connection = getConnection();
        if (connection != null) {
            log.info("压测问题测试,执行关闭connect操作，当前线程id为:{}",Thread.currentThread());
            connection.disconnect();
            clearConnection();
        }
    }



}
