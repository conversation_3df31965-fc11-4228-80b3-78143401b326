//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.microvideo.qsc.client.api.impl;

import cn.microvideo.AesEncrypting.AESEncrypting;
import cn.microvideo.qsc.client.api.impl.entitybuilder.*;
import cn.microvideo.qsc.client.entity.User;
import cn.microvideo.qsc.client.entity.*;
import cn.microvideo.qsc.client.entity.xml.*;
import cn.microvideo.qsc.client.utils.*;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.util.JSONUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;

@Slf4j
public class QsApiImpl {
    private String qsUrl = "http://192.168.200.8:8080/Qsecurity/";
    private String dataType_tree = "tree";
    private static final String AES_KEY = "MicroVideoAESSec";
    private String APPID = "appId";
    private String APPAESSTR = "aesStr";
    private String TIMESTAMP = "timestamp";
    private String MODIFIERID = "modifierId";
    private ObjectMapper jobjectMapper = new ObjectMapper();



    public QsApiImpl() {
    }

    public void setQsUrl(String qsUrl) {
        this.qsUrl = qsUrl;
        this.jobjectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        this.jobjectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        this.jobjectMapper.configure(SerializationFeature.INDENT_OUTPUT, true);
    }

    private Document getDoc(String service, RequestMethod method, Map<String, String> params) {

        InputStream in = InputStreamHelper.getInputStream(service, RequestMethod.GET, params, "");
        Document doc = SAXHelper.getDocument(in);

        if( in != null ){
            try {
                log.info("压测问题测试,执行关闭流操作，当前线程id为:{}",Thread.currentThread());
                in.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        HttpConnectionManager.closeConnection();



        return doc;
    }



    private Document getDoc_forPost(String service, RequestMethod method, String xml) {
        InputStream in = InputStreamHelper.getInputStream(service, RequestMethod.POST, (Map)null, xml);
        Document doc = SAXHelper.getDocument(in);


        if( in != null ){
            try {
                log.info("压测问题测试,执行关闭流操作，当前线程id为:{}",Thread.currentThread());
                in.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        HttpConnectionManager.closeConnection();

        return doc;
    }

    private Document getDoc_forPost(String service, RequestMethod method, Map<String, String> params) {
        InputStream in = InputStreamHelper.getInputStream(service, RequestMethod.POST, params, "");
        Document doc = SAXHelper.getDocument(in);

        if( in != null ){
            try {
                log.info("压测问题测试,执行关闭流操作，当前线程id为:{}",Thread.currentThread());
                in.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        HttpConnectionManager.closeConnection();

        return doc;
    }

    public String inputStream2String(InputStream is) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();


        int i;
        while((i = is.read()) != -1) {
            baos.write(i);
        }

        return baos.toString();
    }

    public Result addPerson(User user) {
        user = this.packMobile(user);
        String service = this.qsUrl + "service/addPerson";
        String conent = this.stitchingUserXml(user);
        System.err.println(conent);
        Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
        IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
        return (Result)builder.getEntity();
    }

    public User packMobile(User user) {
        if (user.getMobile1() != null && !user.getMobile1().isEmpty()) {
            if (user.getMobile() != null && !user.getMobile().isEmpty()) {
                user.setMobile(user.getMobile() + "," + user.getMobile1());
            } else {
                user.setMobile(user.getMobile1());
            }
        }

        return user;
    }

    public Result delPersonByUid(String uid, String uuid, String modifierId) {
        String service = this.qsUrl + "service/delPersonByUid";
        Map<String, String> params = new HashMap();
        params.put("uid", uid);
        params.put("uuid", uuid);
        params.put("modifierId", modifierId);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
        return (Result)builder.getEntity();
    }

    public Result modifyPerson(User user) {
        String service = this.qsUrl + "service/modifyPerson";
        String conent = this.stitchingUserXml(user);
        Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
        IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
        return (Result)builder.getEntity();
    }

    public Result modifyPerson(cn.microvideo.qsc.client.entity.xml.User user) {
        String service = this.qsUrl + "service/modifyPerson";
        String conent = this.stitchingUserXml(user);
        Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
        IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
        return (Result)builder.getEntity();
    }

    public Result modifyPersonOnlyBaseInfo(User user) {
        String service = this.qsUrl + "service/modifyPersonOnlyBaseInfo";
        String conent = this.stitchingUserXmlOnlyBaseInfo(user);
        Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
        IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
        return (Result)builder.getEntity();
    }

    public Result modifyPersonOnlyBaseInfo(cn.microvideo.qsc.client.entity.xml.User user) {
        String service = this.qsUrl + "service/modifyPersonOnlyBaseInfo";
        String conent = this.stitchingUserXmlOnlyBaseInfo(user);
        Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
        IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
        return (Result)builder.getEntity();
    }

    public Result addPersonAndRole(List<String> users, List<String> roles) {
        if (users != null && users.size() > 0 && roles != null && roles.size() > 0) {
            String service = this.qsUrl + "service/addPersonAndRole";
            String conent = this.stitchingRoleUserXml(users, roles);
            Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
            IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
            return (Result)builder.getEntity();
        } else {
            Result res = new Result(false, "用户集合或角色集合为空");
            return res;
        }
    }

    public Result modifyMsnByUid(String uid, String msn) {
        if (uid != null && !uid.isEmpty() && msn != null && !msn.isEmpty()) {
            String service = this.qsUrl + "service/modifyMsnByUid";
            User user = new User();
            user.setUid(uid);
            user.setMsn(msn);


            String key = "modifyMsnByUid";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("User", User.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            String conent = xstream.toXML(user);
            Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
            IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
            return (Result)builder.getEntity();
        } else {
            Result res = new Result(false, "参数传入错误");
            return res;
        }
    }

    public Result modifyPwd(String uid, String oldPwd, String newPwd) {
        return this.modifyPwd(uid, oldPwd, newPwd, false);
    }

    public Result modifyPwd(String uid, String oldPwd, String newPwd, boolean ignorComplexityVerify) {
        if (uid != null && !uid.isEmpty() && oldPwd != null && !oldPwd.isEmpty() && newPwd != null && !newPwd.isEmpty()) {
            String service = this.qsUrl + "service/modifyPwd";
            service = service + "?ignorComplexityVerify=" + ignorComplexityVerify;
            PWD pwd = new PWD(uid, oldPwd, newPwd);


            String key = "modifyPwd";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("PWD", PWD.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            String conent = xstream.toXML(pwd);
            Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
            IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
            return (Result)builder.getEntity();
        } else {
            Result res = new Result(false, "传入参数有误");
            return res;
        }
    }

    public Result updatePwd(String uid, String newPwd, boolean ignorComplexityVerify) {
        if (uid != null && !uid.isEmpty() && newPwd != null && !newPwd.isEmpty()) {
            String service = this.qsUrl + "service/updatePwd";
            Map<String, String> params = new HashMap();
            params.put("uid", uid);
            params.put("pwd", newPwd);
            params.put("ignorComplexityVerify", String.valueOf(ignorComplexityVerify));
            Document doc = this.getDoc_forPost(service, RequestMethod.POST, (Map)params);
            IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
            return (Result)builder.getEntity();
        } else {
            Result res = new Result(false, "传入参数有误");
            return res;
        }
    }

    public User getPersonByPid(String pid) {
        String service = this.qsUrl + "getPersonByPid";
        Map<String, String> params = new HashMap();
        params.put("pid", pid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<User> builder = (new GetPersonByPidBuilder()).setDocumnet(doc);
        return (User)builder.getEntity();
    }

    public User getPersonByPid(String pid, String sys) {
        String service = this.qsUrl + "getPersonByPid";
        Map<String, String> params = new HashMap();
        params.put("pid", pid);
        params.put("sys", sys);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<User> builder = (new GetPersonByPidBuilder()).setDocumnet(doc);
        return (User)builder.getEntity();
    }

    public User getPersonByPidWithPwd(String uid) {
        User u = new User();
        String service = this.qsUrl + "service/findPersonByUidWithPwd";

        try {
            Map<String, String> params = new HashMap();
            params.put("uid", uid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    u = (User)objectMapper.convertValue(info.getResult(), User.class);
                }
            }
        } catch (Exception var10) {
            Exception e = var10;
            e.printStackTrace();
        } finally {
            ;
        }

        return u;
    }

    public cn.microvideo.qsc.client.entity.xml.User getPersonByUid(String uid) {
        String service = this.qsUrl + "getPersonByPid";
        System.err.println("getPersonByUid::" + service);
        Map<String, String> params = new HashMap();
        params.put("pid", uid);
        System.out.println(uid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<cn.microvideo.qsc.client.entity.xml.User> builder = (new GetPersonByUidBuilder()).setDocumnet(doc);
        return (cn.microvideo.qsc.client.entity.xml.User)builder.getEntity();
    }

    public List<User> findPersonByOrganizationId(String orgId) {
        List<User> userList = null;
        String service = this.qsUrl + "service/findPersonByOrganizationId";
        Map<String, String> params = new HashMap();

        try {
            params.put("orgId", orgId);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (objectMapper.readTree(str).get("result") != null) {
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                    };
                    if (info.getResult() != null) {
                        userList = new ArrayList();
                        List<User> list = (List)objectMapper.readValue(result, valueTypeRef);

                        User u;
                        for(Iterator var12 = list.iterator(); var12.hasNext(); userList.add(u)) {
                            u = (User)var12.next();
                            if (u.getMobile() != null && u.getMobile() != null && !u.getMobile().isEmpty() && u.getMobile().indexOf(",") > 0) {
                                int i = u.getMobile().indexOf(",");
                                u.setMobile1(u.getMobile().substring(i + 1, u.getMobile().length()));
                                u.setMobile(u.getMobile().substring(0, i));
                            }
                        }
                    }
                }
            }
        } catch (Exception var16) {
            Exception e = var16;
            e.printStackTrace();
        } finally {
            ;
        }

        return userList;
    }

    public int findPersonCountByOrgId(String orgId) {
        int result = 0;
        String service = this.qsUrl + "service/findPersonCountByOrgId";
        Map<String, String> params = new HashMap();

        try {
            params.put("orgId", orgId);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    result = Integer.valueOf(info.getResult().toString());
                }
            }
        } catch (Exception var10) {
            Exception e = var10;
            e.printStackTrace();
        } finally {
            ;
        }

        return result;
    }

    public OrganizationInfo getDepartmentByDid(String did) {
        String service = this.qsUrl + "getDepartmentByDid";
        Map<String, String> params = new HashMap();
        params.put("did", did);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<OrganizationInfo> builder = (new GetDepartmentByDidBuilder()).setDocumnet(doc);
        return (OrganizationInfo)builder.getEntity();
    }

    public List<OrganizationInfo> getDepartmentByOid(String oid) {
        String service = this.qsUrl + "getDepartmentByOid";
        Map<String, String> params = new HashMap();
        params.put("oid", oid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<List<OrganizationInfo>> builder = (new GetDepartmentByOidBuilder()).setDocumnet(doc);
        return (List)builder.getEntity();
    }

    public List<OrganizationInfo> getOrganizationAll() {
        String service = this.qsUrl + "getOrganizationAll";
        Map<String, String> params = new HashMap();
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<List<OrganizationInfo>> builder = (new GetOrganizationAllBuilder()).setDocumnet(doc);
        return (List)builder.getEntity();
    }

    public OrganizationInfo getOrganizationByOid(String oid) {
        String service = this.qsUrl + "getOrganizationByOid";
        Map<String, String> params = new HashMap();
        params.put("oid", oid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<OrganizationInfo> builder = (new GetOrganizationByOidBuilder()).setDocumnet(doc);
        return (OrganizationInfo)builder.getEntity();
    }

    public ParentOrganizationInfo findOrganizationById(String id) {
        String service = this.qsUrl + "service/findOrganizationById";
        Map<String, String> params = new HashMap();
        params.put("id", id);
        Document doc = this.getDoc(service, RequestMethod.GET, params);


        String key = "findOrganizationById";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentOrganizationInfo.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }



        new ParentOrganizationInfo();

        try {
            ParentOrganizationInfo info = (ParentOrganizationInfo)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var8) {
            Exception e = var8;
            e.printStackTrace();
            return null;
        }
    }

    public ParentOrganizations findOrganizationsByParentid(String id, boolean isAll, boolean showAddBook) {
        String service = this.qsUrl + "service/findOrganizationsByParentid";
        Map<String, String> params = new HashMap();
        params.put("id", id);
        params.put("isAll", String.valueOf(isAll));
        params.put("showAddBook", String.valueOf(showAddBook));
        Document doc = this.getDoc(service, RequestMethod.GET, params);


        String key = "findOrganizationsByParentid";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentOrganizations.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }



        new ParentOrganizations();

        try {
            ParentOrganizations info = (ParentOrganizations)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var10) {
            Exception e = var10;
            e.printStackTrace();
            return null;
        }
    }

    public ParentOrganizations findOrganizationByTel(String tel, String type) {
        String service = this.qsUrl + "service/findOrganizationByTel";
        Map<String, String> params = new HashMap();
        params.put("type", type);
        params.put("tel", tel);
        Document doc = this.getDoc(service, RequestMethod.GET, params);


        String key = "findOrganizationByTel";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentOrganizations.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }


        new ParentOrganizations();

        try {
            ParentOrganizations info = (ParentOrganizations)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var9) {
            Exception e = var9;
            e.printStackTrace();
            return null;
        }
    }

    public User getPersonByPidNoPwd(String pid) {
        String service = this.qsUrl + "getPersonByPid";
        Map<String, String> params = new HashMap();
        params.put("pid", pid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<User> builder = (new GetPersonByPidBuilder()).setDocumnet(doc);
        User u = (User)builder.getEntity();
        if (u != null && u.getUid() != null) {
            u.setPassWord("");
        }

        return u;
    }

    public List<User> getPersonsByDid(String did) {
        String service = this.qsUrl + "getPersonsByDid";
        Map<String, String> params = new HashMap();
        params.put("did", did);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<List<User>> builder = (new GetPersonsByDidBuilder()).setDocumnet(doc);
        return (List)builder.getEntity();
    }

    public UserInfo loginService(String user, String password) {
        String service = this.qsUrl + "loginService";
        Map<String, String> params = new HashMap();
        params.put("user", user);
        params.put("password", password);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<UserInfo> builder = (new LoginServiceBuilder()).setDocumnet(doc);
        return (UserInfo)builder.getEntity();
    }

    public List<Person> getPersonByRid(String rid) {
        String service = this.qsUrl + "getPersonByRid";
        Map<String, String> params = new HashMap();
        params.put("rid", rid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<List<Person>> builder = (new GetPersonByRidBuilder()).setDocumnet(doc);
        return (List)builder.getEntity();
    }

    public ParentUsers findPersonsByTel(String tel) {
        String service = this.qsUrl + "service/findPersonsByTel";
        Map<String, String> params = new HashMap();
        params.put("tel", tel);
        Document doc = this.getDoc(service, RequestMethod.GET, params);


        String key = "findPersonsByTel";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentUsers.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }


        new ParentUsers();

        try {
            ParentUsers info = (ParentUsers)xstream.fromXML(doc.asXML());
            info = this.getMobile1(info);
            return info;
        } catch (Exception var8) {
            Exception e = var8;
            e.printStackTrace();
            return null;
        }
    }

    public ParentUsers findAllPersons() {
        String service = this.qsUrl + "service/findAllPersons";
        Map<String, String> params = new HashMap();
        Document doc = this.getDoc(service, RequestMethod.GET, params);




        String key = "findAllPersons";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentUsers.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }



        new ParentUsers();

        try {
            ParentUsers info = (ParentUsers)xstream.fromXML(doc.asXML());
            info = this.getMobile1(info);
            return info;
        } catch (Exception var7) {
            Exception e = var7;
            e.printStackTrace();
            return null;
        }
    }

    public ParentUsers getMobile1(ParentUsers info) {
        if (info != null && info.getUsers() != null && info.getUsers().getUser() != null && info.getUsers().getUser().size() > 0) {
            List<User> ulist = new ArrayList();

            User user;
            for(Iterator var4 = info.getUsers().getUser().iterator(); var4.hasNext(); ulist.add(user)) {
                user = (User)var4.next();
                if (user.getMobile() != null && user.getMobile() != null && !user.getMobile().isEmpty() && user.getMobile().indexOf(",") > 0) {
                    int i = user.getMobile().indexOf(",");
                    user.setMobile1(user.getMobile().substring(i + 1, user.getMobile().length()));
                    user.setMobile(user.getMobile().substring(0, i));
                }
            }

            Users users = new Users();
            users.setUser(ulist);
            info = new ParentUsers(users);
        }

        return info;
    }

    public String stitchingUserXml(User user) {
        String str = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>" + this.stitching4User2Xml(user);
        return str;
    }

    public String stitchingUserListXml(List<User> user) {
        StringBuffer strB = new StringBuffer("");
        if (user != null && user.size() > 0) {
            strB.append("<?xml version=\"1.0\" encoding=\"utf-8\" ?><UserList>");
            Iterator var4 = user.iterator();

            while(var4.hasNext()) {
                User user2 = (User)var4.next();
                strB.append(this.stitching4User2Xml(user2));
            }

            strB.append("</UserList>");
        }

        return strB.toString();
    }

    public String stitching4User2Xml(User user) {
        StringBuffer o = new StringBuffer("");
        if (user.getOrgList() != null && user.getOrgList().size() > 0) {
            Iterator var4 = user.getOrgList().iterator();

            while(var4.hasNext()) {
                BelongInfo b = (BelongInfo)var4.next();
                o.append("<BelongInfo>\t\t<oid>" + b.getOid() + "</oid>" + "\t\t<ouid>" + b.getOuid() + "</ouid>" + "\t\t<o_sort>" + b.getO_sort() + "</o_sort>" + "\t\t<ou_sort>" + b.getOu_sort() + "</ou_sort>" + "\t\t<part_time>" + b.getPart_time() + "</part_time>" + "\t</BelongInfo>");
            }
        }

        StringBuffer str = new StringBuffer("<User><uid>" + user.getUid() + "</uid>");
        str.append("<name>" + user.getName() + "</name>");
        str.append("<passWord>" + (StringUtils.isEmpty(user.getPassWord()) ? "" : user.getPassWord()) + "</passWord>");
        str.append("<gender>" + user.getGender() + "</gender>");
        str.append("<mobile>" + user.getMobile() + "</mobile>");
        str.append("<tel>" + (StringUtils.isEmpty(user.getTelphone()) ? "" : user.getTelphone()) + "</tel>");
        str.append("<email>" + (StringUtils.isEmpty(user.getEmail()) ? "" : user.getEmail()) + "</email>");
        str.append("<active>" + user.isActive() + "</active>");
        str.append("<qq>" + (StringUtils.isEmpty(user.getQq()) ? "" : user.getQq()) + "</qq>");
        str.append("<msn>" + (StringUtils.isEmpty(user.getMsn()) ? "" : user.getMsn()) + "</msn>");
        str.append("<job_number>" + (StringUtils.isEmpty(user.getJob_number()) ? "" : user.getJob_number()) + "</job_number>");
        str.append("<manager>" + user.isManager() + "</manager>");
        str.append("<pictureUrl></pictureUrl>");
        str.append("<weixin>" + (StringUtils.isEmpty(user.getWeixin()) ? "" : user.getWeixin()) + "</weixin>");
        str.append("<uCode>" + (StringUtils.isEmpty(user.getuCode()) ? "" : user.getuCode()) + "</uCode>");
        str.append("<oneCarId>" + (StringUtils.isEmpty(user.getOneCarId()) ? "" : user.getOneCarId()) + "</oneCarId>");
        str.append("<idCarId>" + (StringUtils.isEmpty(user.getIdCarId()) ? "" : user.getIdCarId()) + "</idCarId>");
        str.append("<birthday>" + (StringUtils.isEmpty(user.getBirthday()) ? "" : user.getBirthday()) + "</birthday>");
        str.append("<modifierId>" + (StringUtils.isEmpty(user.getModifierId()) ? "" : user.getModifierId()) + "</modifierId>");
        str.append("<uuid>" + (StringUtils.isEmpty(user.getUuid()) ? "" : user.getUuid()) + "</uuid>");
        str.append(o.toString() + "</User>");
        return str.toString();
    }

    public String stitchingUserXml(cn.microvideo.qsc.client.entity.xml.User user) {
        StringBuffer o = new StringBuffer("");
        if (user.getOrgList() != null && user.getOrgList().size() > 0) {
            Iterator var4 = user.getOrgList().iterator();

            while(var4.hasNext()) {
                BelongInfo b = (BelongInfo)var4.next();
                o.append("<BelongInfo>\t\t<oid>" + b.getOid() + "</oid>" + "\t\t<ouid>" + b.getOuid() + "</ouid>" + "\t\t<o_sort>" + b.getO_sort() + "</o_sort>" + "\t\t<ou_sort>" + b.getOu_sort() + "</ou_sort>" + "\t\t<part_time>" + b.getPart_time() + "</part_time>" + "\t</BelongInfo>");
            }
        }

        String conent = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><User>\t<uid>" + user.getUid() + "</uid>" + "\t<name>" + user.getName() + "</name>" + "\t<passWord>" + user.getPassWord() + "</passWord>" + "\t<gender>" + user.getGender() + "</gender>" + "\t<mobile>" + user.getMobile() + "</mobile>" + "\t<tel>" + user.getTelphone() + "</tel>" + "\t<email>" + user.getEmail() + "</email>" + "\t<active>" + user.isActive() + "</active>" + "\t<qq>" + user.getQq() + "</qq>" + "\t<msn>" + user.getMsn() + "</msn>" + "\t<job_number>" + user.getJob_number() + "</job_number>" + "\t<manager>" + user.isManager() + "</manager>" + "\t<pictureUrl></pictureUrl>" + "  <weixin>" + user.getWeixin() + "</weixin>" + "  <uCode>" + (user.getuCode() == null ? "" : user.getuCode()) + "</uCode>" + "  <oneCarId>" + (user.getOneCarId() == null ? "" : user.getOneCarId()) + "</oneCarId>" + "  <idCarId>" + (user.getIdCarId() == null ? "" : user.getIdCarId()) + "</idCarId>" + "  <birthday></birthday>" + o.toString() + "</User>";
        return conent;
    }

    public List<Role> getRoleByParentId(String parentId) {
        String service = this.qsUrl + "getRoleByParentId";
        Map<String, String> params = new HashMap();
        params.put("parentId", parentId);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<List<Role>> builder = (new GetRoleByParentIdBuilder()).setDocumnet(doc);
        return (List)builder.getEntity();
    }

    public ParentRoles findRoleByUid(String uid, String ouid) {
        String service = this.qsUrl + "service/findRoleByUid";
        Map<String, String> params = new HashMap();
        params.put("uid", uid);
        if (!StringUtils.isBlank(ouid)) {
            params.put("ouid", ouid);
        }
        Document doc = this.getDoc(service, RequestMethod.GET, params);

        String key = "findRoleByUid";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentRoles.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }


        new ParentRoles();

        try {
            ParentRoles info = (ParentRoles)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var9) {
            Exception e = var9;
            e.printStackTrace();
            return null;
        }
    }








    public ParentRole findRoleByRId(String rid) {
        String service = this.qsUrl + "service/findRoleByRId";
        Map<String, String> params = new HashMap();
        params.put("rid", rid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);


        String key = "findRoleByRId";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentRole.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }


        new ParentRole();

        try {
            ParentRole info = (ParentRole)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var8) {
            Exception e = var8;
            e.printStackTrace();
            return null;
        }
    }

    public ParentRoles findRolesByParentId(String pid) {
        String service = this.qsUrl + "service/findRolesByParentId";
        Map<String, String> params = new HashMap();
        params.put("pid", pid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);


        String key = "findRolesByParentId";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentRoles.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }


        new ParentRoles();

        try {
            ParentRoles info = (ParentRoles)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var8) {
            Exception e = var8;
            e.printStackTrace();
            return null;
        }
    }

    public ParentRoles findRolesByOuidAndAid(String ouid, String aid) {
        String service = this.qsUrl + "service/findRolesByOuidAndAid";
        Map<String, String> params = new HashMap();
        params.put("ouid", ouid);
        params.put("aid", aid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);



        String key = "findRolesByOuidAndAid";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentRoles.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }


        new ParentRoles();

        try {
            ParentRoles info = (ParentRoles)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var9) {
            Exception e = var9;
            e.printStackTrace();
            return null;
        }
    }

    public String stitchingRoleUserXml(List<String> users, List<String> roles) {
        String user = "";
        String role = "";
        StringBuffer strUser = new StringBuffer("");
        StringBuffer strRole = new StringBuffer("");
        Iterator var8 = users.iterator();

        String conent;
        while(var8.hasNext()) {
            conent = (String)var8.next();
            strUser.append(conent + "#");
        }

        var8 = roles.iterator();

        while(var8.hasNext()) {
            conent = (String)var8.next();
            strRole.append(conent + "#");
        }

        if (strUser != null && strUser.length() > 0) {
            user = strUser.substring(0, strUser.length() - 1);
        }

        if (strRole != null && strRole.length() > 0) {
            role = strRole.substring(0, strRole.length() - 1);
        }

        conent = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><personAndRole>\t<persons>" + user + "</persons>" + "\t<roles>" + role + "</roles>" + "</personAndRole>";
        return conent;
    }

    public List<String> authentication(String user, String ouid) {
        String service = this.qsUrl + "authentication";
        Map<String, String> params = new HashMap();
        params.put("user", user);
        params.put("ouid", ouid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);
        IBuilder<List<String>> builder = (new AuthenticationBuilder()).setDocumnet(doc);
        return (List)builder.getEntity();
    }

    public ParentApplications findApplicationByOid(String oid) {
        String service = this.qsUrl + "service/findApplicationByOid";
        Map<String, String> params = new HashMap();
        params.put("oid", oid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);



        String key = "findApplicationByOid";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentApplications.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }


        new ParentApplications();

        try {
            ParentApplications info = (ParentApplications)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var8) {
            Exception e = var8;
            e.printStackTrace();
            return null;
        }
    }

    public ParentFunctions findModuleByUid(String uid, String ouid) {
        String service = this.qsUrl + "service/findModuleByUid";
        Map<String, String> params = new HashMap();
        params.put("uid", uid);
        params.put("ouid", ouid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);



        String key = "findModuleByUid";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentFunctions.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }



        new ParentFunctions();

        try {
            ParentFunctions info = (ParentFunctions)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var9) {
            Exception e = var9;
            e.printStackTrace();
            return null;
        }
    }

    public ParentUsers findPersonsByRid(String rid) {
        String service = this.qsUrl + "service/findPersonsByRid";
        Map<String, String> params = new HashMap();
        params.put("rid", rid);
        Document doc = this.getDoc(service, RequestMethod.GET, params);



        String key = "findPersonsByRid";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("info", ParentUsers.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }


        new ParentUsers();

        try {
            ParentUsers info = (ParentUsers)xstream.fromXML(doc.asXML());
            info = this.getMobile1(info);
            return info;
        } catch (Exception var8) {
            Exception e = var8;
            e.printStackTrace();
            return null;
        }
    }

    public ParentToken queryToken(String uid, String pwd) {
        ParentToken info = new ParentToken();
        if (uid != null && !"".equals(uid) && pwd != null && !"".equals(pwd)) {
            String service = this.qsUrl + "service/queryToken";
            String conent = this.stitchingToken(uid, pwd);
            Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);



            String key = "queryToken";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("info", ParentToken.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            try {
                info = (ParentToken)xstream.fromXML(doc.asXML());
            } catch (Exception var9) {
                Exception e = var9;
                e.printStackTrace();
                return info;
            }
        } else {
            info.setMsg("传入参数有误：用户名，密码为空！");
        }

        return info;
    }

    private String stitchingToken(String uid, String pwd) {
        StringBuffer str = new StringBuffer("<?xml version=\"1.0\" encoding=\"utf-8\" ?>");
        str.append("<User><uid>" + uid + "</uid><passWord>" + pwd + "</passWord></User>");
        return str.toString();
    }

    public ParentCert queryCert(String uid, String aid) {
        ParentCert info = new ParentCert();
        Map<String, String> params = new HashMap();
        String service = this.qsUrl + "service/findCert";
        if (uid != null && !"".equals(uid) && aid != null && !"".equals(aid)) {
            params.put("uid", uid);
            params.put("aid", aid);
            Document doc = this.getDoc(service, RequestMethod.GET, params);


            String key = "queryCert";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("info", ParentCert.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            info = (ParentCert)xstream.fromXML(doc.asXML());
        } else {
            info.setMsg("输入参数有误：令牌、应用id不可以为空！");
        }

        return info;
    }

    public ParentUsers findPersonsByRidAndDid(List<String> rid, List<String> did, List<String> oid, String isSearchUR) {
        Condition con = new Condition();
        con.setDid(this.list4String(did));
        con.setRid(this.list4String(rid));
        con.setOid(this.list4String(oid));
        con.setIsSearchUR(isSearchUR);
        String service = this.qsUrl + "service/findPersonsByRidAndDid";


        String key = "findPersonsByRidAndDid";
        XStream xstream  =  XStreamManager.getxStream(key);
        if(xstream == null){
            xstream = new XStream(new DomDriver());
            xstream.alias("Condition", Condition.class);
            xstream.autodetectAnnotations(true);
            XStreamManager.setXStream(key,xstream);
        }


        String xml = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>" + xstream.toXML(con);
        Document doc = this.getDoc_forPost(service, RequestMethod.POST, xml);
        xstream.alias("info", ParentUsers.class);
        xstream.autodetectAnnotations(true);
        new ParentUsers();

        try {
            ParentUsers info = (ParentUsers)xstream.fromXML(doc.asXML());
            info = this.getMobile1(info);
            return info;
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
            return null;
        }
    }

    public String list4String(List<String> listStr) {
        StringBuffer str = new StringBuffer("");
        if (listStr != null && listStr.size() > 0) {
            Iterator var4 = listStr.iterator();

            while(var4.hasNext()) {
                String string = (String)var4.next();
                str.append(string + "#");
            }
        }

        return "".equals(str.toString()) ? str.toString() : str.substring(0, str.length() - 1);
    }

    public String list4String1(List<String> listStr) {
        StringBuffer str = new StringBuffer("");
        if (listStr != null && listStr.size() > 0) {
            Iterator var4 = listStr.iterator();

            while(var4.hasNext()) {
                String string = (String)var4.next();
                str.append(string + ",");
            }
        }

        return "".equals(str.toString()) ? str.toString() : str.substring(0, str.length() - 1);
    }

    public ParentUsers findPersonsByName(String name) {
        String service = this.qsUrl + "service/findPersonsByName";
        Map<String, String> params = new HashMap();
        new ParentUsers();

        try {
            name = URLEncoder.encode(URLEncoder.encode(name, "utf-8"), "utf-8");
            params.put("name", name);
            Document doc = this.getDoc(service, RequestMethod.GET, params);


            String key = "findPersonsByName";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("info", ParentUsers.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            ParentUsers info = (ParentUsers)xstream.fromXML(doc.asXML());
            info = this.getMobile1(info);
            return info;
        } catch (Exception var7) {
            Exception e = var7;
            e.printStackTrace();
            return null;
        }
    }

    public FunctionXmlListInfo authorityModule(String user, String ouid) {
        String service = this.qsUrl + "service/authorityModule";
        Map<String, String> params = new HashMap();
        new FunctionXmlListInfo();

        try {
            params.put("user", user);
            params.put("ouid", ouid);
            Document doc = this.getDoc(service, RequestMethod.GET, params);


            String key = "authorityModule";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("info", FunctionXmlListInfo.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }



            FunctionXmlListInfo info = (FunctionXmlListInfo)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var8) {
            Exception e = var8;
            e.printStackTrace();
            return null;
        }
    }

    public cn.microvideo.qsc.client.entity.xml.User loginByType(String style, String date, String pwd) {
        cn.microvideo.qsc.client.entity.xml.User loginUser = new cn.microvideo.qsc.client.entity.xml.User();
        String service = this.qsUrl + "service/loginByType";
        Map<String, String> params = new HashMap();

        try {
            params.put("style", style);
            params.put("date", date);
            params.put("token", pwd);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    loginUser = (cn.microvideo.qsc.client.entity.xml.User)objectMapper.convertValue(info.getResult(), cn.microvideo.qsc.client.entity.xml.User.class);
                    if (loginUser != null && loginUser.getMobile() != null && !loginUser.getMobile().isEmpty() && loginUser.getMobile().indexOf(",") > 0) {
                        int i = loginUser.getMobile().indexOf(",");
                        loginUser.setMobile1(loginUser.getMobile().substring(i + 1, loginUser.getMobile().length()));
                        loginUser.setMobile(loginUser.getMobile().substring(0, i));
                    }
                }
            }

            return loginUser;
        } catch (Exception var11) {
            Exception e = var11;
            e.printStackTrace();
            return null;
        }
    }

    public boolean isHasPermission(String uuid, String mcode, String scope4query, String ouid) {
        boolean result = false;
        String service = this.qsUrl + "service/isHasPermission";
        Map<String, String> params = new HashMap();

        try {
            params.put("uuid", uuid);
            params.put("mcode", mcode);
            params.put("scope4query", scope4query);
            params.put("ouid", ouid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    String re = info.getResult().toString();
                    result = Boolean.valueOf(re);
                }
            }

            return result;
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
            return result;
        }
    }

    public PageResultResponseUser selectPersonByCond(String ouid, String name, String tel, String fixTel, String email, String pageNum, String pageSize) {
        PageResultResponseUser pr = null;
        List<cn.microvideo.qsc.client.entity.xml.User> userList = new ArrayList();
        String service = this.qsUrl + "service/findPersonsByCond";
        Map<String, String> params = new HashMap();

        try {
            if (name != null && !name.isEmpty()) {
                name = URLEncoder.encode(URLEncoder.encode(name, "utf-8"), "utf-8");
            }

            params.put("ouid", ouid);
            params.put("name", name);
            params.put("tel", tel);
            params.put("fixTel", fixTel);
            params.put("email", email);
            params.put("pageNum", pageNum);
            params.put("pageSize", pageSize);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            ObjectMapper objectMapper = new ObjectMapper();
            if (str != null && !str.isEmpty()) {
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    pr = (PageResultResponseUser)objectMapper.convertValue(info.getResult(), PageResultResponseUser.class);
                    List<cn.microvideo.qsc.client.entity.xml.User> list = pr.getList();

                    cn.microvideo.qsc.client.entity.xml.User u;
                    for(Iterator var17 = list.iterator(); var17.hasNext(); userList.add(u)) {
                        u = (cn.microvideo.qsc.client.entity.xml.User)var17.next();
                        if (u != null && u.getMobile() != null && !u.getMobile().isEmpty() && u.getMobile().indexOf(",") > 0) {
                            int i = u.getMobile().indexOf(",");
                            u.setMobile1(u.getMobile().substring(i + 1, u.getMobile().length()));
                            u.setMobile(u.getMobile().substring(0, i));
                        }
                    }

                    pr.setList(userList);
                }
            }

            return pr;
        } catch (Exception var19) {
            Exception e = var19;
            e.printStackTrace();
            return null;
        }
    }

    public List<ModuleInfoXml> findAPPAndModsByUid(String uid, String scope4query, String ouid) {
        List<ModuleInfoXml> mList = null;
        String service = this.qsUrl + "service/findAPPAndModsByUid";
        Map<String, String> params = new HashMap();

        try {
            params.put("uid", uid);
            params.put("scope4query", scope4query);
            params.put("ouid", ouid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<ModuleInfoXml>> valueTypeRef = new TypeReference<List<ModuleInfoXml>>() {
                    };
                    mList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }

            return mList;
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
            return null;
        }
    }

    public ParentUsers findPersonsByTidAndDid(KeyAndValueCondition condition) {
        String service = this.qsUrl + "service/findPersonsByTidAndDid";
        new ParentUsers();

        try {


            String key = "findPersonsByTidAndDid";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("info", ParentUsers.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            String conent = xstream.toXML(condition);
            Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
            ParentUsers info = (ParentUsers)xstream.fromXML(doc.asXML());
            info = this.getMobile1(info);
            return info;
        } catch (Exception var7) {
            Exception e = var7;
            e.printStackTrace();
            return null;
        }
    }

    public ParentKenAndValue findAttributeTagByUid(String uid) {
        String service = this.qsUrl + "service/findAttributeTagByUid";
        Map<String, String> params = new HashMap();
        new ParentKenAndValue();

        try {
            params.put("uid", uid);
            Document doc = this.getDoc(service, RequestMethod.GET, params);


            String key = "findAttributeTagByUid";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("info", ParentKenAndValue.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            ParentKenAndValue info = (ParentKenAndValue)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var7) {
            Exception e = var7;
            e.printStackTrace();
            return null;
        }
    }

    public ParentAttributeTags findAttrTagByTag(String arrtTag) {
        String service = this.qsUrl + "service/findAttrTagByTag";
        Map<String, String> params = new HashMap();
        new ParentAttributeTags();

        try {
            params.put("arrtTag", arrtTag);
            Document doc = this.getDoc(service, RequestMethod.GET, params);


            String key = "findAttrTagByTag";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("info", ParentAttributeTags.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            ParentAttributeTags info = (ParentAttributeTags)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var7) {
            Exception e = var7;
            e.printStackTrace();
            return null;
        }
    }

    public ParentAttributeTag findAttrTagByID(String attrTagId) {
        String service = this.qsUrl + "service/findAttrTagByID";
        Map<String, String> params = new HashMap();
        new ParentAttributeTag();

        try {
            params.put("attrTagId", attrTagId);
            Document doc = this.getDoc(service, RequestMethod.GET, params);


            String key = "findAttrTagByID";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("info", ParentAttributeTag.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            ParentAttributeTag info = (ParentAttributeTag)xstream.fromXML(doc.asXML());
            return info;
        } catch (Exception var7) {
            Exception e = var7;
            e.printStackTrace();
            return null;
        }
    }

    public List<ModuleInfo> findAuthorityByUidAndPid(String uid, String pid, String scope4query, String ouid) {
        List<ModuleInfo> mxList = null;
        String service = this.qsUrl + "service/findAuthorityByUidAndPid";
        Map<String, String> params = new HashMap();

        try {
            params.put("uid", uid);
            params.put("pid", pid);
            params.put("scope4query", scope4query);
            params.put("ouid", ouid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<ModuleInfo>> valueTypeRef = new TypeReference<List<ModuleInfo>>() {
                    };
                    mxList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var15) {
            Exception e = var15;
            e.printStackTrace();
        } finally {
            ;
        }

        return mxList;
    }

    public User findPersonByUUID(String uuid) {
        User u = null;
        String service = this.qsUrl + "service/findPersonByUUID";
        Map<String, String> params = new HashMap();

        try {
            params.put("uuid", uuid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    u = (User)objectMapper.convertValue(info.getResult(), User.class);
                    if (u != null && u.getMobile() != null && !u.getMobile().isEmpty() && u.getMobile().indexOf(",") > 0) {
                        int i = u.getMobile().indexOf(",");
                        u.setMobile1(u.getMobile().substring(i + 1, u.getMobile().length()));
                        u.setMobile(u.getMobile().substring(0, i));
                    }
                }
            }
        } catch (Exception var11) {
            Exception e = var11;
            e.printStackTrace();
        } finally {
            ;
        }

        return u;
    }

    public ModuleInfo findAPPByCodeOrUuid(String appCode, String appId, String type) {
        ModuleInfo m = null;
        String service = this.qsUrl + "service/findAPPByCodeOrUuid";
        Map<String, String> params = new HashMap();

        try {
            params.put("code", appCode);
            params.put("uuid", appId);
            params.put("type", type);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            Map<String, Class> classMap = new HashMap();
            classMap.put("result", ModuleInfo.class);
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    m = (ModuleInfo)objectMapper.convertValue(info.getResult(), ModuleInfo.class);
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return m;
    }

    public TreeNodes findTreeByCond(TreeCondition tc) {
        TreeNodes tn = null;
        String service = this.qsUrl + "service/findTreeByCond";

        try {


            String key = "findTreeByCond";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("treeCondion", TreeCondition.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            String xml = xstream.toXML(tc);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, (Map)null, xml);
            Map<String, Class> classMap = new HashMap();
            classMap.put("result", TreeNodes.class);
            classMap.put("children", TreeNodes.class);
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new TreeNodes();
                    tn = (TreeNodes)objectMapper.convertValue(info.getResult(), TreeNodes.class);
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return tn;
    }

    public ModuleInfo findAllModuleByUidAndPid(String uid, String pid, String scope4query, String ouid) {
        ModuleInfo m = null;
        String service = this.qsUrl + "service/findAllModuleByUidAndPid";
        Map<String, String> params = new HashMap();

        try {
            params.put("uid", uid);
            params.put("pid", pid);
            params.put("scope4query", scope4query);
            params.put("ouid", ouid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    m = (ModuleInfo)objectMapper.convertValue(info.getResult(), ModuleInfo.class);
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return m;
    }

    public ModuleInfo findAllModuleByUidAndPCode(String uid, String pcode, String scope4query, String ouid) {
        ModuleInfo m = null;
        String service = this.qsUrl + "service/findAllModuleByUidAndPCode";
        Map<String, String> params = new HashMap();

        try {
            params.put("uid", uid);
            params.put("pcode", pcode);
            params.put("scope4query", scope4query);
            params.put("ouid", ouid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    m = (ModuleInfo)objectMapper.convertValue(info.getResult(), ModuleInfo.class);
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return m;
    }

    public ModuleInfo findAllModuleByUid(String uid, String scope4query, String ouid) {
        ModuleInfo m = null;
        String service = this.qsUrl + "service/findAllModuleByUid";
        Map<String, String> params = new HashMap();

        try {
            params.put("uid", uid);
            params.put("scope4query", scope4query);
            params.put("ouid", ouid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    m = (ModuleInfo)objectMapper.convertValue(info.getResult(), ModuleInfo.class);
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return m;
    }

    public List<ModuleInfo> findAllModuleByUidOnlyDesktop(String uid, String scope4query, String ouid) {
        List<ModuleInfo> result = null;
        String service = this.qsUrl + "service/findAllModuleByUidOnlyDesktop";
        Map<String, String> params = new HashMap();

        try {
            params.put("uid", uid);
            params.put("scope4query", scope4query);
            params.put("ouid", ouid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String resu = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<ModuleInfo>> valueTypeRef = new TypeReference<List<ModuleInfo>>() {
                    };
                    result = (List)objectMapper.readValue(resu, valueTypeRef);
                }
            }
        } catch (Exception var14) {
            Exception e = var14;
            e.printStackTrace();
        } finally {
            ;
        }

        return result;
    }

    public List<ModuleInfo> findAllModuleByUidOnlyQuickActions(String uid, String scope4query, String ouid) {
        List<ModuleInfo> result = null;
        String service = this.qsUrl + "service/findAllModuleByUidOnlyQuickActions";
        Map<String, String> params = new HashMap();

        try {
            params.put("uid", uid);
            params.put("scope4query", scope4query);
            params.put("ouid", ouid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String res = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<ModuleInfo>> valueTypeRef = new TypeReference<List<ModuleInfo>>() {
                    };
                    result = (List)objectMapper.readValue(res, valueTypeRef);
                }
            }
        } catch (Exception var14) {
            Exception e = var14;
            e.printStackTrace();
        } finally {
            ;
        }

        return result;
    }

    public List<String> findAllAMFIdByUid(String uid, String scope4query, String ouid, int authorityType) {
        List<String> result = null;
        String service = this.qsUrl + "service/findAllAMFIdByUid";
        Map<String, String> params = new HashMap();

        try {
            params.put("type", String.valueOf(authorityType));
            params.put("uid", uid);
            params.put("scope4query", scope4query);
            params.put("ouid", ouid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String res = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<String>> valueTypeRef = new TypeReference<List<String>>() {
                    };
                    result = (List)objectMapper.readValue(res, valueTypeRef);
                }
            }
        } catch (Exception var15) {
            Exception e = var15;
            e.printStackTrace();
        } finally {
            ;
        }

        return result;
    }

    public String stitchingUserXmlOnlyBaseInfo(User user) {
        String conent = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><User>\t<uid>" + user.getUid() + "</uid>" + "\t<name>" + user.getName() + "</name>" + "\t<gender>" + user.getGender() + "</gender>" + "\t<mobile>" + user.getMobile() + "</mobile>" + "\t<tel>" + (user.getTelphone() != null ? user.getTelphone().replace(",", "#") : "") + "</tel>" + "\t<email>" + user.getEmail() + "</email>" + "\t<active>" + user.isActive() + "</active>" + "\t<qq>" + user.getQq() + "</qq>" + "\t<msn>" + user.getMsn() + "</msn>" + "\t<job_number>" + user.getJob_number() + "</job_number>" + "\t<manager>" + user.isManager() + "</manager>" + "\t<pictureUrl></pictureUrl>" + "  <weixin>" + user.getWeixin() + "</weixin>" + "  <uCode>" + (user.getuCode() == null ? "" : user.getuCode()) + "</uCode>" + "  <oneCarId>" + (user.getOneCarId() == null ? "" : user.getOneCarId()) + "</oneCarId>" + "  <idCarId>" + (user.getIdCarId() == null ? "" : user.getIdCarId()) + "</idCarId>" + "  <birthday>" + user.getBirthday() + "</birthday></User>";
        return conent;
    }

    public String stitchingUserXmlOnlyBaseInfo(cn.microvideo.qsc.client.entity.xml.User user) {
        String conent = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><User>\t<uid>" + user.getUid() + "</uid>" + "\t<name>" + user.getName() + "</name>" + "\t<gender>" + user.getGender() + "</gender>" + "\t<mobile>" + user.getMobile() + "</mobile>" + "\t<tel>" + user.getTelphone() + "</tel>" + "\t<email>" + user.getEmail() + "</email>" + "\t<active>" + user.isActive() + "</active>" + "\t<qq>" + user.getQq() + "</qq>" + "\t<msn>" + user.getMsn() + "</msn>" + "\t<job_number>" + user.getJob_number() + "</job_number>" + "\t<manager>" + user.isManager() + "</manager>" + "\t<pictureUrl></pictureUrl>" + "  <weixin>" + user.getWeixin() + "</weixin>" + "  <uCode>" + (user.getuCode() == null ? "" : user.getuCode()) + "</uCode>" + "  <oneCarId>" + (user.getOneCarId() == null ? "" : user.getOneCarId()) + "</oneCarId>" + "  <idCarId>" + (user.getIdCarId() == null ? "" : user.getIdCarId()) + "</idCarId>" + "  <birthday></birthday>" + "</User>";
        return conent;
    }

    public List<TreeNodes> findPersonListByCond(TreeCondition tc) {
        List<TreeNodes> tnList = null;
        String service = this.qsUrl + "service/findPersonListByCond";

        try {


            String key = "findPersonListByCond";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("treeCondion", TreeCondition.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            String xml = xstream.toXML(tc);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, (Map)null, xml);
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<TreeNodes>> valueTypeRef = new TypeReference<List<TreeNodes>>() {
                    };
                    tnList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return tnList;
    }

    public int findPersonCountByCond(TreeCondition tc) {
        int result = 0;
        String service = this.qsUrl + "service/findPersonCountByCond";

        try {



            String key = "findPersonCountByCond";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("treeCondion", TreeCondition.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }


            String xml = xstream.toXML(tc);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, (Map)null, xml);
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    result = Integer.valueOf(info.getResult().toString());
                }
            }
        } catch (Exception var11) {
            Exception e = var11;
            e.printStackTrace();
        } finally {
            ;
        }

        return result;
    }

    public OrganizationList findOrgByName(String name, int pageNum, int pageSize) {
        OrganizationList tnList = null;
        String service = this.qsUrl + "service/findOrgByName";

        try {
            Map<String, String> params = new HashMap();
            name = URLEncoder.encode(URLEncoder.encode(name, "utf-8"), "utf-8");
            params.put("name", name);
            params.put("pageNum", String.valueOf(pageNum));
            params.put("pageSize", String.valueOf(pageSize));
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null && info.getResult() != null) {
                    tnList = (OrganizationList)objectMapper.convertValue(info.getResult(), OrganizationList.class);
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return tnList;
    }

    public List<User> findPersonByOuidAndAid(String ouId, String aId) {
        List<User> tnList = null;
        String service = this.qsUrl + "service/findPersonByOuidAndAid";

        try {
            Map<String, String> params = new HashMap();
            params.put("ouId", ouId);
            params.put("aId", aId);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                    };
                    tnList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return tnList;
    }

    public User findPersonByPidAndAid(String pId, String aId) {
        User u = new User();
        String service = this.qsUrl + "service/findPersonByPidAndAid";

        try {
            Map<String, String> params = new HashMap();
            params.put("pId", pId);
            params.put("aId", aId);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    u = (User)objectMapper.convertValue(info.getResult(), User.class);
                }
            }
        } catch (Exception var11) {
            Exception e = var11;
            e.printStackTrace();
        } finally {
            ;
        }

        return u;
    }

    public List<cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo> findOrganizationsByParentid_v01(String id, boolean isAll, boolean showAddBook) {
        List<cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo> orgList = null;

        try {
            String service = this.qsUrl + "service/findOrganizationsByParentid_v0.1";
            Map<String, String> params = new HashMap();
            params.put("id", id);
            params.put("isAll", String.valueOf(isAll));
            params.put("showAddBook", String.valueOf(showAddBook));
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo>> valueTypeRef = new TypeReference<List<cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo>>() {
                    };
                    orgList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var14) {
            Exception e = var14;
            e.printStackTrace();
        } finally {
            ;
        }

        return orgList;
    }

    public UserList findPersonsByNameAndTel(String name, String aid, int pageNum, int pageSize) {
        String service = this.qsUrl + "service/findPersonsByNameAndTel";
        Map<String, String> params = new HashMap();
        UserList userList = new UserList();

        try {
            name = URLEncoder.encode(URLEncoder.encode(name, "utf-8"), "utf-8");
            params.put("name", name);
            params.put("aid", aid);
            params.put("pageNum", String.valueOf(pageNum));
            params.put("pageSize", String.valueOf(pageSize));
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    userList = (UserList)objectMapper.convertValue(info.getResult(), UserList.class);
                }
            }

            return userList;
        } catch (Exception var11) {
            Exception e = var11;
            e.printStackTrace();
            return null;
        }
    }

    public NameFuzzyQueryList findUserOrOrgListByName(String name, int pageNum, int pageSize, String type, String aId, String orgType) {
        String service = this.qsUrl + "service/findUserOrOrgListByName";
        Map<String, String> params = new HashMap();
        NameFuzzyQueryList userList = new NameFuzzyQueryList();

        try {
            name = URLEncoder.encode(URLEncoder.encode(name, "utf-8"), "utf-8");
            params.put("name", name);
            params.put("aId", aId);
            params.put("pageNum", String.valueOf(pageNum));
            params.put("pageSize", String.valueOf(pageSize));
            params.put("type", type == "null" ? "" : type);
            params.put("orgType", orgType == "null" ? null : orgType);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    userList = (NameFuzzyQueryList)objectMapper.convertValue(info.getResult(), NameFuzzyQueryList.class);
                }
            }

            return userList;
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
            return null;
        }
    }

    public UserData findOrgAndUserInfoByOrgId(String orgId, String aid) {
        String service = this.qsUrl + "service/findOrgAndUserInfoByOrgId";
        Map<String, String> params = new HashMap();
        UserData userList = new UserData();

        try {
            params.put("orgId", orgId);
            params.put("aid", aid == "null" ? "" : aid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    userList = (UserData)objectMapper.convertValue(info.getResult(), UserData.class);
                }
            }

            return userList;
        } catch (Exception var9) {
            Exception e = var9;
            e.printStackTrace();
            return null;
        }
    }

    public List<User> findLeadsByOid(String oid) {
        List<User> userList = null;
        String service = this.qsUrl + "service/findLeadsByOid";

        try {
            Map<String, String> params = new HashMap();
            params.put("oid", oid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                    };
                    userList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return userList;
    }

    public List<User> findPersonByOuidPositionUp(String ouid, String positionStr) {
        List<User> userList = new ArrayList();
        String service = this.qsUrl + "service/findPersonByOuidPositionUp";

        try {
            Map<String, String> params = new HashMap();
            params.put("ouid", ouid);
            params.put("positionStr", positionStr);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    userList = (List)objectMapper.convertValue(info.getResult(), List.class);
                }
            }
        } catch (Exception var11) {
            Exception e = var11;
            e.printStackTrace();
        } finally {
            ;
        }

        return (List)userList;
    }

    public List<User> findUserByOidAndRanks(String oid, List<String> postIdList, String aboveRankId) {
        List<User> userList = null;
        String service = this.qsUrl + "service/findUserByOidAndRanks";

        try {
            String postIds = this.list4String1(postIdList);
            Map<String, String> params = new HashMap();
            params.put("rankIds", postIds);
            params.put("oid", oid);
            params.put("aboveRankId", aboveRankId);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                    };
                    userList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var15) {
            Exception e = var15;
            e.printStackTrace();
        } finally {
            ;
        }

        return userList;
    }

    public Map<String, String> findPositionByUUID(String uuid) {
        Map<String, String> mapPost = new HashMap();
        String service = this.qsUrl + "service/findPositionByUUID";

        try {
            Map<String, String> params = new HashMap();
            params.put("uuid", uuid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<Map<String, String>> valueTypeRef = new TypeReference<Map<String, String>>() {
                    };
                    mapPost = (Map)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return (Map)mapPost;
    }

    public Template findOrgTempById(String id) {
        Template tem = null;
        String service = this.qsUrl + "service/findOrgTempById";

        try {
            Map<String, String> params = new HashMap();
            params.put("id", id);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<Template> valueTypeRef = new TypeReference<Template>() {
                    };
                    tem = (Template)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return tem;
    }

    public List<OrganizationInfo> findOrgListByParentId(String parentId, String type, String isAll, String showAddBook) {
        List<OrganizationInfo> orgList = null;
        String service = this.qsUrl + "service/findOrgListByParentId";

        try {
            Map<String, String> params = new HashMap();
            params.put("id", parentId);
            params.put("type", type);
            params.put("isAll", isAll);
            params.put("showAddBook", showAddBook);
            System.out.println(service);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<OrganizationInfo>> valueTypeRef = new TypeReference<List<OrganizationInfo>>() {
                    };
                    orgList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var15) {
            Exception e = var15;
            e.printStackTrace();
        } finally {
            ;
        }

        return orgList;
    }

    public OrganizationInfo findOrgByOCode(String oCode) {
        OrganizationInfo org = null;
        String service = this.qsUrl + "service/findOrgByOCode";

        try {
            Map<String, String> params = new HashMap();
            params.put("oCode", oCode);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    org = (OrganizationInfo)objectMapper.convertValue(info.getResult(), OrganizationInfo.class);
                }
            }
        } catch (Exception var10) {
            Exception e = var10;
            e.printStackTrace();
        } finally {
            ;
        }

        return org;
    }

    public List<User> findPersonByOid(String oid) {
        List<User> userList = null;
        String service = this.qsUrl + "service/findPersonByOid";

        try {
            Map<String, String> params = new HashMap();
            params.put("oId", oid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                    };
                    userList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return userList;
    }

    public Result modifyNameMobileByUid(String uid, String name, String mobile) {
        if (uid != null && !uid.isEmpty() && name != null && !name.isEmpty() && mobile != null && !mobile.isEmpty()) {
            String service = this.qsUrl + "service/modifyNameMobileByUid";
            User user = new User();
            user.setUid(uid);
            user.setName(name);
            user.setMobile(mobile);


            String key = "modifyNameMobileByUid";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("User", User.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }



            String conent = xstream.toXML(user);
            Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
            IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
            return (Result)builder.getEntity();
        } else {
            Result res = new Result(false, "参数传入错误");
            return res;
        }
    }

    public Result hrBelong(String uuid, String oid, String ouid, String modifierId) {
        if (uuid != null && !uuid.isEmpty() && oid != null && !oid.isEmpty() && ouid != null && !ouid.isEmpty()) {
            String service = this.qsUrl + "service/hrBelong";
            Map<String, String> params = new HashMap();
            params.put("uid", uuid);
            params.put("oid", oid);
            params.put("ouid", ouid);
            params.put("modifierId", modifierId);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            return "success".equals(str) ? new Result(true, "修改成功") : new Result(false, "修改失败");
        } else {
            Result res = new Result(false, "参数传入错误");
            return res;
        }
    }

    public List<String> findParentIdsById(String id) {
        List<String> resultIds = null;

        try {
            if (id != null && !id.isEmpty()) {
                String service = this.qsUrl + "service/findParentIdsById";
                Map<String, String> params = new HashMap();
                params.put("id", id);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        new ArrayList();
                        String result = objectMapper.readTree(str).get("result").toString();
                        TypeReference<List<String>> valueTypeRef = new TypeReference<List<String>>() {
                        };
                        resultIds = (List)objectMapper.readValue(result, valueTypeRef);
                    }
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return resultIds;
    }

    public Object filterPersonListByOid(String uIds, String ouId, String dataType) {
        List<User> userList = null;
        TreeNodes tn = null;

        try {
            if (uIds != null && !uIds.isEmpty() && ouId != null && !ouId.isEmpty()) {
                String service = this.qsUrl + "service/filterPersonListByOid";
                Map<String, String> params = new HashMap();
                params.put("uIds", uIds);
                params.put("ouId", ouId);
                params.put("dataType", dataType);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        if (this.dataType_tree.equals(dataType)) {
                            new TreeNodes();
                            tn = (TreeNodes)objectMapper.convertValue(info.getResult(), TreeNodes.class);
                        } else {
                            new ArrayList();
                            String result = objectMapper.readTree(str).get("result").toString();
                            TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                            };
                            userList = (List)objectMapper.readValue(result, valueTypeRef);
                        }
                    }
                }
            }
        } catch (Exception var15) {
            Exception e = var15;
            e.printStackTrace();
        } finally {
            ;
        }

        return this.dataType_tree.equals(dataType) ? tn : userList;
    }

    public String findParentNamesById(String id) {
        String result = "";

        try {
            if (id != null && !id.isEmpty()) {
                String service = this.qsUrl + "service/findParentNamesById";
                Map<String, String> params = new HashMap();
                params.put("id", id);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        result = info.getResult().toString();
                    }
                }
            }
        } catch (Exception var10) {
            Exception e = var10;
            e.printStackTrace();
        } finally {
            ;
        }

        return result;
    }

    public cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo findDirectlyOrgById(String id) {
        cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo org = null;
        String service = this.qsUrl + "service/findDirectlyOrgById";

        try {
            Map<String, String> params = new HashMap();
            params.put("id", id);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    org = (cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo)objectMapper.convertValue(info.getResult(), cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo.class);
                }
            }
        } catch (Exception var10) {
            Exception e = var10;
            e.printStackTrace();
        } finally {
            ;
        }

        return org;
    }

    public List<User> findUsersByModuleCode(String code) {
        List<User> userList = null;
        String service = this.qsUrl + "service/findUsersByModuleCode";
        Map<String, String> params = new HashMap();

        try {
            params.put("code", code);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            Map<String, Class> classMap = new HashMap();
            classMap.put("result", User.class);
            classMap.put("orgList", BelongInfo.class);
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                String result = objectMapper.readTree(str).get("result").toString();
                TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                };
                if (info.getResult() != null) {
                    userList = new ArrayList();
                    List<User> list = (List)objectMapper.readValue(result, valueTypeRef);

                    User u;
                    for(Iterator var13 = list.iterator(); var13.hasNext(); userList.add(u)) {
                        u = (User)var13.next();
                        if (u.getMobile() != null && u.getMobile().indexOf(",") > 0) {
                            int i = u.getMobile().indexOf(",");
                            u.setMobile1(u.getMobile().substring(i + 1, u.getMobile().length()));
                            u.setMobile(u.getMobile().substring(0, i));
                        }
                    }
                }
            }
        } catch (Exception var17) {
            Exception e = var17;
            e.printStackTrace();
        } finally {
            ;
        }

        return userList;
    }

    public List<ModuleInfo> findAllModuleOnlyDesktop(String systemId) {
        List<ModuleInfo> result = null;
        String service = this.qsUrl + "service/findAllModuleOnlyDesktop";
        Map<String, String> params = new HashMap();
        if (systemId != null && !StringUtils.isBlank(systemId)) {
            params.put("systemId", systemId);
        }

        try {
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String resu = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<ModuleInfo>> valueTypeRef = new TypeReference<List<ModuleInfo>>() {
                    };
                    result = (List)objectMapper.readValue(resu, valueTypeRef);
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return result;
    }

    public Response addOrgList(List<OrgInfo> orgList, String uuid) {
        Response info = new Response();
        String service = this.qsUrl + "service/addOrgList";
        Map<String, String> params = new HashMap();
        params.put("uuids", uuid);
        JSONArray json = JSONArray.fromObject(orgList);
        String conent = json.toString();
        String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, params, conent);
        if (str != null && !str.isEmpty()) {
            ObjectMapper objectMapper = new ObjectMapper();

            try {
                info = (Response)objectMapper.readValue(str, Response.class);
            } catch (JsonParseException var11) {
                JsonParseException e = var11;
                e.printStackTrace();
            } catch (JsonMappingException var12) {
                JsonMappingException e = var12;
                e.printStackTrace();
            } catch (IOException var13) {
                IOException e = var13;
                e.printStackTrace();
            }
        }

        return info;
    }

    public Response batchAddPerson(List<User> userList, String uuid) {
        Response info = new Response();
        Map<String, String> params = new HashMap();
        params.put("uuids", uuid);
        userList = this.packMobile4List(userList);
        String service = this.qsUrl + "service/batchAddPerson";
        List<UserXml> userXmls = this.user2UserXml(userList);
        JSONArray json = JSONArray.fromObject(userXmls);
        String conent = json.toString();
        String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, params, conent);
        if (str != null && !str.isEmpty()) {
            ObjectMapper objectMapper = new ObjectMapper();

            try {
                info = (Response)objectMapper.readValue(str, Response.class);
            } catch (JsonParseException var12) {
                JsonParseException e = var12;
                e.printStackTrace();
            } catch (JsonMappingException var13) {
                JsonMappingException e = var13;
                e.printStackTrace();
            } catch (IOException var14) {
                IOException e = var14;
                e.printStackTrace();
            }
        }

        return info;
    }

    public List<UserXml> user2UserXml(List<User> userList) {
        List<UserXml> newUserList = new ArrayList();
        Iterator var4 = userList.iterator();

        while(var4.hasNext()) {
            User user1 = (User)var4.next();
            newUserList.add(new UserXml(user1));
        }

        return newUserList;
    }

    public List<User> packMobile4List(List<User> userList) {
        List<User> newUserList = new ArrayList();
        Iterator var4 = userList.iterator();

        while(var4.hasNext()) {
            User user1 = (User)var4.next();
            newUserList.add(this.packMobile(user1));
        }

        return newUserList;
    }

    public List<OrganizationInfo> findOrgListByLabelId(String labelId) {
        List<OrganizationInfo> orgList = null;
        String service = this.qsUrl + "service/findOrgListByLabelId";

        try {
            if (labelId != null && !labelId.isEmpty()) {
                Map<String, String> params = new HashMap();
                params.put("labelId", labelId);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        new ArrayList();
                        String result = objectMapper.readTree(str).get("result").toString();
                        TypeReference<List<OrganizationInfo>> valueTypeRef = new TypeReference<List<OrganizationInfo>>() {
                        };
                        orgList = (List)objectMapper.readValue(result, valueTypeRef);
                    }
                }
            }
        } catch (Exception var12) {
            Exception e = var12;
            e.printStackTrace();
        } finally {
            ;
        }

        return orgList;
    }

    public List<OrganizationInfo> findOrgListByParentIdAndTempId(String parentId, String tempId) {
        List<OrganizationInfo> orgList = null;
        String service = this.qsUrl + "service/findOrgListByParentIdAndTempId";

        try {
            if (parentId != null && !parentId.isEmpty() && tempId != null && !tempId.isEmpty()) {
                Map<String, String> params = new HashMap();
                params.put("orgId", parentId);
                params.put("tempId", tempId);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        orgList = new ArrayList();
                        String result = objectMapper.readTree(str).get("result").toString();
                        TypeReference<List<cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo>> valueTypeRef = new TypeReference<List<cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo>>() {
                        };
                        List<cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo> list = (List)objectMapper.readValue(result, valueTypeRef);
                        if (list != null && list.size() > 0) {
                            orgList = this.changeOrgArray(list);
                        }
                    }
                }
            }
        } catch (Exception var14) {
            Exception e = var14;
            e.printStackTrace();
        } finally {
            ;
        }

        return (List)orgList;
    }

    public List<OrganizationInfo> changeOrgArray(List<cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo> list) {
        List<OrganizationInfo> orgList = new ArrayList();
        if (list != null && list.size() > 0) {
            Iterator var4 = list.iterator();

            while(var4.hasNext()) {
                cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo org = (cn.microvideo.qsc.client.entity.xml.imp.OrganizationInfo)var4.next();
                OrganizationInfo organization = new OrganizationInfo(org);
                orgList.add(organization);
            }
        }

        return orgList;
    }

    public List<SectorResult> statisticalRankUserCount(String rankId, String childId) {
        List<SectorResult> list = null;
        String service = this.qsUrl + "service/chart/statisticalRankUserCount";

        try {
            Map<String, String> params = new HashMap();
            params.put("rankId", rankId);
            params.put("childId", childId);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<SectorResult>> valueTypeRef = new TypeReference<List<SectorResult>>() {
                    };
                    list = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return list;
    }

    public List<User> findUsersByOidAndRid(String oid, String rid) {
        List<User> userList = null;
        String service = this.qsUrl + "service/findUsersByOidAndRid";

        try {
            Map<String, String> params = new HashMap();
            params.put("oid", oid);
            params.put("rid", rid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new ArrayList();
                    String result = objectMapper.readTree(str).get("result").toString();
                    TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                    };
                    userList = (List)objectMapper.readValue(result, valueTypeRef);
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return userList;
    }

    public TreeNodes findOrgTreeByTempIds(String tempIds, String isShowVOOrg, String filterOrgIds) {
        TreeNodes tn = null;
        String service = this.qsUrl + "service/findOrgTreeByTempIds";

        try {
            Map<String, String> params = new HashMap();
            params.put("tempIds", URLEncoder.encode(tempIds, "utf-8"));
            params.put("isShowVOOrg", isShowVOOrg);
            params.put("filterOrgIds", URLEncoder.encode(filterOrgIds, "utf-8"));
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            Map<String, Class> classMap = new HashMap();
            classMap.put("result", TreeNodes.class);
            classMap.put("children", TreeNodes.class);
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    new TreeNodes();
                    tn = (TreeNodes)objectMapper.convertValue(info.getResult(), TreeNodes.class);
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return tn;
    }

    public List<User> findUserListByDate4AQXT(String startDay, String endDay) {
        List<User> userList = null;
        String service = this.qsUrl + "service/findUserListByDate4AQXT";

        try {
            if (startDay != null && !startDay.isEmpty()) {
                Map<String, String> params = new HashMap();
                params.put("startDay", startDay);
                params.put("endDay", endDay);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        new ArrayList();
                        String result = objectMapper.readTree(str).get("result").toString();
                        TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                        };
                        userList = (List)objectMapper.readValue(result, valueTypeRef);
                    }
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return userList;
    }

    public List<OrganizationInfo> findOrgListByDate4AQXT(String startDay, String endDay) {
        String service = this.qsUrl + "service/findOrgListByDate4AQXT";
        List<OrganizationInfo> orgList = null;

        try {
            if (startDay != null && !startDay.isEmpty()) {
                Map<String, String> params = new HashMap();
                params.put("startDay", startDay);
                params.put("endDay", endDay);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        new ArrayList();
                        String result = objectMapper.readTree(str).get("result").toString();
                        TypeReference<List<OrganizationInfo>> valueTypeRef = new TypeReference<List<OrganizationInfo>>() {
                        };
                        orgList = (List)objectMapper.readValue(result, valueTypeRef);
                    }
                }
            }
        } catch (Exception var13) {
            Exception e = var13;
            e.printStackTrace();
        } finally {
            ;
        }

        return orgList;
    }

    public List<User> findOperateUserListByDate(String startDay, String endDay, String includeOrgCode, String excludeOrgCode) {
        List<User> userList = null;
        String service = this.qsUrl + "service/findOperateUserListByDate";

        try {
            if (startDay != null && !startDay.isEmpty()) {
                Map<String, String> params = new HashMap();
                params.put("startDay", startDay);
                params.put("endDay", endDay);
                params.put("includeOrgCode", URLEncoder.encode(includeOrgCode, "utf-8"));
                params.put("excludeOrgCode", URLEncoder.encode(excludeOrgCode, "utf-8"));
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        new ArrayList();
                        String result = objectMapper.readTree(str).get("result").toString();
                        TypeReference<List<User>> valueTypeRef = new TypeReference<List<User>>() {
                        };
                        userList = (List)objectMapper.readValue(result, valueTypeRef);
                    }
                }
            }
        } catch (Exception var15) {
            Exception e = var15;
            e.printStackTrace();
        } finally {
            ;
        }

        return userList;
    }

    public List<OrganizationInfo> findOperateOrgListByDate(String startDay, String endDay, String includeOrgCode, String excludeOrgCode) {
        List<OrganizationInfo> orgList = null;
        String service = this.qsUrl + "service/findOperateOrgListByDate";

        try {
            if (startDay != null && !startDay.isEmpty()) {
                Map<String, String> params = new HashMap();
                params.put("startDay", startDay);
                params.put("endDay", endDay);
                params.put("includeOrgCode", URLEncoder.encode(includeOrgCode, "utf-8"));
                params.put("excludeOrgCode", URLEncoder.encode(excludeOrgCode, "utf-8"));
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                System.out.println(str);
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        new ArrayList();
                        String result = objectMapper.readTree(str).get("result").toString();
                        TypeReference<List<OrganizationInfo>> valueTypeRef = new TypeReference<List<OrganizationInfo>>() {
                        };
                        orgList = (List)objectMapper.readValue(result, valueTypeRef);
                    }
                }
            }
        } catch (Exception var15) {
            Exception e = var15;
            e.printStackTrace();
        } finally {
            ;
        }

        return orgList;
    }

    public Result modifyIdCardByUuid(String uuid, String idCard, String modifierId) {
        if (!StringUtils.isBlank(uuid) && !StringUtils.isBlank(idCard)) {
            String service = this.qsUrl + "service/modifyIdCardByUuid";
            User user = new User();
            user.setUuid(BasecodeUtil.enString(uuid));
            user.setIdCarId(DES.createDecryptorByKey(idCard, uuid));
            user.setModifierId(BasecodeUtil.enString(modifierId));


            String key = "modifyIdCardByUuid";
            XStream xstream  =  XStreamManager.getxStream(key);
            if(xstream == null){
                xstream = new XStream(new DomDriver());
                xstream.alias("User", User.class);
                xstream.autodetectAnnotations(true);
                XStreamManager.setXStream(key,xstream);
            }

            String conent = xstream.toXML(user);
            Document doc = this.getDoc_forPost(service, RequestMethod.POST, conent);
            IBuilder<Result> builder = (new DelPersonByUidBuilder()).setDocumnet(doc);
            return (Result)builder.getEntity();
        } else {
            Result res = new Result(false, "参数传入错误");
            return res;
        }
    }

    public String findUuidByUid(String uid) {
        String result = "";

        try {
            if (uid != null && !uid.isEmpty()) {
                String service = this.qsUrl + "service/findUuidByUid";
                Map<String, String> params = new HashMap();
                params.put("uid", uid);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        result = info.getResult().toString();
                    }
                }
            }
        } catch (Exception var10) {
            Exception e = var10;
            e.printStackTrace();
        } finally {
            ;
        }

        return result;
    }

    public ParentUsers findPersonOrgListByRidAndDid(List<String> rid, List<String> did, List<String> oid) {
        Map<String, String> params = new HashMap(16);
        params.put("rid", StringUtils.join(rid, "#"));
        params.put("did", StringUtils.join(did, "#"));
        params.put("oid", StringUtils.join(oid, "#"));
        String res = InputStreamHelper.loadJSON(this.qsUrl + "service/findPersonOrgListByRidAndDid", RequestMethod.POST, (Map)null, JSONObject.fromObject(params).toString());
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            return (ParentUsers)objectMapper.readValue(res, ParentUsers.class);
        } catch (IOException var8) {
            return null;
        }
    }

    public ResponseResult<OrganizationExtInfo> findOrgExtAttrByOid(String oid) {
        Map<String, String> params = new HashMap();
        params.put("oid", oid);
        String res = InputStreamHelper.loadJSON(this.qsUrl + "service/findOrgExtAttrByOid", RequestMethod.GET, params, "");
        ObjectMapper objectMapper = new ObjectMapper();
        TypeReference<ResponseResult<OrganizationExtInfo>> valueTypeRef = new TypeReference<ResponseResult<OrganizationExtInfo>>() {
        };

        try {
            return (ResponseResult)objectMapper.readValue(res, valueTypeRef);
        } catch (IOException var7) {
            return null;
        }
    }

    public ResponseResult<Map<String, String>> findOrgOpenKeyByOid(String oid) {
        Map<String, String> params = new HashMap();
        params.put("oid", oid);
        String res = InputStreamHelper.loadJSON(this.qsUrl + "service/findOrgOpenKeyByOid", RequestMethod.GET, params, "");
        TypeReference<ResponseResult<Map<String, String>>> valueTypeRef = new TypeReference<ResponseResult<Map<String, String>>>() {
        };
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            return (ResponseResult)objectMapper.readValue(res, valueTypeRef);
        } catch (IOException var7) {
            return null;
        }
    }

    public User findUserByUid(String uid) {
        User u = new User();
        String service = this.qsUrl + "service/other/findUserByUid";

        try {
            Map<String, String> params = new HashMap();
            params.put("uid", uid);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
            System.err.println("===" + str);
            if (str != null && !str.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                if (info.getResult() != null) {
                    u = (User)objectMapper.convertValue(info.getResult(), User.class);
                }
            }
        } catch (Exception var10) {
            Exception e = var10;
            e.printStackTrace();
        } finally {
            ;
        }

        return u;
    }

    public Response changePersonAndRoleAndOuid(List<OperationUserAndRoleAndOuid> uroArry, String uuid) {
        Response info = new Response();
        String service = this.qsUrl + "service/changePersonAndRoleAndOuid";
        Map<String, String> params = new HashMap();
        params.put("uuid", uuid);
        JSONArray json = JSONArray.fromObject(uroArry);
        String conent = json.toString();
        System.err.println(conent);
        String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, params, conent);
        if (str != null && !str.isEmpty()) {
            ObjectMapper objectMapper = new ObjectMapper();

            try {
                info = (Response)objectMapper.readValue(str, Response.class);
            } catch (JsonParseException var11) {
                JsonParseException e = var11;
                e.printStackTrace();
            } catch (JsonMappingException var12) {
                JsonMappingException e = var12;
                e.printStackTrace();
            } catch (IOException var13) {
                IOException e = var13;
                e.printStackTrace();
            }
        }

        return info;
    }

    public ResponseResult<List<OrgDataInfo>> queryZhddCenterByUid(String uid, String oid) {
        Map<String, String> params = new HashMap();
        params.put("uid", uid);
        params.put("oid", oid);
        String res = InputStreamHelper.loadJSON(this.qsUrl + "service/api/queryZhddCenterByUid", RequestMethod.GET, params, "");
        System.out.println(res);
        TypeReference<ResponseResult<List<OrgDataInfo>>> valueTypeRef = new TypeReference<ResponseResult<List<OrgDataInfo>>>() {
        };
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            return (ResponseResult)objectMapper.readValue(res, valueTypeRef);
        } catch (IOException var8) {
            return null;
        }
    }

    public ResponseResult<UserExtInfo> findUserExtInfoByUid(String uid) {
        Map<String, String> params = new HashMap();
        params.put("uid", uid);
        String res = InputStreamHelper.loadJSON(this.qsUrl + "service/findUserExtInfoByUid", RequestMethod.GET, params, "");
        System.err.println(res);
        ObjectMapper objectMapper = new ObjectMapper();
        TypeReference<ResponseResult<UserExtInfo>> valueTypeRef = new TypeReference<ResponseResult<UserExtInfo>>() {
        };

        try {
            return (ResponseResult)objectMapper.readValue(res, valueTypeRef);
        } catch (IOException var7) {
            return null;
        }
    }

    public List<ModuleInfo> findCustomAuthority(String custom, String uid, String ouid, String scope4query) {
        List<ModuleInfo> mList = null;
        String service = this.qsUrl + "service/findCustomAuthority";
        if (custom != null && !StringUtils.isEmpty(custom)) {
            try {
                Map<String, String> params = new HashMap();
                params.put("custom", custom);
                params.put("userId", uid);
                params.put("ouId", ouid);
                params.put("scope4query", scope4query);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                System.out.println(str);
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        new ArrayList();
                        String result = objectMapper.readTree(str).get("result").toString();
                        TypeReference<List<ModuleInfo>> valueTypeRef = new TypeReference<List<ModuleInfo>>() {
                        };
                        mList = (List)objectMapper.readValue(result, valueTypeRef);
                    }
                }
            } catch (Exception var15) {
                Exception e = var15;
                e.printStackTrace();
            } finally {
                ;
            }

            return mList;
        } else {
            return null;
        }
    }

    public List<ModuleInfo> findLevelDown(String pId, String uid, String ouid, String scope4query) {
        List<ModuleInfo> mList = null;
        String service = this.qsUrl + "service/findLevelDown";
        if (pId != null && !StringUtils.isEmpty(pId)) {
            try {
                Map<String, String> params = new HashMap();
                params.put("pId", pId);
                params.put("userId", uid);
                params.put("ouId", ouid);
                params.put("scope4query", scope4query);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                System.out.println(str);
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    ResultResponse info = (ResultResponse)objectMapper.readValue(str, ResultResponse.class);
                    if (info.getResult() != null) {
                        new ArrayList();
                        String result = objectMapper.readTree(str).get("result").toString();
                        TypeReference<List<ModuleInfo>> valueTypeRef = new TypeReference<List<ModuleInfo>>() {
                        };
                        mList = (List)objectMapper.readValue(result, valueTypeRef);
                    }
                }
            } catch (Exception var15) {
                Exception e = var15;
                e.printStackTrace();
            } finally {
                ;
            }

            return mList;
        } else {
            return null;
        }
    }

    public Response updateOrgCodeByOid(String oid, String oldCode, String code, String modifierId) {
        Response info = new Response();
        String service = this.qsUrl + "service/updateOrgCodeByOid";
        if (!StringUtils.isEmpty(oid) && !StringUtils.isEmpty(oldCode) && !StringUtils.isEmpty(code) && !StringUtils.isEmpty(modifierId)) {
            try {
                Map<String, String> params = new HashMap();
                params.put("oid", oid);
                params.put("oldCode", oldCode);
                params.put("code", code);
                params.put("modifierId", modifierId);
                String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, "");
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    info = (Response)objectMapper.readValue(str, Response.class);
                }
            } catch (JsonParseException var14) {
                JsonParseException e = var14;
                e.printStackTrace();
            } catch (JsonMappingException var15) {
                JsonMappingException e = var15;
                e.printStackTrace();
            } catch (IOException var16) {
                IOException e = var16;
                e.printStackTrace();
            } finally {
                ;
            }

            return info;
        } else {
            return null;
        }
    }

    public Response dataSync(ChangeData data) {
        if (data != null && !StringUtils.isEmpty(data.getContent()) && !StringUtils.isEmpty(data.getEvent()) && !StringUtils.isEmpty(data.getUuid())) {
            Response info = new Response();
            String service = this.qsUrl + "service/data-sync/operate";
            JSONObject obj = JSONObject.fromObject(data);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, (Map)null, obj.toString());
            if (StringUtils.isEmpty(str)) {
                return new Response(40094);
            } else {
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();

                    try {
                        info = (Response)objectMapper.readValue(str, Response.class);
                    } catch (JsonParseException var8) {
                        JsonParseException e = var8;
                        e.printStackTrace();
                    } catch (JsonMappingException var9) {
                        JsonMappingException e = var9;
                        e.printStackTrace();
                    } catch (IOException var10) {
                        IOException e = var10;
                        e.printStackTrace();
                    }
                }

                return info;
            }
        } else {
            return new Response(40035);
        }
    }

    public Response importModule(List<ModuleInfoRequest> modList, String uuid, String appId, String appCode) {
        if (!CollectionUtils.isEmpty(modList) && !StringUtils.isEmpty(uuid) && !StringUtils.isEmpty(appId) && !StringUtils.isEmpty(appCode)) {
            Response info = new Response();
            String service = this.qsUrl + "service/import_module";
            modList.remove((Object)null);
            Collections.sort(modList, new Comparator<ModuleInfoRequest>() {
                public int compare(ModuleInfoRequest o1, ModuleInfoRequest o2) {
                    return o1 != null && o2 != null ? o1.getId().compareTo(o2.getId()) : 0;
                }
            });
            List<String> dataIds = new ArrayList();
            Iterator var9 = modList.iterator();

            while(var9.hasNext()) {
                ModuleInfoRequest m = (ModuleInfoRequest)var9.next();
                dataIds.add(m.getId());
            }

            String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, (Map)null, JSONUtils.valueToString(modList), this.pageHeaderMap(uuid, appId, appCode, StringUtils.join(dataIds, "")));
            if (StringUtils.isEmpty(str)) {
                return new Response(40094);
            } else {
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();

                    try {
                        info = (Response)objectMapper.readValue(str, Response.class);
                    } catch (JsonParseException var11) {
                        JsonParseException e = var11;
                        e.printStackTrace();
                    } catch (JsonMappingException var12) {
                        JsonMappingException e = var12;
                        e.printStackTrace();
                    } catch (IOException var13) {
                        IOException e = var13;
                        e.printStackTrace();
                    }
                }

                return info;
            }
        } else {
            return new Response(40035);
        }
    }

    public Map<String, String> pageHeaderMap(String uuid, String appId, String appCode, String dataIds) {
        long stm = System.currentTimeMillis();
        Map<String, String> header = new HashedMap();
        header.put(this.APPID, appId);
        header.put(this.MODIFIERID, uuid);
        header.put(this.TIMESTAMP, String.valueOf(stm));
        header.put(this.APPAESSTR, this.aesStr(appId, appCode, stm, dataIds));
        return header;
    }

    private String aesStr(String appId, String code, long timestamp, String dataId) {
        return DigestUtils.md5Hex(AESEncrypting.encrypt(appId + code + timestamp + dataId, "MicroVideoAESSec"));
    }

    public Response saveModule(ModuleInfoRequest mod, String uuid, String appId, String appCode) {
        if (mod != null && !StringUtils.isEmpty(mod.getId()) && !StringUtils.isEmpty(mod.getParent()) && !StringUtils.isEmpty(mod.getName()) && !StringUtils.isEmpty(mod.getType()) && !StringUtils.isEmpty(mod.getCode()) && !StringUtils.isEmpty(mod.getUrl()) && !StringUtils.isEmpty(uuid) && !StringUtils.isEmpty(appId) && !StringUtils.isEmpty(appCode)) {
            Response info = new Response();
            String service = this.qsUrl + "service/save_module";
            JSONObject obj = JSONObject.fromObject(mod);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, (Map)null, obj.toString(), this.pageHeaderMap(uuid, appId, appCode, mod.getId()));
            if (StringUtils.isEmpty(str)) {
                return new Response(40094);
            } else {
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();

                    try {
                        info = (Response)objectMapper.readValue(str, Response.class);
                    } catch (JsonParseException var11) {
                        JsonParseException e = var11;
                        e.printStackTrace();
                    } catch (JsonMappingException var12) {
                        JsonMappingException e = var12;
                        e.printStackTrace();
                    } catch (IOException var13) {
                        IOException e = var13;
                        e.printStackTrace();
                    }
                }

                return info;
            }
        } else {
            return new Response(40035);
        }
    }

    public Response modifyModule(ModuleInfoRequest mod, String uuid, String appId, String appCode) {
        if (mod != null && !StringUtils.isEmpty(mod.getId()) && !StringUtils.isEmpty(mod.getParent()) && !StringUtils.isEmpty(mod.getName()) && !StringUtils.isEmpty(mod.getCode()) && !StringUtils.isEmpty(mod.getUrl()) && !StringUtils.isEmpty(uuid) && !StringUtils.isEmpty(appId) && !StringUtils.isEmpty(appCode)) {
            Response info = new Response();
            String service = this.qsUrl + "service/modify_module";
            JSONObject obj = JSONObject.fromObject(mod);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.POST, (Map)null, obj.toString(), this.pageHeaderMap(uuid, appId, appCode, mod.getId()));
            if (StringUtils.isEmpty(str)) {
                return new Response(40094);
            } else {
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();

                    try {
                        info = (Response)objectMapper.readValue(str, Response.class);
                    } catch (JsonParseException var11) {
                        JsonParseException e = var11;
                        e.printStackTrace();
                    } catch (JsonMappingException var12) {
                        JsonMappingException e = var12;
                        e.printStackTrace();
                    } catch (IOException var13) {
                        IOException e = var13;
                        e.printStackTrace();
                    }
                }

                return info;
            }
        } else {
            return new Response(40035);
        }
    }

    public Response deleteModule(String mId, String uuid, String appId, String appCode) {
        if (!StringUtils.isEmpty(mId) && !StringUtils.isEmpty(uuid) && !StringUtils.isEmpty(appId) && !StringUtils.isEmpty(appCode)) {
            Response info = new Response();
            String service = this.qsUrl + "service/delete_module";
            Map<String, String> params = new HashMap();
            params.put("mId", mId);
            String str = InputStreamHelper.loadJSON(service, RequestMethod.GET, params, (String)null, this.pageHeaderMap(uuid, appId, appCode, mId));
            if (StringUtils.isEmpty(str)) {
                return new Response(40094);
            } else {
                if (str != null && !str.isEmpty()) {
                    ObjectMapper objectMapper = new ObjectMapper();

                    try {
                        info = (Response)objectMapper.readValue(str, Response.class);
                    } catch (JsonParseException var11) {
                        JsonParseException e = var11;
                        e.printStackTrace();
                    } catch (JsonMappingException var12) {
                        JsonMappingException e = var12;
                        e.printStackTrace();
                    } catch (IOException var13) {
                        IOException e = var13;
                        e.printStackTrace();
                    }
                }

                return info;
            }
        } else {
            return new Response(40035);
        }
    }

    public ResponseResult<List<User>> findUserListByPositionId(String positionId, String oid) {
        Map<String, String> params = new HashMap();
        params.put("oid", oid);
        params.put("positionId", positionId);
        String res = InputStreamHelper.loadJSON(this.qsUrl + "service/position/findUserListByPositionId", RequestMethod.GET, params, "");
        TypeReference<ResponseResult<List<User>>> valueTypeRef = new TypeReference<ResponseResult<List<User>>>() {
        };

        try {
            return (ResponseResult)this.jobjectMapper.readValue(res, valueTypeRef);
        } catch (IOException var7) {
            return null;
        }
    }

    public ResponseResult<List<Position>> findPositionByOid(String oid) {
        Map<String, String> params = new HashMap();
        params.put("oid", oid);
        String res = InputStreamHelper.loadJSON(this.qsUrl + "service/position/findPositionsByOid", RequestMethod.GET, params, "");
        TypeReference<ResponseResult<List<Position>>> valueTypeRef = new TypeReference<ResponseResult<List<Position>>>() {
        };

        try {
            return (ResponseResult)this.jobjectMapper.readValue(res, valueTypeRef);
        } catch (IOException var6) {
            return null;
        }
    }
}
