package cn.microvideo.module.plyh.commonconstant;

public interface CommonConstant {
    /**
     * 正常状态
     */
    public static final Integer STATUS_NORMAL = 0;

    /**
     * 禁用状态
     */
    public static final Integer STATUS_DISABLE = -1;

    /**
     * 删除标志
     */
    public static final Integer DEL_FLAG_1 = 1;

    /**
     * 未删除
     */
    public static final Integer DEL_FLAG_0 = 0;

    /**
     * 系统日志类型： 登录
     */
    public static final int LOG_TYPE_1 = 1;

    /**
     * 系统日志类型： 操作
     */
    public static final int LOG_TYPE_2 = 2;

    /**
     * 用户的serviceSystemId SESION
     */
    public static final String SESSION_SERVICE_SYSTEM = "SESSION_SERVICE_SYSTEM";

    /**
     * {@code 500 Server Error} (HTTP/1.0 - RFC 1945)
     */
    public static final Integer ERROR_500 = 500;

    /**
     * {@code 200 OK} (HTTP/1.0 - RFC 1945)
     */
    public static final Integer OK_200 = 200;

    /**
     * 访问权限认证未通过 510
     */
    public static final Integer NO_AUTHZ = 510;

    /**
     * 访问权限认证未通过 510
     */
    public static final Integer NO_LOGIN = 401;

    /**
     * 文件导入和错误提示
     */
    public static final Integer TEXT_ERROR_CODE = 20500;

    /**
     * 登录用户令牌缓存KEY前缀
     */
    public static final int TOKEN_EXPIRE_TIME = 3600; // 3600秒即是一小时

    public static final String PREFIX_USER_TOKEN = "PREFIX_USER_TOKEN_";

    /**
     * 字典翻译文本后缀
     */
    public static final String DICT_TEXT_SUFFIX = "_dictText";
    /**
     * 国密加密秘钥（必须设置成16位）
     */
    public static final String SECRET_KEY = "9977235587108266";
}
