package cn.microvideo.module.plyh.commonconstant.Enum;


public enum NodeTypeEnum {
    USER("users", "用户"),
    ORG("org", "部门"),
    DEPT("dept", "单位");


    private String code;
    private String name;

    NodeTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (NodeTypeEnum value : NodeTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }


}
