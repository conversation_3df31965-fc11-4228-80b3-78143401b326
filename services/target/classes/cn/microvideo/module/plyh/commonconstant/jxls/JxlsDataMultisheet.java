package cn.microvideo.module.plyh.commonconstant.jxls;

import cn.microvideo.framework.core.util.jxls.data.JxlsData;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Collections;
import java.util.List;

/**
 * <b>描述:</b>
 *
 * <p>多工作薄数据</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2020年2月25日上午11:33:38
 * @since JDK1.8
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class JxlsDataMultisheet extends JxlsData {

    /**
     * 使用数据和工作薄名集合构建对象
     *
     * @param data       数据
     * @param sheetNames 工作薄名集合
     */
    public JxlsDataMultisheet(Object data, List<String> sheetNames) {
        super(data);
        this.sheetNames = sheetNames;
    }

    /**
     * 工作薄名集合
     */
    private List<String> sheetNames = Collections.emptyList();
}
