package cn.microvideo.module.plyh.commonconstant;

/**
 * 系统配置常量
 */

public class Contant {
    //推送门户类型通知
    public static final String NOTICE_TYPE_0 = "通知公告";
    //推送门户类型公告
    public static final String NOTICE_TYPE_1 = "1";

    //新增状态
    public static final String ADD_DATA_TYPE = "add";
    //更新状态
    public static final String DETAILS_DATA_TYPE = "details";


    //未删除
    public static final Integer DEL_FLAG_0 = 0;
    //已删除
    public static final Integer DEL_FLAG_1 = 1;

    /**
     * 显示状态
     */
    //不显示状态
    public static final String HELP_VIEW_FALSE = "0";
    //显示状态
    public static final String HELP_VIEW_TRUE = "1";

    /**
     * 审核状态
     */
    //未审核
    public static final String AUDIT_STATUS_0 = "0";
    //同意
    public static final String AUDIT_STATUS_1 = "1";
    //拒绝
    public static final String AUDIT_STATUS_2 = "2";

    //下标0
    public static final String STR_INT_INDEX_0 = "0";

    /**
     * 审核状态
     */
    //待办
    public static final Integer TASK_STATUS_0 = 0;
    //已办
    public static final Integer TASK_STATUS_1 = 1;
    //撤回或者删除
    public static final Integer TASK_STATUS_2 = 2;


    //下标0
    public static final Integer INT_INDEX_0 = 0;

    //下标0
    public static final Integer INT_INDEX_2 = 2;
    //下标4
    public static final Integer INT_INDEX_4 = 4;

    public static final Integer INT_INDEX_30 = 30;
    //常量数字1
    public static final Integer INT_NUMBER_1 = 1;

    //首页图标列表
    public static final String ICON_CODE = "homePageCode";


    //获取启动流程的Key
    public static final String SOLE_KEY = "mainKey";

    //数据来源状态0
    public static final String INPORT_TYPE_0 = "0";
    //数据来源状态1
    public static final String INPORT_TYPE_1 = "1";


    //判断是否是管理员
    //不是管理员
    public static final Integer IS_ADMIN_0 = 0;
    //是管理员
    public static final Integer IS_ADMIN_1 = 1;

    //用户初始密码
    public static final String PASSWORD = "qwer@1234";


    //叶子节点的标识
    public static final String LEAF_YES = "y";
    //数据来源状态1
    public static final String LEAF_NO = "n";


    //https   Integer 返回码
    public static final Integer INTEGER_HTTP_OK_0 = 0;


    /**
     * java程序时间格式
     */
    public static final String DATA_YEAR = "yyyy";
    public static final String DATA_MONTH = "yyyy-MM";
    //年月日
    public static final String DATA_DAY = "yyyy-MM-dd";
    //年月日时分
    public static final String DATA_MIN = "yyyy-MM-dd HH:mm";
    //年月日时分秒
    public static final String DATA_SEC = "yyyy-MM-dd HH:mm:ss";

    /**
     * 数据库查询的时间格式
     */
    //年月日
    public static final String DATA_MYSQL_DAY = "%Y-%m-%d";
    //年月日时分
    public static final String DATA_MYSQL_MIN = "%Y-%m-%d %H:%i";
    //年月日时分秒
    public static final String DATA_MYSQL_SEC = "%Y-%m-%d %H:%i:%s";


    //http/https正确请返回码
    public static final Integer HTTP_OK_0 = 0;

    public static final Integer HTTP_OK_200 = 200;

    //http/https正确请返回码
    public static final String STR_HTTP_OK_0 = "0";

    public static final String STR_HTTP_OK_200 = "200";


    //sheet页名
    public static final String SHEET_TITLE_NAME = "%s%s年%s月值班表";
    //sheet页名
    public static final String SHEET_NAME = "值班表";

    public static final String WORKBOOK_TITLE_NAME = "%s%s年%s月值班表.xlsx";


    /**
     * 日期类型
     */
    //节假日
    public static final Integer DATE_TYPE_1 = 1;
    //周六日
    public static final Integer DATE_TYPE_2 = 2;
    //调班
    public static final Integer DATE_TYPE_3 = 3;


    public static final String HOLIDAY_NAME_1 = "节假日";

    public static final String HOLIDAY_NAME_3 = "调休";


}
