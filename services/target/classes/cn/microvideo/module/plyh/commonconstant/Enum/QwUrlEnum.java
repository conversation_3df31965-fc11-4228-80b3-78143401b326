package cn.microvideo.module.plyh.commonconstant.Enum;


public enum QwUrlEnum {
    ACCESS_TOKEN("https://qyapi.weixin.qq.com/cgi-bin/gettoken", "access_token获取"),
    GET_USERINFO("https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo", "用户身份"),
    GET_USER_DETAIL("https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail", "用户敏感信息"),
    SEND_MESSAGE_URL("https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=", "发送企业微信应用消息");


    private String url;
    private String name;

    QwUrlEnum(String url, String name) {
        this.url = url;
        this.name = name;
    }

    public static String getName(String code) {
        for (QwUrlEnum value : QwUrlEnum.values()) {
            if (value.getUrl().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getName() {
        return name;
    }

    public String getUrl() {
        return url;
    }


}
