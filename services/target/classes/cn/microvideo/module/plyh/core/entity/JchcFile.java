package cn.microvideo.module.plyh.core.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 文件管理表
 * @Author: spring-boot
 * @Date:   2023-08-31
 * @Version: V1.0
 */
@Data
@TableName("b_plyh_file")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="b_plyh_file对象", description="文件管理表")
public class JchcFile {
    
	/**物理主键*/
	
    @ApiModelProperty(value = "物理主键")
    @TableId("F_VC_ID")
	private String id;
	/**表单主键*/
	
    @ApiModelProperty(value = "表单主键")
    @TableField("F_VC_FORM_ID")
	private String formId;
	/**文件名称*/
	
    @ApiModelProperty(value = "文件名称")
    @TableField("F_VC_FILE_NAME")
	private String fileName;
	/**文件类型*/
	
    @ApiModelProperty(value = "文件类型")
    @TableField("F_VC_CONTENT_TYPE")
	private String contentType;
	/**文件大小*/
	
    @ApiModelProperty(value = "文件大小")
    @TableField("F_NB_TOTAL_SPACE")
	private Integer totalSpace;
	/**对象名称*/
	
    @ApiModelProperty(value = "对象名称")
    @TableField("F_VC_OBJECT_NAME")
	private String objectName;
	/**上传时间*/
	
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上传时间")
    @TableField("F_DT_UPLOAD_TIME")
	private Date uploadTime;
	/**上传人ID*/
	
    @ApiModelProperty(value = "上传人ID")
    @TableField("F_VC_USER_ID")
	private String userId;
	/**上传人名*/
	
    @ApiModelProperty(value = "上传人名")
    @TableField("F_VC_USER_NAME")
	private String userName;
	/**预览ID*/
	
    @ApiModelProperty(value = "预览ID")
    @TableField("F_VC_IDOCVIEW_UUID")
	private String idocviewUuid;
	/**文件类别 0：一开始上传合同 1: 归档文件2:重大合同 3：规章制度 4：背景资料 5：外聘法务部门文件 6内部法务文件 7合同分类说明*/
	
    @ApiModelProperty(value = "文件类别 0：一开始上传合同 1: 归档文件2:重大合同 3：规章制度 4：背景资料 5：外聘法务部门文件 6内部法务文件 7合同分类说明")
    @TableField("F_NB_KIND")
	private Integer kind;
	/**是否删除*/
	
    @ApiModelProperty(value = "是否删除")
    @TableField("F_INT_DEL")
	private Integer del;

    /**上传人名*/
    @ApiModelProperty(value = "文件编码")
    @TableField("F_VC_FILE_CODE")
    private String fileCode;
}
