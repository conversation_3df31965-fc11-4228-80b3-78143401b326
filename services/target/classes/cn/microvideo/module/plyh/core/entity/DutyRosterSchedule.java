package cn.microvideo.module.plyh.core.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 值班排班时间子表

 * @since 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_duty_roster_schedules")
public class DutyRosterSchedule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 本条记录的唯一ID
     */
    @TableId(value = "F_VC_ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 外键, 关联到值班主表ID (t_duty_rosters.F_VC_ID)
     */
    @TableField("F_VC_ROSTER_ID")
    private String rosterId;

    /**
     * 开始日期 (格式: yyyy-MM-dd)
     */
    @TableField("F_DT_START_TIME")
    private LocalDate startTime;

    /**
     * 结束日期 (格式: yyyy-MM-dd)
     */
    @TableField("F_DT_END_TIME")
    private LocalDate endTime;

}
