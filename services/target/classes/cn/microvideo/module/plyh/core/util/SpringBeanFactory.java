package cn.microvideo.module.plyh.core.util;

import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.stereotype.Component;

/**
 * <b>描述:</b>
 *
 * <p>获取SpringIOC容器中的Bean</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2019年4月11日下午1:32:44
 * @since JDK1.8
 */
@Component
public class SpringBeanFactory implements BeanFactoryAware {

    /**
     * Bean工厂
     */
    private static BeanFactory beanFactory;

    /**
     * 注入Bean工厂
     */
    @Override
    public void setBeanFactory(BeanFactory beanFactory) {
        SpringBeanFactory.beanFactory = beanFactory;
    }

    /**
     * 根据名称获取Bean对象
     *
     * @param name
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T getBean(String name) {
        if (null != beanFactory) {
            return (T) beanFactory.getBean(name);
        }
        return null;
    }

    /**
     * 根据类型对象从Spring中获取实例对象
     *
     * @param clazz
     * @return
     */
    public static <T> T getBean(Class<T> clazz) {
        if (null != beanFactory) {
            return beanFactory.getBean(clazz);
        }
        return null;
    }

}
