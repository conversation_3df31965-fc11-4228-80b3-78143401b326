package cn.microvideo.module.plyh.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 节假日、休息、值班管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Data
@TableName("b_plyh_holiday")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "b_plyh_holiday对象", description = "节假日、休息、值班管理表")
public class Holiday {

    /**
     * UUID
     */

    @ApiModelProperty(value = "UUID")
    @TableId("F_VC_ID")
    private String id;
    /**
     * 日期时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "日期时间")
    @TableField("F_DT_HOLIDAY_DATA")
    private Date holidayData;
    /**
     * 类型 1 节假日-假   2周末-休  3班（正常上班-调休一类）
     */

    @ApiModelProperty(value = "类型 1 节假日-假   2周末-休  3班（正常上班-调休一类）")
    @TableField("F_INT_HOLIDAY_TYPE")
    private Integer holidayType;

    /**
     * 周六、周日、节假日
     */

    @ApiModelProperty(value = "周六、周日、节假日")
    @TableField("F_VC_HOLIDAY_NAME")
    private String holidayName;
}
