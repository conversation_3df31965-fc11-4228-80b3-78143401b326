package cn.microvideo.module.plyh.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ActivityProIdUserVO对象", description = "获取节点和人员数据")
public class ActivityProIdUserVO {
    @ApiModelProperty(value = "节点名称")
    private String taskName;
    @ApiModelProperty(value = "节点Id")
    private String taskId;
    @ApiModelProperty(value = "节点类型")
    private String nodeType;
    @ApiModelProperty(value = "节点下的人员")
    private List<ActivityUserVO> extObj;
}
