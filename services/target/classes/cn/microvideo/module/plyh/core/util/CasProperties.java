package cn.microvideo.module.plyh.core.util;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @description: Cas配置类
 * @author: Mr<PERSON>
 * @date: 2023/2/14 16:06
 */
@Component
@Data
@ConfigurationProperties(prefix = "microvideo.cas")
public class CasProperties {
    /**
     * 全局会话key
     */
    private String sessionKey;

    //忽略配置
    private String serverIgnoreUrl;
    //后端服务地址
    private String clientHostUrl;
    //前端服务地址
    private String serverHtmlUrl;
    //服务重定向地址
    private String baseServerUrl;
    //企业微信的cas的配置
    private String interceptorsIgnoreUrl;

    //服务重定向地址
    private String serverLogoutUrl;
}
