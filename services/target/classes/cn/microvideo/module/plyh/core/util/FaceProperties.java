package cn.microvideo.module.plyh.core.util;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @description: 基础配置类
 * @author: Mr<PERSON>
 * @date: 2023/2/14 16:06
 */
@Component
@Data
@ConfigurationProperties(prefix = "portal.server")
public class FaceProperties {
    //门户地址
    private String url;
    //本地服务地址
    private String noticeUrl;
}
