package cn.microvideo.module.plyh.core.service;

import cn.microvideo.module.plyh.core.entity.HolidayDeptUser;
import cn.microvideo.module.plyh.core.vo.AddHolidayUserVO;
import cn.microvideo.module.plyh.core.vo.HolidayDeptUserDetailVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 节假日、部门关系管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface IHolidayDeptUserService extends IService<HolidayDeptUser> {

    /**
     * 获取数据
     *
     * @param holidayIds
     * @param deptIds
     * @return
     */
    public List<HolidayDeptUserDetailVO> selectByHolidayDeptIds(List<String> holidayIds, List<String> deptIds);


    /**
     * 根据日期和部门ids查询数据
     * @param holidayId
     * @param deptIds
     * @return
     */
    public List<HolidayDeptUser> selectByHolidayIdDeptIds(String holidayId,List<String> deptIds);


    /**
     * 批量新增
     * @param holidayDeptUser
     */
    public void saveByBatchEntity(List<AddHolidayUserVO> holidayDeptUser);


    /**
     * 根据日期IDS 和 部门Id获取数据
     * @param holidayIds
     * @param deptId
     * @return
     */
    public List<HolidayDeptUserDetailVO> selectByHolidayIdsWithDept(String holidayIds,String deptId);


    /**
     * 根据Ids删除数据
     * @param holidayIds
     */
    public void deleteByHolidayIds(List<String> holidayIds);


    /**
     * 获取数据
     * @param deptId
     * @return
     */
    public List<HolidayDeptUser> selectByDeptId(String deptId);


    /**
     * 根据ids 和部门Id查询数据
     * @param holidayIds
     * @param deptId
     * @return
     */
    public List<HolidayDeptUser> selectByHolidayDeptId(List<String> holidayIds, String deptId);



}
