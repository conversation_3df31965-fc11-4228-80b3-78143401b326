package cn.microvideo.module.plyh.core.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "NodeOpinionVO对象", description = "审批记录")
public class NodeOpinionVO {
    @ApiModelProperty(value = "用户ID")
    private String userId;
    @ApiModelProperty(value = "用户ID")
    private String procDefId;
    @ApiModelProperty(value = "processId")
    private String procId;
    @ApiModelProperty(value = "任务ID")
    private String taskId;
    @ApiModelProperty(value = "来自节点状态")
    private String fromNodeId;
    @ApiModelProperty(value = "任务ID")
    private String toNodeId;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "审批意见")
    private String opinion;
    @ApiModelProperty(value = "任务名称")
    private String statusVal;
    @ApiModelProperty(value = "任务名称")
    private String innerStatus;
    @ApiModelProperty(value = "任务名称")
    private String activityInstanceId;
    @ApiModelProperty(value = "节点状态")
    private String nodeType;
    @ApiModelProperty(value = "用户名")
    private String userName;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "标题")
    private String taskUserType;
    private String nodeRunId;
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "任务名称")
    private String preTaskId;
    @ApiModelProperty(value = "任务名称")
    private Integer endStatus;
    @ApiModelProperty(value = "任务名称")
    private Integer suspensionState;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    @ApiModelProperty(value = "转办记录")
    private List<PrevionVO> transferTaskLogs;

    @ApiModelProperty(value = "召回")
    private String recall;

    @ApiModelProperty(value = "序号")
    private Integer number;
    @ApiModelProperty(value = "手写意见")
    private String contentImg;

}
