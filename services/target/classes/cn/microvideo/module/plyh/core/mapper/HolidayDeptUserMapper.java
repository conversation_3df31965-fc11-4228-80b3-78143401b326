package cn.microvideo.module.plyh.core.mapper;

import cn.microvideo.module.plyh.core.entity.HolidayDeptUser;
import cn.microvideo.module.plyh.core.vo.HolidayDeptUserDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 节假日、部门关系管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface HolidayDeptUserMapper extends BaseMapper<HolidayDeptUser> {
    /**
     * 根据holidayIds 和部门获取数据
     *
     * @param holidayIds
     * @param deptIds
     * @return
     */
    public List<HolidayDeptUserDetailVO> selectByHolidayDeptIds(@Param("holidayIds") List<String> holidayIds, @Param("deptIds") List<String> deptIds);


    /**
     * 根据日期ID和部门ID获取数据
     * @param holidayIds
     * @param deptId
     * @return
     */
    public List<HolidayDeptUserDetailVO> selectByHolidayIdsWithDept(@Param("holidayIds") String holidayIds, @Param("deptId") String deptId);

}
