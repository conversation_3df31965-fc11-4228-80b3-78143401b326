package cn.microvideo.module.plyh.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "AgreeActiviotyVO对象", description = "工作流提交用到的字段")
public class AgreeActiviotyVO {
    @ApiModelProperty(value = "签报UUID")
    private String uuid;
    @ApiModelProperty(value = "任务UUID")
    private String taskUuid;
    @ApiModelProperty(value = "按钮的别名(agree)")
    private String action;
    @ApiModelProperty(value = "按钮的中文(agree)")
    private String actionName;
    @ApiModelProperty(value = "按钮的别名(agree)")
    private String chooseNode;
    @ApiModelProperty(value = "选择的下一个节点名称")
    private String chooseNodeUser;
    @ApiModelProperty(value = "选择的下一个节点处理人")
    private String currentUserId;
    @ApiModelProperty(value = "当前人员id")
    private String currentUserName;
    @ApiModelProperty(value = "当前人员")
    private String formData;
    @ApiModelProperty(value = "表单数据（推送人数据 例如{'userList':'fN71BN0texSA8LeKS0cNhPadm000EA,0818vYal8fGxtzgqdEqZwv019AkGhb'}")
    private String formId;
    @ApiModelProperty(value = "表单ID")
    private Boolean monitor;
    @ApiModelProperty(value = "处理意见")
    private String opinion;
    @ApiModelProperty(value = "操作按钮")
    private String operate;
    @ApiModelProperty(value = "优先级")
    private String priority;
    @ApiModelProperty(value = "任务ID")
    private String taskId;
    @ApiModelProperty(value = "类型标题")
    private String taskTitle;
    @ApiModelProperty(value = "处理人，多个用户逗号拼接")
    private String userList;
    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;
    @ApiModelProperty(value = "跳转节点(退回必传)")
    private String toTaskDefKey;
    @ApiModelProperty(value = "退回用户ID")
    private String userId;
    @ApiModelProperty(value = "退回用户名称")
    private String userName;
    @ApiModelProperty(value = "节点ID(撤销的参数)")
    private String taskDefinitionKey;
    @ApiModelProperty(value = "用于转办")
    private ZbUserVO usersInfo;
    @ApiModelProperty(value = "选中的转办人数据（用于转办）")
    private TransferUserVO transferUserVO;

}
