package cn.microvideo.module.plyh.core.service;

import cn.microvideo.module.plyh.core.vo.SendMessageResultVO;

/**
 * @Description: bpm流程调用表
 * @Author: spring-boot
 * @Date: 2023-09-05
 * @Version: V1.0
 */
public interface WXService {
    /**
     * 获取人员基础信息
     *
     * @param
     * @return
     */
    public String selectByToken(String appCode, Integer appData);


    /**
     * 发送企业微信消息
     *
     * @param title
     * @param desc
     * @param url
     * @param touser
     * @param agentid
     * @return
     */
    public SendMessageResultVO sendWeChatMessage(String title, String desc, String url, String touser, Integer agentid);

    /**
     * 发送企业微信消息
     *
     * @param title
     * @param desc
     * @param url
     * @param touser
     * @param agentid
     * @return
     */
    public SendMessageResultVO sendWeChatMessagePic(String title, String desc, String url, String touser, String pic, Integer agentid);

}
