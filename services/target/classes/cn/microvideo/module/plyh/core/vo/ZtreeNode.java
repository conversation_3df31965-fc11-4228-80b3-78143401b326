package cn.microvideo.module.plyh.core.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <b>描述:</b>
 *
 * <p>ZTreeNode节点数据结构</p>
 *
 * @version 1.0
 * <AUTHOR>
 * @Date 2019年4月16日下午2:32:57
 * @since JDK1.8
 */
@Data
public class ZtreeNode implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * NAV ID
	 */
	private String id;
	/**
	 * 导航名称
	 */
	private String name;
	/**
	 * 父ID
	 */
	private String parentId;

	private String extendName;

	/**
	 * 是否显示checkbox/radio
	 */
	private boolean nocheck = true;
	/**
	 * 是否父节点
	 */
	private boolean isParent = true;
	/**
	 * 扩展属性
	 */
	private Map<String, String> extAttrs = new HashMap<>();
	/**
	 * default constructor
	 */
	public ZtreeNode(){
		
	}
	/**
	 * 使用ID和名称构造
	 * @param id
	 * @param name
	 */
	public ZtreeNode(String id, String name) {
		this.id = id;
		this.name = name;
	}
	/**
	 * -使用zTree参数构造导航节点
	 * @param isParent
	 * @param nocheck
	 */
	public ZtreeNode(boolean isParent, boolean nocheck) {
		this.setParent(isParent);
		this.setNocheck(nocheck);
	}
	/**
	 * @param nocheck the nocheck to set
	 */
	public void setNocheck(boolean nocheck) {
		this.nocheck = nocheck;
	}
	/**
	 * @return the nocheck
	 */
	public boolean isNocheck() {
		return nocheck;
	}
	/**
	 * @param isParent the isParent to set
	 */
	public void setParent(boolean isParent) {
		this.isParent = isParent;
	}
	
	public boolean isisParent() {
		return this.isParent;
	}
	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}
	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
	/**
	 * @param name the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}
	/**
	 * @return the parentId
	 */
	public String getParentId() {
		return parentId;
	}
	/**
	 * @param parentId the parentId to set
	 */
	public void setParentId(String parentId) {
		this.parentId = parentId;
	}
	/**
	 * @return the extAttrs
	 */
	public Map<String, String> getExtAttrs() {
		return extAttrs;
	}
	/**
	 * @param extAttrs the extAttrs to set
	 */
	public void setExtAttrs(Map<String, String> extAttrs) {
		this.extAttrs = extAttrs;
	}
	
	/**
	 * 设置扩展属性
	 * @param key
	 * @param value
	 */
	public void putExtAttr(String key, String value) {
		this.extAttrs.put(key, value);
	}

	/**
	 * 使用ID和NAME，重写Object#toString()
	 */
	@Override
	public String toString() {
		return "ZtreeNode{" +
				"id='" + id + '\'' +
				", name='" + name + '\'' +
				", parentId='" + parentId + '\'' +
				", nocheck=" + nocheck +
				", isParent=" + isParent +
				", extAttrs=" + extAttrs +
				'}';
	}

}
