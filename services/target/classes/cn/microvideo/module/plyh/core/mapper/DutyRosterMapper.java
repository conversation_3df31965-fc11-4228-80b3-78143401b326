package cn.microvideo.module.plyh.core.mapper;

import cn.microvideo.module.plyh.core.entity.DutyRoster;
import cn.microvideo.module.plyh.core.vo.DutyRosterVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 值班标题主表 Mapper 接口

 * @since 2025-06-11
 */
@Mapper
public interface DutyRosterMapper extends BaseMapper<DutyRoster> {

    /**
     * 按月份分页查询值班表及排班时间
     *
     * @param page   分页参数
     * @param month  查询月份 (格式: yyyy-MM)
     * @param orgId  组织ID
     * @return 分页结果
     */
    IPage<DutyRosterVO> queryPageListByMonth(Page<DutyRosterVO> page,
                                           @Param("month") String month,
                                           @Param("orgId") String orgId);

}
