package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.commonconstant.Contant;
import cn.microvideo.module.plyh.core.entity.DeptUser;
import cn.microvideo.module.plyh.core.entity.DutyDept;
import cn.microvideo.module.plyh.core.entity.Holiday;
import cn.microvideo.module.plyh.core.entity.HolidayDeptUser;
import cn.microvideo.module.plyh.core.mapper.DutyDeptMapper;
import cn.microvideo.module.plyh.core.service.IDeptUserService;
import cn.microvideo.module.plyh.core.service.IDutyDeptService;
import cn.microvideo.module.plyh.core.service.IHolidayDeptUserService;
import cn.microvideo.module.plyh.core.service.IHolidayService;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.vo.DutyDeptUserVO;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.module.plyh.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 排班部门管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Service
public class DutyDeptServiceImpl extends ServiceImpl<DutyDeptMapper, DutyDept> implements IDutyDeptService {
    @Resource
    private UserInfoUtil userInfoUtil;

    @Resource
    private IDeptUserService deptUserService;

    @Resource
    private IHolidayDeptUserService holidayDeptUserService;

    @Resource
    private IHolidayService holidayService;


    // 定义每次新增或重排时使用的间隙大小
    public static final long ORDER_GAP = 1000L;
    // 定义触发局部重排时，向前和向后查找的范围
    private static final int RE_BALANCE_RANGE = 50;

    /**
     * 数据新增
     *
     * @param dutyDept
     */
    @Override
    public void saveByEntity(DutyDept dutyDept) {
        try {
            //获取当前登录用户信息
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if (null == userVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前登录人信息！");
            }
            if (CharSequenceUtil.isBlank(dutyDept.getDeptName())) {
                throw new BizException(CommonConstant.ERROR_500, "部门名称不能为空！");
            }
            //单位ID
            dutyDept.setOrgId(userVO.getGroupId());
            //单位名称
            dutyDept.setOrgName(userVO.getGroupName());
            //创建人
            dutyDept.setCreateBy(userVO.getId());
            //创建时间
            dutyDept.setCreateTime(new Date());
            //更新时间
            dutyDept.setUpdateTime(new Date());
            //去空
            dutyDept.setDeptName(dutyDept.getDeptName().trim());
            //获取正常使用部门已经存在
            DutyDept dutyDept1 = isSameDeptName(userVO.getGroupId(), dutyDept.getDeptName().trim(), null, Contant.DEL_FLAG_0);
            //判定数据是否已经存在
            if (null != dutyDept1) {
                throw new BizException(CommonConstant.ERROR_500, "已存在相同部门！");
            }
            //获取删除的部门
            DutyDept dutyDeptDel = isSameDeptName(userVO.getGroupId(), dutyDept.getDeptName().trim(), null, Contant.DEL_FLAG_1);
            if (null != dutyDeptDel) {
                dutyDept.setId(dutyDeptDel.getId());
                //更新数据
                dutyDept.setDelFlag(Contant.DEL_FLAG_0);
                this.updateById(dutyDept);
            } else {
                //UUID
                dutyDept.setId(IdUtil.simpleUUID());
                //保存数据
                this.save(dutyDept);
            }

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 判定部门是否已经存在
     *
     * @param groupId
     * @param deptName
     * @return
     */

    public DutyDept isSameDeptName(String groupId, String deptName, String id, Integer delFlag) {
        LambdaQueryWrapper<DutyDept> query = new LambdaQueryWrapper<>();
        //部门名称
        query.eq(DutyDept::getDeptName, deptName);
        //单位ID
        query.eq(DutyDept::getOrgId, groupId);
        //删除标记
        query.eq(DutyDept::getDelFlag, delFlag);
        //是否id为空
        if (CharSequenceUtil.isNotBlank(id)) {
            query.notIn(DutyDept::getId, id);
        }
        List<DutyDept> list = this.list(query);
        if (CollUtil.isNotEmpty(list)) {
            return list.get(Contant.INT_INDEX_0);
        } else {
            return null;
        }

    }

    /**
     * 更新数据
     *
     * @param dutyDept
     * @return
     */
    @Override
    public boolean updateByEntity(DutyDept dutyDept) {
        try {
            //获取当前登录用户信息
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if (null == userVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前登录人信息！");
            }
            //
            DutyDept dutyDeptEntity = this.getById(dutyDept.getId());
            if (CharSequenceUtil.isBlank(dutyDept.getDeptName())) {
                throw new BizException(CommonConstant.ERROR_500, "部门名称不能为空！");
            }
            //获取正常使用部门已经存在
            DutyDept dutyDept1 = isSameDeptName(userVO.getGroupId(), dutyDept.getDeptName().trim(), dutyDept.getId(), Contant.DEL_FLAG_0);
            //判定数据是否已经存在
            if (null != dutyDept1) {
                throw new BizException(CommonConstant.ERROR_500, "已存在相同部门！");
            }
            //更新时间
            dutyDeptEntity.setUpdateTime(new Date());
            //更新人
            dutyDeptEntity.setUpdateBy(userVO.getId());
            //获取删除的部门
            DutyDept dutyDeptDel = isSameDeptName(userVO.getGroupId(), dutyDept.getDeptName().trim(), dutyDept.getId(), Contant.DEL_FLAG_1);
            if (null != dutyDeptDel) {
                //更新数据
                dutyDeptEntity.setDelFlag(Contant.DEL_FLAG_1);
                //更新数据
                dutyDeptDel.setDelFlag(Contant.DEL_FLAG_0);
                //更新数据
                this.updateById(dutyDeptDel);

            } else {
                dutyDeptEntity.setDeptName(dutyDept.getDeptName());
            }
            return this.updateById(dutyDeptEntity);

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 根据id获取数据和关连人员数据详情
     *
     * @param id
     * @return
     */
    @Override
    public DutyDeptUserVO getByFormId(String id) {
        try {
            //获取数据
            DutyDept dutyDept = this.getById(id);
            if (null == dutyDept) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前登录人信息！");
            }
            //复制数据
            DutyDeptUserVO dutyDeptUserVO = BeanUtil.copyProperties(dutyDept, DutyDeptUserVO.class);
            //获取部门下的人员列表
            dutyDeptUserVO.setUserIds(deptUserService.selectByFormId(id));
            return dutyDeptUserVO;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 获取分页列表
     *
     * @param page
     * @param dutyDept
     * @return
     */
    @Override
    public IPage<DutyDeptUserVO> pageList(Page<DutyDeptUserVO> page, DutyDept dutyDept) {
        try {
            //获取当前登录用户信息
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if (null == userVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前登录人信息！");
            }
            //获取当前单位信息
            dutyDept.setOrgId(userVO.getGroupId());
            //查询列表
            IPage<DutyDeptUserVO> pageList = baseMapper.pageList(page, dutyDept);
            //格式化数据
            if (CollUtil.isNotEmpty(pageList.getRecords())) {
                formatField(pageList.getRecords());
            }
            return pageList;

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 伪删除（涉及到历史数据查询）
     *
     * @param id
     */
    @Override
    public void removeByFormId(String id) {
        try {
            //判定当前时间往后村部存在排班信息（存在则不能删除）
            String errLog = selectByDateTime(id);
            if (CharSequenceUtil.isNotBlank(errLog)) {
                throw new BizException(CommonConstant.TEXT_ERROR_CODE, errLog);
            }
            //获取数据判定是否彻底删除
            if (isDelete(id)) {
                //彻底删除
                deptUserService.deleteByFormId(id);
                this.removeById(id);
            } else {
                //伪删除
                DutyDept dutyDept = this.getById(id);
                //删除标记
                dutyDept.setDelFlag(Contant.DEL_FLAG_1);
                //更新数据
                this.updateById(dutyDept);
            }
        } catch (Exception e) {
            throw new BizException(CommonConstant.TEXT_ERROR_CODE, e.getMessage());
        }
    }

    /**
     * @param id
     * @return
     */
    public boolean isDelete(String id) {
        List<HolidayDeptUser> list = holidayDeptUserService.selectByDeptId(id);
        if (CollUtil.isEmpty(list)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取当前日期往后还存不存在排班
     *
     * @return
     */
    public String selectByDateTime(String deptId) {
        try {
            //判定数据
            StringBuffer errLog = new StringBuffer();
            //获取当前往后的日期数据
            List<Holiday> holidayList = holidayService.selectByDateTime(DateUtil.format(new Date(), Contant.DATA_DAY), null);
            //封装map关系
            Map<String, List<HolidayDeptUser>> holidayMap = new HashMap<>();
            if (CollUtil.isNotEmpty(holidayList)) {
                List<String> holidayIds = holidayList.stream().map(Holiday::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(holidayIds)) {
                    //获取部门下的数据
                    List<HolidayDeptUser> holidayDeptUserList = holidayDeptUserService.selectByHolidayDeptId(holidayIds, deptId);
                    if (CollUtil.isNotEmpty(holidayDeptUserList)) {
                        holidayMap = holidayDeptUserList.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getHolidayId())).collect(Collectors.groupingBy(HolidayDeptUser::getHolidayId));
                    }
                }
                //查询当前日期下是否已经存在排班
                for (Holiday item : holidayList) {
                    if (CollUtil.isNotEmpty(holidayMap.get(item.getId()))) {
                        errLog.append("请删除" + DateUtil.format(item.getHolidayData(), Contant.DATA_DAY) + "的排班数据！<br>");
                    }
                }
            }
            //返回数据
            if (CharSequenceUtil.isNotBlank(errLog)) {
                return errLog.toString();
            } else {
                return null;
            }
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 数据全量查询
     *
     * @param dutyDept
     * @return
     */
    @Override
    public List<DutyDeptUserVO> queryList(DutyDept dutyDept) {
        try {
            //获取当前登录用户信息
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if (null == userVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前登录人信息！");
            }
            //获取当前单位信息
            dutyDept.setOrgId(userVO.getGroupId());
            List<DutyDeptUserVO> list = baseMapper.queryList(dutyDept);
            //格式化数据
            formatField(list);

            return list;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param dutyDeptUserVO
     */
    @Override
    public void addWithUsers(DutyDeptUserVO dutyDeptUserVO) {
        try {
            //获取当前登录用户信息
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if (null == userVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前登录人信息！");
            }
            //UUID
            dutyDeptUserVO.setId(IdUtil.simpleUUID());
            //单位ID
            dutyDeptUserVO.setOrgId(userVO.getGroupId());
            //单位名称
            dutyDeptUserVO.setOrgName(userVO.getGroupName());
            //创建人
            dutyDeptUserVO.setCreateBy(userVO.getId());
            //创建时间
            dutyDeptUserVO.setCreateTime(new Date());
            //更新时间
            dutyDeptUserVO.setUpdateTime(new Date());
            //保存人员数据
            if (CollUtil.isNotEmpty(dutyDeptUserVO.getUserIds())) {
                deptUserService.saveByFormIdUsers(dutyDeptUserVO.getId(), dutyDeptUserVO.getUserIds());
            }
            this.save(dutyDeptUserVO);

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 更新数据
     *
     * @param dutyDeptUserVO
     */
    @Override
    public void updateWithUsers(DutyDeptUserVO dutyDeptUserVO) {
        try {
            //获取当前登录用户信息
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if (null == userVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前登录人信息！");
            }
            //先删除关联的人员
            deptUserService.deleteByFormId(dutyDeptUserVO.getId());
            //创建人
            dutyDeptUserVO.setUpdateBy(userVO.getId());
            //更新时间
            dutyDeptUserVO.setUpdateTime(new Date());
            //保存人员数据
            if (CollUtil.isNotEmpty(dutyDeptUserVO.getUserIds())) {
                deptUserService.saveByFormIdUsers(dutyDeptUserVO.getId(), dutyDeptUserVO.getUserIds());
            }
            this.updateById(dutyDeptUserVO);


        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    @Override
    public List<DutyDept> selectByOrgIdFormIds(String orgId, List<String> holidayIds) {
        return null;
    }

    /**
     * 根据单位Id查询部门数据
     *
     * @param orgId
     * @return
     */
    @Override
    public List<DutyDept> selectByOrgId(String orgId, Integer delType) {
        LambdaQueryWrapper<DutyDept> query = new LambdaQueryWrapper<DutyDept>();
        query.orderByAsc(DutyDept::getCreateTime);
        query.eq(DutyDept::getOrgId, orgId);
        //删除标记
        if (null != delType) {
            query.eq(DutyDept::getDelFlag, delType);
        }
        return this.list(query);
    }

    @Override
    public List<DutyDept> selectByHolidayId(String orgId, List<String> holidayIds) {
        return baseMapper.selectByHolidayId(orgId, holidayIds);
    }

    /**
     * 新增排班列表
     *
     * @param orgId
     * @param holidayIds
     * @return
     */
    @Override
    public List<DutyDept> selectByHolidayIdWithUserDept(String orgId, List<String> holidayIds) {
        return baseMapper.selectByHolidayIdWithUserDept(orgId, holidayIds);
    }

    @Override
    public void moveItem(List<String> orderedIds) {
        if (CollectionUtils.isEmpty(orderedIds)) {
            return;
        }
        for (int i = 0; i < orderedIds.size(); i++) {
            String itemId = orderedIds.get(i);
            long newOrder = (long)(i + 1) * ORDER_GAP;
            baseMapper.update(new DutyDept(), new UpdateWrapper<DutyDept>().lambda()
                            .eq(DutyDept::getId, itemId)
                    .set(DutyDept::getSort, newOrder));
        }
    }


    public int latestOrder() {
        MicrovideoUserVO user = userInfoUtil.getHttpSessionUser();
        int maxOrder = baseMapper.findTopByOrderBySortOrderDesc(user.getGroupId());
        if (maxOrder == 0) {
            return (int) ORDER_GAP;
        }
        return (int) (maxOrder + ORDER_GAP);
    }



    /**
     * 格式化部门下面的人员
     *
     * @param records
     */
    private void formatField(List<DutyDeptUserVO> records) {
        try {
            //获取当前任务的人员所有数据
            List<String> deptIds = records.stream().map(DutyDeptUserVO::getId).collect(Collectors.toList());
            //查询数据
            if (CollUtil.isNotEmpty(deptIds)) {
                List<DeptUser> userList = deptUserService.selectByFormIds(deptIds);
                //处理数据
                if (CollUtil.isNotEmpty(userList)) {
                    Map<String, List<DeptUser>> userMap = userList.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getDeptId())).collect(Collectors.groupingBy(DeptUser::getDeptId));
                    //处理单位下面的人员数据
                    for (DutyDeptUserVO item : records) {
                        item.setUserIds(userMap.get(item.getId()));
                    }
                }
            }
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }
}
