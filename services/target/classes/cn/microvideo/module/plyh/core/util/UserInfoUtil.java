package cn.microvideo.module.plyh.core.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.framework.support.qs.util.MicrovideoQsUtil;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 获取用户信息
 */

@Component
public class UserInfoUtil {

    @Resource
    private RedisUtil redisUtil;

    /**
     * 获取当前登陆人信息
     *
     * @param request
     * @return
     */
    public MicrovideoUserVO getUserInfo(HttpServletRequest request) {
        String token = request.getHeader("token");
        Object s = redisUtil.get(token);
        if (ObjectUtil.isEmpty(s)) {
            return null;
        }
        MicrovideoUserVO user = JSONUtil.toBean(s.toString(), MicrovideoUserVO.class);
        return user;
    }

    /**
     * 获取当前HttpServletRequest对象
     *
     * @return
     */
    public HttpServletRequest getHttpServletRequest() {
        ServletRequestAttributes sra = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());
        return sra == null ? null : sra.getRequest();
    }

    /**
     * 获取当前会话用户
     *
     * @return
     */
    public MicrovideoUserVO getHttpSessionUser() {
        HttpServletRequest request = getHttpServletRequest();
        return request == null ? null : getUserInfo(request);
    }
}
