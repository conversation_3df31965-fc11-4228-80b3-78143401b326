package cn.microvideo.module.plyh.core.service;

import cn.microvideo.module.plyh.core.entity.Holiday;
import cn.microvideo.module.plyh.core.entity.ReleaseDay;
import cn.microvideo.module.plyh.core.vo.ReleaseHolidayVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 发布记录日期关联表
 * @Author: spring-boot
 * @Date:   2024-02-23
 * @Version: V1.0
 */
public interface IReleaseDayService extends IService<ReleaseDay> {

    /**
     * 保存发布和日期关联表
     * @param id
     * @param holidayIds
     */
    public void addByHoliday(String id,String holidayIds);


    /**
     *获取关系列表
     */
    public List<ReleaseHolidayVO> selectByReleaseIdsHoliday(List<String> ids);


    /**
     * 根据formId删除数据
     * @param formId
     */
    public void removeByFormId(String formId);

    /**
     *获取关系列表
     */
    public List<Holiday> selectByReleaseId(String id);

    /**
     * 获取当前节假日下面所有发布的关系
     * @param holidayIds
     * @return
     */
    public List<ReleaseDay> selectByHolidayIds(List<String> holidayIds);



}
