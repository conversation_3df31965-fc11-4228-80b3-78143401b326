package cn.microvideo.module.plyh.core.vo;

import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import lombok.Data;

import java.io.Serializable;

@Data
public class MicrovideoUserVO extends MicrovideoSessionUser implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 是否切换用户
     */
    private boolean exchange;
    /**
     * 是否切换用户
     */
    private boolean choiceOrg;
}
