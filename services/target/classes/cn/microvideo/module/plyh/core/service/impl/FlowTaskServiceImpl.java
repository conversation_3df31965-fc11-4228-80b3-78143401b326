package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.module.plyh.commonconstant.Enum.ActivityEnum;
import cn.microvideo.module.plyh.core.entity.FlowTask;
import cn.microvideo.module.plyh.core.mapper.FlowTaskMapper;
import cn.microvideo.module.plyh.core.service.IFlowTaskService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Description: 用印流程数据关系表
 * @Author: spring-boot
 * @Date: 2023-09-05
 * @Version: V1.0
 */
@Service
public class FlowTaskServiceImpl extends ServiceImpl<FlowTaskMapper, FlowTask> implements IFlowTaskService {
    /**
     * 处理流程完成状态
     *
     * @param uuid
     */
    @Override
    public void dealNode(String uuid, String type, MicrovideoSessionUser user) {
        FlowTask signetTask = this.getById(uuid);
        if (null != signetTask) {
            signetTask.setNodeCode(type);
            signetTask.setHandleTime(new Date());
            signetTask.setHandleUserId(user.getId());
            signetTask.setHandleUserName(user.getName());
            this.updateById(signetTask);
        }

    }

    /**
     * 处理退回节点的数据
     *
     * @param uuid
     * @param type
     * @param user
     */
    @Override
    public void dealFreeJumpNode(String uuid, String type, MicrovideoSessionUser user) {
        FlowTask signetTask = this.getById(uuid);
        if (null != signetTask) {
            //查询同分组的数据
            List<FlowTask> list = this.selectByGroupId(signetTask.getGroupId());
            if (CollUtil.isNotEmpty(list)) {
                for (FlowTask vo : list) {
                    vo.setNodeCode(type);
                    vo.setHandleTime(new Date());
                    vo.setHandleUserId(user.getId());
                    vo.setHandleUserName(user.getName());
                }
                this.updateBatchById(list);
            }
        }
    }

    /**
     * 获取数据
     *
     * @param groupId
     * @return
     */
    public List<FlowTask> selectByGroupId(String groupId) {
        LambdaQueryWrapper<FlowTask> query = new LambdaQueryWrapper<>();
        query.eq(FlowTask::getGroupId, groupId);
        return this.list(query);

    }

    /**
     * @param userId
     * @param type
     * @return
     */
    @Override
    public List<FlowTask> signetWithTaskVO(String userId, String type) {
        LambdaQueryWrapper<FlowTask> query = new LambdaQueryWrapper<>();
        query.eq(FlowTask::getHandleUserId, userId);
        query.eq(FlowTask::getNodeCode, type);
        return this.list(query);
    }

    /**
     * 创建一个已完成的节点
     *
     * @param signetId
     * @param user
     */
    @Override
    public void createStartNode(String signetId, MicrovideoSessionUser user) {
        FlowTask signetTask = new FlowTask();
        //taskKey
        signetTask.setTaskKey(ActivityEnum.APPLY.getCode());
        //UUID
        signetTask.setSignetUuid(signetId);
        //申请名称
        signetTask.setNodeStatus(ActivityEnum.APPLY.getName());
        //待办状态
        signetTask.setNodeCode(ActivityEnum.COMPING.getCode());
        //创建时间
        signetTask.setCreateTime(new Date());
        //处理时间
        signetTask.setHandleTime(new Date());
        //更新时间
        signetTask.setUpdateTime(new Date());
        //待办人员ID
        signetTask.setHandleUserId(user.getId());
        //待办人员
        signetTask.setHandleUserName(user.getName());
        //保存数据
        this.save(signetTask);
    }

}
