package cn.microvideo.module.plyh.core.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 值班标题主表
 *

 * @since 2025-06-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_duty_rosters")
public class DutyRoster implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "F_VC_ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 值班表标题
     */
    @TableField("F_VC_TITLE")
    private String title;

    /**
     * 所属组织ID
     */
    @TableField("F_VC_ORG_ID")
    private String orgId;

    /**
     * 所属组织名称
     */
    @TableField("F_VC_ORG_NAME")
    private String orgName;

    /**
     * 创建人ID
     */
    @TableField("F_VC_CREATE_BY")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("F_DT_CREATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 最后更新人ID
     */
    @TableField("F_VC_UPDATE_BY")
    private String updateBy;

    /**
     * 最后更新时间
     */
    @TableField("F_DT_UPDATE_TIME")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 软删除标记 (0=正常, 1=已删除)
     */
    @TableLogic
    @TableField("F_INT_DEL_FLAG")
    private Integer delFlag;

    /**
     * 排序号
     */
    @TableField("F_INT_SORT")
    private Integer sort;


    /**
     * 发布状态 (0=未发布, 1=已发布)
     */
    @TableField("F_INT_PUBLISH_STATUS")
    private Integer publishStatus;


}
