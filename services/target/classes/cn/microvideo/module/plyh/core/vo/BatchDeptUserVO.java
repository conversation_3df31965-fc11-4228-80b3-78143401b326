package cn.microvideo.module.plyh.core.vo;

import cn.microvideo.module.plyh.core.entity.DeptUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "批量新增人员实体", description = "批量新增人员实体")
public class BatchDeptUserVO {
    //部门ID
    @ApiModelProperty(value = "部门ID")
    private String deptId;
    //部门下的人员
    @ApiModelProperty(value = "部门下的人员")
    private List<DeptUser> deptUsers;
}
