package cn.microvideo.module.plyh.core.vo;

import cn.microvideo.module.plyh.core.entity.Holiday;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "HolidayDetailVO对象", description = "获取节假日详情列表")
public class HolidayDetailVO extends Holiday {
    @ApiModelProperty(value = "节假日详情列表")
    private List<HolidayDayVO> holidayDayVOList;
}
