package cn.microvideo.module.plyh.core.util;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 
 * <b>描述:</b>
 *
 * <p>
 * 全局公用线程池
 * </p>
 *
 * @version 1.0
 * <AUTHOR>
 * @Date 2019年8月22日上午11:15:34
 * @since JDK1.8
 */
public final class ThreadPool {
	/**
	 * 线程池
	 */
	private static final ExecutorService CACHED_THREAD_POOL = new ThreadPoolExecutor(8, 8, 0, TimeUnit.SECONDS,
			new LinkedBlockingQueue<>(1024),
			new BasicThreadFactory.Builder().namingPattern("jchc-common-pool-%d").daemon(true).build(),
			new ThreadPoolExecutor.AbortPolicy());

	/**
	 * 私有构造
	 */
	private ThreadPool() {
	}

	/**
	 * 执行线程任务
	 * 
	 * @param command
	 */
	public static void execute(Runnable command) {
		CACHED_THREAD_POOL.execute(command);
	}


}
