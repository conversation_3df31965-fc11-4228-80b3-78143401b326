package cn.microvideo.module.plyh.core.mapper;

import cn.microvideo.module.plyh.core.entity.Holiday;
import cn.microvideo.module.plyh.core.entity.ReleaseDay;
import cn.microvideo.module.plyh.core.vo.ReleaseHolidayVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 发布记录日期关联表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface ReleaseDayMapper extends BaseMapper<ReleaseDay> {

    /**
     * 获取数据列表
     * @param ids
     * @return
     */
    public List<ReleaseHolidayVO> selectByReleaseIdsHoliday(@Param("ids") List<String> ids);


    /**
     * 根据ID查询天
     * @param id
     * @return
     */
    public List<Holiday> selectByReleaseId(@Param("id") String id);


}
