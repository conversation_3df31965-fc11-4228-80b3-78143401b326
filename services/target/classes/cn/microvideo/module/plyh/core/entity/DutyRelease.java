package cn.microvideo.module.plyh.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 值班发布记录表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Data
@TableName("b_plyh_duty_release")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "b_plyh_duty_release对象", description = "值班发布记录表")
public class DutyRelease {

    /**
     * UUID
     */

    @ApiModelProperty(value = "UUID")
    @TableId("F_VC_ID")
    private String id;
    /**
     * 发布标题
     */
    @ApiModelProperty(value = "发布标题")
    @TableField("F_VC_TITLE")
    private String title;
    /**
     * 单位ID
     */

    @ApiModelProperty(value = "单位ID")
    @TableField("F_VC_ORG_ID")
    private String orgId;
    /**
     * 单位名称
     */

    @ApiModelProperty(value = "单位名称")
    @TableField("F_VC_ORG_NAME")
    private String orgName;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    @TableField("F_VC_CREATE_BY")
    private String createBy;
    /**
     * 创建时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @TableField("F_DT_CREATE_TIME")
    private Date createTime;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    @TableField("F_VC_UPDATE_BY")
    private String updateBy;
    /**
     * 更新时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    @TableField("F_VC_UPDATE_TIME")
    private Date updateTime;

    /**
     * 发布人ID
     */

    @ApiModelProperty(value = "发布人ID")
    @TableField("F_VC_USER_ID")
    private String userId;

    /**
     * 发布人ID
     */

    @ApiModelProperty(value = "发布人")
    @TableField("F_VC_USER_NAME")
    private String userName;

    /**
     * 访问量
     */
    @ApiModelProperty(value = "访问量")
    @TableField("F_INT_VISITS_NUMBER")
    private Integer visitsNumber;

    /**
     * 单位Id
     */

    @ApiModelProperty(value = "单位Id")
    @TableField("F_VC_DEPT_ID")
    private String deptId;

    /**
     * 发布人部门名称
     */

    @ApiModelProperty(value = "发布人部门名称")
    @TableField("F_VC_DEPT_NAME")
    private String deptName;

}
