package cn.microvideo.module.plyh.core.util;

import cn.microvideo.module.plyh.core.vo.NoticePublicity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <b>描述:</b>
 *
 * <p>门户任务工具类</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2019年7月13日下午2:10:16
 * @since JDK1.8
 */
@Slf4j
@Component
public class FaceTaskUtils {

    @Resource
    private FaceProperties faceProperties;

    @Resource
    private FileHttpUtils fileHttpUtils;

    /**
     * 值班排班批量数据发布
     *
     * @param params 参数
     */
    public void addNoticeBatch(List<NoticePublicity> params) {
        try {
            String url = String.format("%s/addBatch", faceProperties.getUrl());
            fileHttpUtils.postSendBodyAPPList(url, params);
        } catch (Exception e) {
            log.error("插入门户任务出错:{}", e.getMessage());
        }
    }


    /**
     * 撤销任务
     *
     * @param noticeId 任务ID
     */
    public void cancelNoticeBatch(String noticeId) {
        try {
            String url = String.format("%s/cancelNoticeBatch", faceProperties.getUrl());
            Map<String, Object> params = new HashMap<>();
            params.put("noticeId", noticeId);
            fileHttpUtils.getSendParamBody(url, params);
        } catch (Exception e) {
            log.error("撤销门户任务出错:{}", e.getMessage());
        }
    }

}
