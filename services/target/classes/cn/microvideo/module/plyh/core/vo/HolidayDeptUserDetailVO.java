package cn.microvideo.module.plyh.core.vo;

import cn.microvideo.module.plyh.core.entity.DeptUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ActivityProIdUserVO对象", description = "获取节点和人员数据")
public class HolidayDeptUserDetailVO extends DeptUser {
    @ApiModelProperty(value = "节假日ID")
    private String holidayId;
    @ApiModelProperty(value = "部门ID")
    private String dutyDeptId;
}
