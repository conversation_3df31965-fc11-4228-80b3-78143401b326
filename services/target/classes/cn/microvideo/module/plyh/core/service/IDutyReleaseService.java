package cn.microvideo.module.plyh.core.service;

import cn.microvideo.module.plyh.core.entity.DutyRelease;
import cn.microvideo.module.plyh.core.vo.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 值班发布记录表
 * @Author: spring-boot
 * @Date:   2024-02-23
 * @Version: V1.0
 */
public interface IDutyReleaseService extends IService<DutyRelease> {

    /**
     * 发布数据
     * @param dutyRelease
     */
    public void addRelease(AddDutyReleaseVO dutyRelease);


    /**
     * 数据分页
     * @param page
     * @param dutyRelease
     * @return
     */
    public IPage<DutyReleaseUserVO> pageList(Page<DutyReleaseUserVO> page, DutyReleaseSearchVO dutyRelease);

    /**
     * 发布删除
     * @param id
     */
    public void removeByCancelId(String id);


    /**
     * 根据id查找具体发布的数据
     * @param id
     * @return
     */
    public ReleaseHolidayDetailVO getByReleaseId(String id);

}
