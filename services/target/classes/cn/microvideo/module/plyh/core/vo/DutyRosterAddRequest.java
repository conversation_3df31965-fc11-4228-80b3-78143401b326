package cn.microvideo.module.plyh.core.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 值班表添加请求VO
 *
 * <AUTHOR> Agent
 * @since 2025-01-16
 */
@Data
@ApiModel(value = "DutyRosterAddRequest对象", description = "值班表添加请求VO")
public class DutyRosterAddRequest {

    /**
     * 值班表标题
     */
    @ApiModelProperty(value = "值班表标题", required = true)
    @NotBlank(message = "值班表标题不能为空")
    private String title;

    /**
     * 所属组织ID
     */
    @ApiModelProperty(value = "所属组织ID")
    private String orgId;

    /**
     * 所属组织名称
     */
    @ApiModelProperty(value = "所属组织名称")
    private String orgName;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sort;

    /**
     * 排班时间列表
     */
    @ApiModelProperty(value = "排班时间列表", required = true)
    @NotEmpty(message = "排班时间列表不能为空")
    private List<LocalDate> scheduleTimes;
}
