package cn.microvideo.module.plyh.core.service;

import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.module.plyh.core.entity.FlowTask;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 用印流程数据关系表
 * @Author: spring-boot
 * @Date: 2023-09-05
 * @Version: V1.0
 */
public interface IFlowTaskService extends IService<FlowTask> {
    /**
     * 完成节点状态
     *
     * @param uuid
     */
    public void dealNode(String uuid, String type, MicrovideoSessionUser user);

    /**
     * 处理退回节点的数据
     *
     * @param uuid
     * @param type
     * @param user
     */
    public void dealFreeJumpNode(String uuid, String type, MicrovideoSessionUser user);


    /**
     * 通过userId获取人员数据
     *
     * @param userId
     * @return
     */
    public List<FlowTask> signetWithTaskVO(String userId, String type);

    /**
     * 创建第一个节点状态为已完成
     *
     * @param signetId
     * @param user
     */
    public void createStartNode(String signetId, MicrovideoSessionUser user);

}
