package cn.microvideo.module.plyh.core.vo;

import lombok.Data;

import java.util.Map;

@Data
public class NodeUserVO {
    //ID
    private String id;
    //节点ID
    private String nodeId;
    //
    private String specId;
    //节点名称
    private String nodeName;
    //节点状态
    private String pluginType;
    //用户数据
    private String pluginVal;
    //用户数据
    private Map<String,String> specMap;
    //是否是第一个节点
    private boolean first;
}
