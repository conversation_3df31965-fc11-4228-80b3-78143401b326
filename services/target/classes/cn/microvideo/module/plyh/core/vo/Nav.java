package cn.microvideo.module.plyh.core.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <b>描述:</b>
 *
 * <p>导航菜单</p>
 *
 * @version 1.0
 * <AUTHOR>
 * @Date 2020年7月1日下午1:48:11
 * @since JDK1.8
 */
@Getter
@Setter
@ToString
public class Nav {

	/**
	 * 标识
	 */
	private String id;
	/**
	 * 编码
	 */
	private String code;
	/**
	 * 图标
	 */
	private String icon;
	/**
	 * 第二个图标
	 */
	private String icon2;
	/**
	 * 名称
	 */
	private String name;
	/**
	 * url
	 */
	private String url;
	/**
	 * 序号
	 */
	private int sort;
	/**
	 * 待办数量
	 */
	private int count;
	/**
	 * 模块类别
	 */
	private String classify;
	/**
	 * 子级菜单
	 */
	private List<Nav> children = Collections.emptyList();
	/**
	 * 是否有权限
	 */
	private boolean has;

	/**
	 * 添加子菜单
	 * @param child 子菜单
	 */
	public boolean addChild(Nav child) {
		if(children.isEmpty()) {
			children = new ArrayList<>();
		}
		return children.add(child);
	}
}
