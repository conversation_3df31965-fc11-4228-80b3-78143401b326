package cn.microvideo.module.plyh.core.service;

import cn.microvideo.module.plyh.core.entity.DeptUser;
import cn.microvideo.module.plyh.core.vo.BatchDeptUserVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: 值班部门人员管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface IDeptUserService extends IService<DeptUser> {

    /**
     * @return
     */
    public List<DeptUser> selectByFormId(String id);

    /**
     * 根据formId批量获取数据
     *
     * @param ids
     * @return
     */
    public List<DeptUser> selectByFormIds(List<String> ids);

    /**
     * 批量新增数据
     *
     * @param batchDeptUserVO
     */
    public void addBatchUsers(BatchDeptUserVO batchDeptUserVO);


    /**
     * 根据formId删除数据
     *
     * @param id
     */
    public void deleteByFormId(String id);

    /**
     * 保存数据
     * @param id
     * @param deptUsers
     */
    public void saveByFormIdUsers(String id, List<DeptUser> deptUsers);

}
