package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.commonconstant.Contant;
import cn.microvideo.module.plyh.config.DockingProperties;
import cn.microvideo.module.plyh.core.entity.*;
import cn.microvideo.module.plyh.core.mapper.HolidayMapper;
import cn.microvideo.module.plyh.core.service.*;
import cn.microvideo.module.plyh.core.util.FileHttpUtils;
import cn.microvideo.module.plyh.core.util.PoiUtil;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.util.YcgzUtil;
import cn.microvideo.module.plyh.core.vo.*;
import cn.microvideo.module.plyh.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 节假日、休息、值班管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Slf4j
@Service
public class HolidayServiceImpl extends ServiceImpl<HolidayMapper, Holiday> implements IHolidayService {
    @Resource
    private UserInfoUtil userInfoUtil;

    @Resource
    private IDutyDeptService dutyDeptService;

    @Resource
    private IHolidayDeptUserService holidayDeptUserService;

    @Resource
    private IDeptUserService deptUserService;

    @Resource
    private IReleaseDayService releaseDayService;

    @Resource
    private IDutyReleaseService dutyReleaseService;

    @Resource
    private FileHttpUtils httpUtils;

    @Resource
    private DockingProperties dockingProperties;

    /**
     * 新增节假日
     *
     * @param holiday
     */
    @Override
    public void saveByEntity(Holiday holiday) {
        /**
         * 新增节假日
         */
        holiday.setId(IdUtil.simpleUUID());
        //保存数据
        this.save(holiday);
    }

    /**
     * 数据批量新增
     *
     * @param addBatchHolidayVO
     */
    @Override
    public void saveByBatchEntity(AddBatchHolidayVO addBatchHolidayVO) {
        try {
            if (CharSequenceUtil.isBlank(addBatchHolidayVO.getYear())) {
                throw new BizException(CommonConstant.ERROR_500, "未找到数据年份！");
            }
            //新增列表
            List<Holiday> addList = new ArrayList<>();
            //更新列表
            List<Holiday> updateList = new ArrayList<>();
            //删除数据
            List<String> deleteList = new ArrayList<>();
            //删除值班的排班的人员
            List<String> holidayIds = new ArrayList<>();
            //获取当前年的数据
            List<Holiday> allYearList = this.queryByYear(addBatchHolidayVO.getYear());
            //获取Map
            Map<String, Holiday> holidayMap = new HashMap<>();
            //用于错误信息的返回
            Map<String, Holiday> detailMap = new HashMap<>();
            //获取当前年的数据
            if (CollUtil.isNotEmpty(allYearList)) {
                for (Holiday item : allYearList) {
                    //返回数据
                    holidayMap.put(DateUtil.format(item.getHolidayData(), "yyyy-MM-dd"), item);
                    //
                    detailMap.put(item.getId(), item);
                }
            }
            //创建Map
            Map<String, Date> yearMap = new HashMap<>();
            //刷新数据(新增或者修改数据)
            for (Holiday item : addBatchHolidayVO.getHolidayList()) {
                //存储已经新增的数据
                yearMap.put(DateUtil.format(item.getHolidayData(), "yyyy-MM-dd"), item.getHolidayData());
                //获取日期具体名称
                item.setHolidayName(getHolidayName(item.getHolidayData(), item.getHolidayType()));
                //获取老数据
                Holiday source = holidayMap.get(DateUtil.format(item.getHolidayData(), "yyyy-MM-dd"));
                //获取需要更新的数据
                if (null != source) {
                    //返回ID
                    item.setId(source.getId());
                    if (item.getHolidayType() != source.getHolidayType()) {
                        holidayIds.add(item.getId());
                    }
                    updateList.add(item);
                } else {
                    //UUID
                    item.setId(IdUtil.simpleUUID());
                    addList.add(item);
                }
            }
            //删除数据
            if (CollUtil.isNotEmpty(allYearList)) {
                for (Holiday item : allYearList) {
                    if (null == yearMap.get(DateUtil.format(item.getHolidayData(), "yyyy-MM-dd"))) {
                        //如果是周六日则更新数据,否则删除
                        if (DateUtil.isWeekend(item.getHolidayData())) {
                            if (Contant.DATE_TYPE_2 != item.getHolidayType()) {
                                //需要删除的数据
                                holidayIds.add(item.getId());
                            }
                            //获取节假日名称
                            item.setHolidayName(getHolidayName(item.getHolidayData(), Contant.DATE_TYPE_2));
                            //封装类型
                            item.setHolidayType(Contant.DATE_TYPE_2);
                            //返回更新列表
                            updateList.add(item);
                        } else {
                            //获取数据
                            holidayIds.add(item.getId());
                            //需要删除的数据
                            deleteList.add(item.getId());
                        }
                    }
                }
            }

            StringBuffer errLog = new StringBuffer();
            //验证删除列表是否已经发布通知公告
            if (CollUtil.isNotEmpty(deleteList)) {
                //获取发布关联日期
                List<ReleaseDay> dayList = releaseDayService.selectByHolidayIds(deleteList);
                //获取发布记录
                List<DutyRelease> dutyReleases = dutyReleaseService.list();
                Map<String, DutyRelease> dutyReleaseMap = new HashMap<>();
                if (CollUtil.isNotEmpty(dutyReleases)) {
                    for (DutyRelease item : dutyReleases) {
                        //封装数据关系
                        dutyReleaseMap.put(item.getId(), item);
                    }
                }
                //封装错误数据
                if (CollUtil.isNotEmpty(dayList)) {
                    for (ReleaseDay item : dayList) {
                        //获取具体的天
                        Holiday holiday = detailMap.get(item.getHolidayId());
                        if (null != holiday) {
                            DutyRelease dutyRelease = dutyReleaseMap.get(item.getReleaseId());
                            if (null != dutyRelease) {
                                errLog.append(DateUtil.format(holiday.getHolidayData(), Contant.DATA_DAY) + "已经在" + dutyRelease.getOrgName() + "【" + dutyRelease.getTitle() + "】中发布,请先撤销！<br>");
                            }
                        }
                    }
                    //存在则提示
                    if (CharSequenceUtil.isNotBlank(errLog)) {
                        throw new BizException(CommonConstant.TEXT_ERROR_CODE, errLog.toString());
                    }
                }
            }

            //新增数据
            if (CollUtil.isNotEmpty(addList)) {
                this.saveBatch(addList);
            }
            //更新数据
            if (CollUtil.isNotEmpty(updateList)) {
                this.updateBatchById(updateList);
            }
            //删除数据
            if (CollUtil.isNotEmpty(deleteList)) {
                this.removeByIds(deleteList);
            }
            //删除排班的数据
            if (CollUtil.isNotEmpty(holidayIds)) {
                holidayDeptUserService.deleteByHolidayIds(holidayIds);
            }
            //同步数据
            addWithUpdateBatchEntity(addBatchHolidayVO);

        } catch (Exception e) {
            throw new BizException(CommonConstant.TEXT_ERROR_CODE, e.getMessage());
        }

    }

    /**
     * 同步时局
     */
    public void addWithUpdateBatchEntity(AddBatchHolidayVO addBatchHolidayVO) {
        //同步数据到考勤系统
        try {
            //查询全部的值班排班数据
            addBatchHolidayVO.setHolidayList(queryByYear(addBatchHolidayVO.getYear()));
            //推送数据
            httpUtils.postSendBodyAttList(dockingProperties.getCheckService(), addBatchHolidayVO);
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }

    /**
     * 根据年份数据
     *
     * @return
     */
    public List<Holiday> queryByYear(String year) {
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y' )", year);
        return this.list(queryWrapper);
    }


    /**
     * 判定数据周几
     *
     * @param data
     * @return
     */
    public String getHolidayName(Date data, Integer type) {
        if (Contant.DATE_TYPE_1.equals(type)) {
            return Contant.HOLIDAY_NAME_1;
        }
        if (Contant.DATE_TYPE_3.equals(type)) {
            return Contant.HOLIDAY_NAME_3;
        }
        return DateUtil.dayOfWeekEnum(data).toChinese();
    }

    /**
     * 根据月份查询数据
     *
     * @param month
     * @return
     */
    @Override
    public HolidayMonthVO selectByMonth(String month, String type) {
        try {
            HolidayMonthVO holidayMonthVO = new HolidayMonthVO();
            //获取当前月的数据
            if (CharSequenceUtil.isBlank(month)) {
                month = DateUtil.format(new Date(), Contant.DATA_MONTH);
            }
            //获取节假日列表
            List<Holiday> holidayList = queryByMonth(month);
            if (CollUtil.isNotEmpty(holidayList)) {
                formatHolidayDetails(holidayList, holidayMonthVO, type, month);
            }
            return holidayMonthVO;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 根据月份查询数据
     *
     * @param
     * @return
     */
    @Override
    public HolidayMonthVO selectByHolidays(List<Holiday> holidayList, String orgId) {
        try {
            HolidayMonthVO holidayMonthVO = new HolidayMonthVO();
            if (CollUtil.isNotEmpty(holidayList)) {
                formatHolidayByHolidays(holidayList, holidayMonthVO, orgId);
            }
            return holidayMonthVO;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 根据具体时间查询相关部门和已选中人
     *
     * @param id
     * @return
     */
    @Override
    public HolidayTodoDetailVO queryByIdDetailUser(String id) {
        try {
            //获取当前用户信息
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if (null == userVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前用户信息！");
            }
            //查询具体的日期详情
            Holiday holiday = this.getById(id);
            if (null == holiday) {
                throw new BizException(CommonConstant.ERROR_500, "未找到数据实体！");
            }
            //复制数据
            HolidayTodoDetailVO holidayTodoDetailVO = BeanUtil.copyProperties(holiday, HolidayTodoDetailVO.class);
            //获取当前单位下的人员的部门
            List<DutyDept> deptList = dutyDeptService.selectByOrgId(userVO.getGroupId(), Contant.DEL_FLAG_0);
            //创建部门用户关系
            Map<String, List<DeptUser>> userMap = new HashMap<>();
            //获取每个部门已选择的人员
            Map<String, List<HolidayDeptUser>> holidayUserMap = new HashMap<>();
            if (CollUtil.isNotEmpty(deptList)) {
                List<String> deptIds = deptList.stream().map(DutyDept::getId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(deptIds)) {
                    //获取部门下的所有人
                    List<DeptUser> allUserList = deptUserService.selectByFormIds(deptIds);
                    if (CollUtil.isNotEmpty(allUserList)) {
                        userMap = allUserList.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getDeptId())).collect(Collectors.groupingBy(DeptUser::getDeptId));
                    }
                    //获取当前单位下已选中的人员
                    List<HolidayDeptUser> doneList = holidayDeptUserService.selectByHolidayIdDeptIds(id, deptIds);
                    //数据如果不为空
                    if (CollUtil.isNotEmpty(doneList)) {
                        holidayUserMap = doneList.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getDutyDeptId())).collect(Collectors.groupingBy(HolidayDeptUser::getDutyDeptId));
                    }
                }
            }
            //构建返回数据
            holidayTodoDetailVO.setHolidayDayVOList(createHolidayDetail(deptList, userMap, holidayUserMap));
            return holidayTodoDetailVO;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 数据导入
     *
     * @param inputStream
     */
    @Override
    public void inputDutyExcel(InputStream inputStream, String month) {
        try {
            //获取当前用户信息
            MicrovideoUserVO microvideoUserVO = userInfoUtil.getHttpSessionUser();
            //月份处理
            if (CharSequenceUtil.isBlank(month)) {
                throw new BizException(CommonConstant.ERROR_500, "请选择导入的月份！");
            }
            Date dateMonth = DateUtil.parse(month, "yyyy-MM");
            //获取当前月份日期
            Map<String, String> map = selectByMonth(month);
            //判空
            if (CollUtil.isEmpty(map)) {
                throw new BizException(CommonConstant.ERROR_500, "未找到本月可导入的节假日、周六、日数据！");
            }
            //解析Excel
            List<List<Object>> list = ExcelUtil.getReader(inputStream).read(Contant.INT_NUMBER_1);
            //处理第二行日期数据
            List<Object> dateList = list.get(Contant.INT_INDEX_0);
            //校验时间是否设置成节假日并存储关系
            if (CollUtil.isEmpty(dateList) || dateList.size() <= Contant.INT_NUMBER_1) {
                throw new BizException(CommonConstant.ERROR_500, "导入时间设置异常！");
            }
            //存储列和日期的关系
            Map<Integer, String> holidayMap = new HashMap<>();
            //时间的校验
            for (int i = 1; i < dateList.size(); i++) {
                log.info(dateList.get(i).toString());
                //是否可转换成正确时间
                Date date = DateUtil.parse(dateList.get(i).toString(), "yyyy-MM-dd");
                if (null == date) {
                    throw new BizException(CommonConstant.ERROR_500, "第" + i + "列时间设置异常！");
                }
                //判定日期
                if (DateUtil.compare(new Date(), date) >= 0) {
                    throw new BizException(CommonConstant.ERROR_500, "第" + i + "列不是排班时间已过！");
                }

                if (CharSequenceUtil.isBlank(map.get(DateUtil.format(date, "yyyy-MM-dd")))) {
                    throw new BizException(CommonConstant.ERROR_500, "第" + i + "列不是节假日、周六或周日！");
                }
                //存储列表的关系
                holidayMap.put(i, map.get(DateUtil.format(date, "yyyy-MM-dd")));
            }
            Map<String, String> deptMap = new HashMap<>();
            //获取当前部门的数据
            List<DutyDept> deptList = dutyDeptService.selectByOrgId(microvideoUserVO.getGroupId(), Contant.DEL_FLAG_0);
            //封装map
            for (DutyDept item : deptList) {
                deptMap.put(item.getDeptName(), item.getId());
            }
            //判定数据
            StringBuffer errLog = new StringBuffer();

            List<HolidayDeptUser> holidayDeptUser = new ArrayList<>();
            //循环处理部门数据
            for (int t = 1; t < list.size(); t++) {
                List<Object> items = list.get(t);
                if (CollUtil.isEmpty(items) || items.size() <= 1) {
                    continue;
                }
                if (ObjectUtil.isEmpty(items.get(Contant.INT_INDEX_0))) {
                    throw new BizException(CommonConstant.ERROR_500, "第" + t + "行部门为空！");
                }
                String deptId = deptMap.get(items.get(Contant.INT_INDEX_0).toString().trim());
                //获取第一个
                for (int y = 1; y < items.size(); y++) {
                    if (ObjectUtil.isNotEmpty(items.get(y))) {
                        //解析人员数据
                        holidayDeptUser.addAll(formatUserList(items.get(y).toString(), deptId, holidayMap.get(y), dateMonth, microvideoUserVO, errLog, t + 2, y + 1));
                    }
                }
            }
            //判定抛出异常
            if (CharSequenceUtil.isNotBlank(errLog)) {
                throw new BizException(CommonConstant.TEXT_ERROR_CODE, errLog.toString());
            }
            //保存数据
            if (CollUtil.isNotEmpty(holidayDeptUser)) {
                //保存数据
                holidayDeptUserService.saveBatch(holidayDeptUser);
            }
            log.info(dateList.toString());
        } catch (Exception e) {
            throw new BizException(CommonConstant.TEXT_ERROR_CODE, e.getMessage());
        }
    }

    /**
     * 导出值班排班列表
     *
     * @param month
     * @return
     */
    @Override
    public Workbook exportDutyExcel(String month, String orgName, String orgId) {
        try {
            //数据判空
            if (CharSequenceUtil.isBlank(month)) {
                throw new BizException(CommonConstant.ERROR_500, "未找到导出的月份！");
            }
            //获取数据
            HolidayMonthVO holidayMonthVO = this.selectByHolidays(queryByMonth(month), orgId);
            if (CollUtil.isEmpty(holidayMonthVO.getHolidayList())) {
                throw new BizException(CommonConstant.ERROR_500, "未找到可导出的数据！");
            }
            if (CollUtil.isEmpty(holidayMonthVO.getDeptList())) {
                throw new BizException(CommonConstant.ERROR_500, "未找到可导出的数据！");
            }
            //封装数据
            return writeToExcelByList(month, holidayMonthVO, orgName);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 刷周六、日数据
     */
    @Override
    public void refreshHolidayData() {
        try {
            //获取数据
            Map<String, String> map = new HashMap<>();
            //获取当前年往后的数据
            List<Holiday> list = selectByYearList(DateUtil.format(new Date(), "yyyy"));
            //封装数据
            if (CollUtil.isNotEmpty(list)) {
                for (Holiday item : list) {
                    map.put(DateUtil.format(item.getHolidayData(), "yyyy-MM-dd"), item.getId());
                }
            }
            //获取当前年最后一个月份
            String endMonth = DateUtil.format(new Date(), "yyyy").concat("-12");
            //当前年的最后一天
            Date lastDayTime = DateUtil.endOfMonth(DateUtil.parse(endMonth, "yyyy-MM"));
            //获取今天时间+1天
            Date nowDay = DateUtil.offsetDay(new Date(), 1);
            //获取需要刷新的数据
            List<Holiday> weekEndList = new ArrayList<>();
            //判断是否是最后一年的最后一天
            if (DateUtil.compare(nowDay, lastDayTime) <= 0) {
                //获取当前时间往后的所有时间
                List<DateTime> allList = DateUtil.rangeToList(nowDay, lastDayTime, DateField.DAY_OF_YEAR, 1);
                if (CollUtil.isNotEmpty(allList)) {
                    Holiday holiday = null;
                    for (DateTime dateItem : allList) {
                        log.info(DateUtil.format(dateItem, "yyyy-MM-dd"));
                        //判定是否为周末
                        if (DateUtil.isWeekend(dateItem)) {
                            holiday = new Holiday();
                            if (CharSequenceUtil.isNotBlank(map.get(DateUtil.format(dateItem, "yyyy-MM-dd")))) {
                                continue;
                            }
                            //UUID
                            holiday.setId(IdUtil.simpleUUID());
                            //日期
                            holiday.setHolidayData(dateItem);
                            //调班
                            holiday.setHolidayType(Contant.DATE_TYPE_2);
                            //日期名称(周六日)
                            holiday.setHolidayName(getHolidayName(dateItem, Contant.DATE_TYPE_2));
                            //回显数据
                            weekEndList.add(holiday);
                        }
                    }
                }
            }
            //存储数据
            if (CollUtil.isNotEmpty(weekEndList)) {
                //保存数据
                this.saveBatch(weekEndList);
            }
            //获取当前日期往后的特殊节假日（同步给考勤系统）
            List<Holiday> dayList = this.selectByYear(DateUtil.format(new Date(), Contant.DATA_YEAR));
            //同步数据
            if (CollUtil.isNotEmpty(dayList)) {
                //构造获取数据
                AddBatchHolidayVO addBatchHolidayVO = new AddBatchHolidayVO();
                //同步数据到考勤系统
                //获取年份
                addBatchHolidayVO.setYear(DateUtil.format(new Date(), Contant.DATA_YEAR));
                //获取所有数据
                addBatchHolidayVO.setHolidayList(dayList);
                //推送数据
                httpUtils.postSendBodyAttList(dockingProperties.getCheckService(), addBatchHolidayVO);
                log.info("同步成功！");
            }

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 根据日期查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public List<Holiday> selectByDateTime(String startTime, String endTime) {
        QueryWrapper<Holiday> query = new QueryWrapper();
        //开始时间
        if (CharSequenceUtil.isNotBlank(startTime)) {
            query.gt("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m-%d' )", startTime);
        }
        //结束时间
        if (CharSequenceUtil.isNotBlank(endTime)) {
            query.lt("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m-%d' )", endTime);
        }
        return this.list(query);
    }


    /**
     * 确认某天是不是节假日或者是周六日
     *
     * @param day
     * @return
     */
    @Override
    public Boolean isHoliday(String day) {
        QueryWrapper<Holiday> query = new QueryWrapper();
        //开始时间
        if (CharSequenceUtil.isNotBlank(day)) {
            query.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m-%d' )", day);
        }
        //只获取节假日和周六日的数据
        List<Integer> type = new ArrayList<>();
        //节假日
        type.add(Contant.DATE_TYPE_1);
        //周末
        type.add(Contant.DATE_TYPE_2);
        query.in("F_INT_HOLIDAY_TYPE",type);
        List<Holiday> list = this.list(query);
        if (CollUtil.isNotEmpty(list)) {
            return true;
        }
        return false;
    }


    /**
     * 封装Excel
     *
     * @return
     */
    public Workbook writeToExcelByList(String month, HolidayMonthVO holidayMonthVO, String orgName) {
        try {
            //获取标题名称
            Date dateMonth = DateUtil.parse(month, "yyyy-MM");
            //创建Excel
            Workbook workbook = new XSSFWorkbook();
            //封装样式
            //创建sheet页
            Sheet sheet = workbook.createSheet(Contant.SHEET_NAME);
            //内容样式
            CellStyle styleContent = PoiUtil.getContentStyle(workbook);
            //标题样式
            CellStyle styleTitle = PoiUtil.getTitleCenterStyle(workbook);
            //封装标题
            creatTitle(sheet, holidayMonthVO.getHolidayList().size(), styleTitle, orgName, dateMonth);
            //封装主体内容
            creatContent(sheet, holidayMonthVO, styleContent);

            return workbook;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 封装主题内容
     */
    private void creatContent(Sheet sheet, HolidayMonthVO holidayMonthVO, CellStyle styleContent) {
        try {
            //行数从第二行开始
            Integer rowIndex = Contant.INT_NUMBER_1;
            //保存天和具体部门的关系
            Map<Integer, List<HolidayDayVO>> holidayMap = new HashMap<>();
            //处理第二行日期数据
            if (CollUtil.isNotEmpty(holidayMonthVO.getHolidayList())) {
                //列数统一从第二列开始
                Integer cellIndex = Contant.INT_NUMBER_1;
                Row row = sheet.createRow(rowIndex);
                //第一列固定位单位
                addCell(row, Contant.INT_INDEX_0, "部门", styleContent);
                //设置字体的宽度
                sheet.setColumnWidth(0, 25 * 256);
                sheet.setHorizontallyCenter(true);
                //处理日期(动态列数)
                for (HolidayDetailVO itemHoliday : holidayMonthVO.getHolidayList()) {
                    //设置字体的宽度
                    sheet.setColumnWidth(cellIndex, 25 * 256);
                    sheet.setHorizontallyCenter(true);
                    //循环处理
                    addCell(row, cellIndex, DateUtil.format(itemHoliday.getHolidayData(), "yyyy-MM-dd"), styleContent);
                    //存储数据
                    holidayMap.put(cellIndex, itemHoliday.getHolidayDayVOList());
                    cellIndex++;
                }
                //下一行
                rowIndex++;
            }
            //人员排班信息第三行开始展示
            for (DutyDept item : holidayMonthVO.getDeptList()) {

                //创建行数
                Row row = sheet.createRow(rowIndex);
                //第一列统一展示部门名称
                addCell(row, Contant.INT_INDEX_0, item.getDeptName(), styleContent);
                //接下来处理人员
                for (int i = Contant.INT_NUMBER_1; i < holidayMonthVO.getHolidayList().size() + 1; i++) {
                    //获取对应列下面的人员
                    List<HolidayDeptUserDetailVO> deptUserList = getSelectByUserList(holidayMap.get(i), item.getId());
                    //处理人员数据
                    if (CollUtil.isNotEmpty(deptUserList)) {
                        addCell(row, i, selectUsers(deptUserList), styleContent);
                    } else {
                        addCell(row, i, "", styleContent);
                    }
                }
                //下一行
                rowIndex++;
            }
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 封装用户数据
     *
     * @param deptUserList
     * @return
     */
    public String selectUsers(List<HolidayDeptUserDetailVO> deptUserList) {
        String users = "";
        List<String> userList = new ArrayList<>();
        if (CollUtil.isNotEmpty(deptUserList)) {
            //循环处理
            for (HolidayDeptUserDetailVO item : deptUserList) {
                if (null != item && CharSequenceUtil.isNotBlank(item.getUserName()) && CharSequenceUtil.isNotBlank(item.getUserMobile())) {
                    userList.add(item.getUserName().concat("(").concat(item.getUserMobile()).concat(")").concat("\n"));
                }
            }
            //拼接数据
            if (CollUtil.isNotEmpty(userList)) {
                users = String.join(" ", userList);
            }
        }
        return users;
    }

    /**
     * 获取对应部门人员
     *
     * @param holidayList
     * @param deptId
     * @return
     */
    public List<HolidayDeptUserDetailVO> getSelectByUserList(List<HolidayDayVO> holidayList, String deptId) {
        if (CollUtil.isNotEmpty(holidayList)) {
            for (HolidayDayVO item : holidayList) {
                if (CharSequenceUtil.equals(item.getDeptId(), deptId)) {
                    return item.getDeptUserList();
                }
            }
        }
        return null;
    }

    /**
     * 封装标题
     *
     * @param sheet
     * @param titleLength
     * @param styleTitle
     * @param orgName
     * @param date
     */
    public void creatTitle(Sheet sheet, Integer titleLength, CellStyle styleTitle, String orgName, Date date) {
        try {
            String title = String.format(Contant.SHEET_TITLE_NAME, orgName, DateUtil.format(date, "yyyy"), DateUtil.format(date, "MM"));
            //创建标题行第一行
            Row row = sheet.createRow(Contant.INT_INDEX_0);
            //设置高度
            row.setHeightInPoints(50);
            //合并单元格
            sheet.addMergedRegion(mergeCell(Contant.INT_INDEX_0, Contant.INT_INDEX_0, Contant.INT_INDEX_0, titleLength));
            //创建列
            addCell(row, Contant.INT_INDEX_0, title, styleTitle);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 合并单元格
     *
     * @param firstRow 第一行
     * @param lastRow  最后一行
     * @param firstCol 初始列
     * @param lastCol  最后一列
     * @return
     */
    public CellRangeAddress mergeCell(int firstRow, int lastRow, int firstCol, int lastCol) {
        CellRangeAddress cellRangeAddress = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
        return cellRangeAddress;
    }


    private void addCell(Row row, int cellIndex, String value, CellStyle styleContent) {
        Cell cell = row.createCell(cellIndex);
        cell.setCellStyle(styleContent);
        cell.setCellValue(YcgzUtil.getString(value));
    }

    /**
     * 格式化导入数据
     *
     * @return
     */
    public List<HolidayDeptUser> formatUserList(String userList, String deptId, String holidayId, Date month, MicrovideoUserVO userVO, StringBuffer errLog, Integer hang, Integer lie) {
        try {
            //根据当前天和部门获取数据
            Map<String, String> userMap = formatUserList(holidayId, deptId);
            //获取用户手机号和ID的关系
            Map<String, String> mobileUserMap = formatMobileUserList(deptId);
            //创建实体
            List<HolidayDeptUser> userLists = new ArrayList<>();
            //处理数据
            if (CharSequenceUtil.isNotBlank(userList)) {
                String users[] = userList.split(",");
                HolidayDeptUser holidayDeptUser = null;
                //获取用户列表
                for (String user : users) {
                    holidayDeptUser = new HolidayDeptUser();
                    //获取手机号
                    String mobile = user.substring(user.indexOf("(") + 1, user.indexOf(")"));
                    //根据手机号判定是否已经排过值班、避免重复数据
                    if (CharSequenceUtil.isNotBlank(userMap.get(mobile))) {
                        continue;
                    }
                    //如果用户不存在
                    if (CharSequenceUtil.isBlank(mobileUserMap.get(mobile))) {
                        errLog.append("第" + hang + "行，第" + lie + "列" + user + "未找到！<br>");
                        continue;
                    }
                    //根据用户
                    holidayDeptUser.setDeptUserId(mobileUserMap.get(mobile));
                    //日期ID
                    holidayDeptUser.setHolidayId(holidayId);
                    //部门ID
                    holidayDeptUser.setDutyDeptId(deptId);
                    //年
                    holidayDeptUser.setDutyYear(DateUtil.year(month));
                    //月
                    holidayDeptUser.setDutyMonth(DateUtil.month(month));
                    //创建时间
                    holidayDeptUser.setCreateTime(new Date());
                    //创建人
                    holidayDeptUser.setCreateBy(userVO.getId());
                    //返回数据
                    userLists.add(holidayDeptUser);
                }
            }

            return userLists;

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 格式化人员数据
     *
     * @return
     */
    public Map<String, String> formatUserList(String holidayId, String deptId) {
        try {
            Map<String, String> userMap = new HashMap<>();
            //根据holiday和部门获取已经排班的数据
            List<HolidayDeptUserDetailVO> holidayDeptUserDetailVOS = holidayDeptUserService.selectByHolidayIdsWithDept(holidayId, deptId);
            if (CollUtil.isNotEmpty(holidayDeptUserDetailVOS)) {
                for (HolidayDeptUserDetailVO item : holidayDeptUserDetailVOS) {
                    userMap.put(item.getUserMobile(), item.getId());
                }
            }
            return userMap;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 格式化人员数据
     *
     * @return
     */
    public Map<String, String> formatMobileUserList(String deptId) {
        try {
            Map<String, String> mobileUserMap = new HashMap<>();
            //根据holiday和部门获取已经排班的数据
            List<DeptUser> deptUserList = deptUserService.selectByFormId(deptId);
            if (CollUtil.isNotEmpty(deptUserList)) {
                for (DeptUser item : deptUserList) {
                    mobileUserMap.put(item.getUserMobile(), item.getId());
                }
            }
            return mobileUserMap;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * @return
     */
    public Map<String, String> selectByMonth(String month) {
        Map<String, String> map = new HashMap<String, String>();
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m' )", month);
        queryWrapper.gt("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m-%d' )", DateUtil.format(new Date(), "yyyy-MM-dd"));
        List<Holiday> list = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            for (Holiday item : list) {
                map.put(DateUtil.format(item.getHolidayData(), "yyyy-MM-dd"), item.getId());
            }
        }
        return map;
    }


    /**
     * 根据年份获取数据
     *
     * @return
     */
    public List<Holiday> selectByYearList(String year) {
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y' )", year);
        queryWrapper.gt("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m-%d' )", DateUtil.format(new Date(), "yyyy-MM-dd"));
        return this.list(queryWrapper);
    }

    /**
     * 根据年份获取数据
     *
     * @return
     */
    public List<Holiday> selectByYear(String year) {
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y' )", year);
//        queryWrapper.gt("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m-%d' )", DateUtil.format(new Date(), "yyyy-MM-dd"));
        return this.list(queryWrapper);
    }


    /**
     * 构建数据关系
     *
     * @param deptList
     * @param userMap
     * @param holidayUserMap
     * @return
     */
    public List<HolidayDayTodoVO> createHolidayDetail(List<DutyDept> deptList, Map<String, List<DeptUser>> userMap, Map<String, List<HolidayDeptUser>> holidayUserMap) {
        try {
            List<HolidayDayTodoVO> list = new ArrayList<>();
            if (CollUtil.isNotEmpty(deptList)) {
                HolidayDayTodoVO holidayDayTodoVO = null;
                for (DutyDept item : deptList) {
                    holidayDayTodoVO = new HolidayDayTodoVO();
                    //部门ID
                    holidayDayTodoVO.setDeptId(item.getId());
                    //部门名称
                    holidayDayTodoVO.setDeptName(item.getDeptName());
                    //部门下的全部人员
                    holidayDayTodoVO.setTodoUserList(userMap.get(item.getId()));
                    //已选中的人员
                    if (CollUtil.isNotEmpty(holidayUserMap)) {
                        holidayDayTodoVO.setDoneUserList(holidayUserMap.get(item.getId()));
                    }
                    list.add(holidayDayTodoVO);
                }

            }
            return list;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 格式化日期详情数据
     *
     * @return
     */
    public HolidayMonthVO formatHolidayDetails(List<Holiday> holidayList, HolidayMonthVO holidayMonthVO, String type, String month) {
        try {
            //格式化日期
            //创建返回的数据实体
            List<HolidayDetailVO> holidayDetailVOList = new ArrayList<>();
            //获取当前用户信息
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if (null == userVO) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前用户信息！");
            }
            //如果不存在排班日期
            if (CollUtil.isEmpty(holidayList)) {
                return holidayMonthVO;
            }
            //创建根据日期获取人员列表
            Map<String, List<HolidayDeptUserDetailVO>> holidayListMap = new HashMap<>();
            //查询所有需要查询的列表
            List<String> holidayIds = holidayList.stream().map(Holiday::getId).collect(Collectors.toList());
            if (CollUtil.isEmpty(holidayIds)) {
                return holidayMonthVO;
            }
            //获取单位下的部门数据（目前正常执行的部门）
            List<DutyDept> deptList = new ArrayList<>();
            if (CharSequenceUtil.equals(Contant.ADD_DATA_TYPE, type)) {
                //查询当前月份(比较年月,如果大于等于当前时间,查询正常使用的部门和新部门，否则，只查历史部门)
                if (CharSequenceUtil.compare(DateUtil.format(new Date(), "yyyy-MM"), month, false) > 0) {
                    deptList = dutyDeptService.selectByHolidayId(userVO.getGroupId(), holidayIds);
                } else {
                    deptList = dutyDeptService.selectByHolidayIdWithUserDept(userVO.getGroupId(), holidayIds);
                }
            } else {
                deptList = dutyDeptService.selectByHolidayIdWithUserDept(userVO.getGroupId(), holidayIds);
            }
            //封装数据
            if (CollUtil.isNotEmpty(deptList)) {
                //获取部门Ids
                List<String> deptIds = deptList.stream().map(DutyDept::getId).collect(Collectors.toList());
                //如果排班时间和所属部门不为空的情况下，查询具体排班列表
                if (CollUtil.isNotEmpty(deptIds)) {
                    //查询当前日期下的单位列表
                    List<HolidayDeptUserDetailVO> holidayDeptUserList = holidayDeptUserService.selectByHolidayDeptIds(holidayIds, deptIds);
                    if (CollUtil.isNotEmpty(holidayDeptUserList)) {
                        //根据数据日期分组
                        holidayListMap = holidayDeptUserList.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getHolidayId())).collect(Collectors.groupingBy(HolidayDeptUserDetailVO::getHolidayId));
                    }
                }
            }
            //封装数据
            for (Holiday item : holidayList) {
                HolidayDetailVO holidayDetailVO = BeanUtil.copyProperties(item, HolidayDetailVO.class);
                //根据日期获取数据列表

                List<HolidayDeptUserDetailVO> holidayDeptUserList1 = holidayListMap.get(item.getId());
                //获取数据列表
                holidayDetailVO.setHolidayDayVOList(getByHolidayList(deptList, holidayDeptUserList1));
                //回显数据
                holidayDetailVOList.add(holidayDetailVO);
            }

            //返回部门人员日期列表
            holidayMonthVO.setHolidayList(holidayDetailVOList);
            //返回部门列表
            holidayMonthVO.setDeptList(deptList);

            return holidayMonthVO;

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 格式化日期详情数据
     *
     * @return
     */
    public HolidayMonthVO formatHolidayByHolidays(List<Holiday> holidayList, HolidayMonthVO holidayMonthVO, String orgId) {
        try {
            //创建返回的数据实体
            List<HolidayDetailVO> holidayDetailVOList = new ArrayList<>();
            //创建根据日期获取人员列表
            Map<String, List<HolidayDeptUserDetailVO>> holidayListMap = new HashMap<>();
            //查询所有需要查询的列表
            List<String> holidayIds = holidayList.stream().map(Holiday::getId).collect(Collectors.toList());
            if (CollUtil.isEmpty(holidayIds)) {
                return holidayMonthVO;
            }
            //获取单位下的部门数据（目前正常执行的部门）
            List<DutyDept> deptList = dutyDeptService.selectByHolidayId(orgId, holidayIds);
            //封装数据
            if (CollUtil.isNotEmpty(deptList)) {
                //获取部门Ids
                List<String> deptIds = deptList.stream().map(DutyDept::getId).collect(Collectors.toList());
                //如果排班时间和所属部门不为空的情况下，查询具体排班列表
                if (CollUtil.isNotEmpty(deptIds)) {
                    //查询当前日期下的单位列表
                    List<HolidayDeptUserDetailVO> holidayDeptUserList = holidayDeptUserService.selectByHolidayDeptIds(holidayIds, deptIds);
                    if (CollUtil.isNotEmpty(holidayDeptUserList)) {
                        //根据数据日期分组
                        holidayListMap = holidayDeptUserList.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getHolidayId())).collect(Collectors.groupingBy(HolidayDeptUserDetailVO::getHolidayId));
                    }
                }
            }
            //封装数据
            for (Holiday item : holidayList) {
                HolidayDetailVO holidayDetailVO = BeanUtil.copyProperties(item, HolidayDetailVO.class);
                //根据日期获取数据列表

                List<HolidayDeptUserDetailVO> holidayDeptUserList1 = holidayListMap.get(item.getId());
                //获取数据列表
                holidayDetailVO.setHolidayDayVOList(getByHolidayList(deptList, holidayDeptUserList1));
                //回显数据
                holidayDetailVOList.add(holidayDetailVO);
            }

            //返回部门人员日期列表
            holidayMonthVO.setHolidayList(holidayDetailVOList);
            //返回部门列表
            holidayMonthVO.setDeptList(deptList);

            return holidayMonthVO;

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * 封装部门和人员数据
     *
     * @return
     */
    public List<HolidayDayVO> getByHolidayList(List<DutyDept> deptList, List<HolidayDeptUserDetailVO> holidayDeptUserList) {
        try {
            List<HolidayDayVO> holidayDayVOList = new ArrayList<>();
            Map<String, List<HolidayDeptUserDetailVO>> userMap = new HashMap<>();
            if (CollUtil.isNotEmpty(holidayDeptUserList)) {
                userMap = holidayDeptUserList.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getDutyDeptId())).collect(Collectors.groupingBy(HolidayDeptUserDetailVO::getDutyDeptId));
            }
            //创建实体
            HolidayDayVO holidayDayVO = null;
            for (DutyDept item : deptList) {
                holidayDayVO = new HolidayDayVO();
                //部门ID
                holidayDayVO.setDeptId(item.getId());
                //部门名称
                holidayDayVO.setDeptName(item.getDeptName());
                //部门值班人员列表
                if (CollUtil.isNotEmpty(userMap)) {
                    holidayDayVO.setDeptUserList(userMap.get(item.getId()));
                }
                //回显数据
                holidayDayVOList.add(holidayDayVO);
            }
            return holidayDayVOList;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 根据年月查询数据
     *
     * @param month
     * @return
     */
    public List<Holiday> queryByMonth(String month) {
        QueryWrapper<Holiday> query = new QueryWrapper<Holiday>();
        query.orderByAsc("F_DT_HOLIDAY_DATA");
        query.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m' )", month);
        query.notIn("F_INT_HOLIDAY_TYPE", Contant.DATE_TYPE_3);
        return this.list(query);
    }
}
