package cn.microvideo.module.plyh.core.vo;

import lombok.Data;

@Data
public class FlowCurrListVO {
    //taskId
    private String flowCurrentStep;
    private String flowCurrentStepKey;
    //节点名称
    private String flowCurrentStepName;
    //用户名
    private String flowCurrentUserName;
    //用户ID
    private String flowCurrentUser;
    private String multiInstanceRoot;

    //上一个节点状态
    private String a1FlowTaskTrajectoryEntity;
}
