package cn.microvideo.module.plyh.core.util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.http.HttpHeaders;
import org.springframework.util.FileCopyUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;

/**
 * 盐城国资自定义的工具类
 */
@Slf4j
public class YcgzUtil {
    /**
     * 计算两字数字的和
     *
     * @param num1
     * @param num2
     * @return
     */
    public static Integer numAdd(Integer num1, Integer num2) {
        if (null == num1 && null != num2) {
            return num2;
        }
        if (null != num1 && null == num2) {
            return num1;
        }
        return num1 + num2;
    }

    /**
     * 重新生成文件的名称
     *
     * @param fileName
     * @return
     */
    public static String formatFileName(String fileName) {
        if (CharSequenceUtil.isBlank(fileName)) {
            return fileName;
        }
        //文件前缀
        String uuid = IdUtil.simpleUUID();
        //获取文件后缀
        String suffix = CharSequenceUtil.sub(fileName, fileName.indexOf("."), fileName.length());
        return uuid.concat(suffix);
    }

    /**
     * 根据文件地址和文件byte保存文件
     *
     * @param filePath
     * @param in
     * @throws IOException
     */
    public static void saveFile(String filePath, byte[] in) throws IOException {
        File fileSelf = new File(filePath);
        if (!fileSelf.getParentFile().exists()) {
            fileSelf.getParentFile().mkdirs();
        }
        //复制文件
        FileCopyUtils.copy(in, fileSelf);
    }

    /**
     * 给导出excel表格设置响应头
     * @param response
     * @param filename 导出文件名称
     */
    public static void addExcelResposeHeader(HttpServletResponse response, String filename){
        response.reset();
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        String encode;
        try {
            encode = URLEncoder.encode(filename, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error("字符编码错误:{}", e.getMessage());
            encode = filename;
        }
        response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + encode);
    }

    /**
     * 将对象装换为map
     *
     * @param bean
     * @return
     */
    public static <T> HashMap<String, Object> beanToMap(T bean) {
        HashMap<String, Object> map = new HashMap();
        if (bean != null) {
            BeanMap beanMap = BeanMap.create(bean);
            for (Object key : beanMap.keySet()) {
                map.put(key + "", beanMap.get(key));
            }
        }
        return map;
    }


    /**
     * 对URL使用UTF-8编码
     *
     * @param url
     * @return
     */
    public static String encodeUTF8(String url) {
        try {
            return URLEncoder.encode(url, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage());
            return url;
        }
    }



    public static String getString(Object object)
    {
        return null == object ? "" : object.toString();
    }



}
