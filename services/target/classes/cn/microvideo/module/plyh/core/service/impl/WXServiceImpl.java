package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.commonconstant.Contant;
import cn.microvideo.module.plyh.commonconstant.Enum.QwUrlEnum;
import cn.microvideo.module.plyh.core.service.WXService;
import cn.microvideo.module.plyh.core.util.FileHttpUtils;
import cn.microvideo.module.plyh.core.util.RedisUtil;
import cn.microvideo.module.plyh.core.util.WeChatConfigurationProperties;
import cn.microvideo.module.plyh.core.util.YcgzUtil;
import cn.microvideo.module.plyh.core.vo.ApplicationProperties;
import cn.microvideo.module.plyh.core.vo.SendMessageResultVO;
import cn.microvideo.module.plyh.core.vo.WXResultVO;
import cn.microvideo.module.plyh.exception.BizException;
import cn.microvideo.sdk.qywx.msg.send.NewsMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/9/6 9:20
 */
@Service
@Slf4j
public class WXServiceImpl implements WXService {
    @Resource
    private FileHttpUtils httpUtils;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private WeChatConfigurationProperties weChatConfigurationProperties;


    @Override
    public String selectByToken(String appCode, Integer appData) {
        try {
            log.info("步骤一");
            //获取应用下的对应的
            if (ObjectUtil.isEmpty(appCode)) {
                throw new BizException(CommonConstant.ERROR_500, "应用Code不存在！");
            }
            String corpsecret = getCorpSecret(appData);
            //从redis里面获取本应用生成token
            String accessToken = getAccessToken(appData, corpsecret);
            //构造请求参数
            Map<String, Object> map = new HashMap<String, Object>();
            //构造参数
            map.put("access_token", accessToken);
            log.info("access_token" + accessToken);
            //code
            map.put("code", appCode);
            log.info("code" + appCode);

            //获取用户信息
            log.info("开始获取用户信息--------------------------------------->" + accessToken);
            WXResultVO wxResultVO1 = getByString(QwUrlEnum.GET_USERINFO.getUrl(), map);
            log.info("用户id信息:" + wxResultVO1.toString());
            if (CharSequenceUtil.isEmpty(wxResultVO1.getUserid())) {
                throw new BizException(CommonConstant.ERROR_500, "获取用户信息失败！");
            }
            return wxResultVO1.getUserid();
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

    /**
     * -发送微信消息到待办事项应用
     *
     * @param title  标题
     * @param desc   描述
     * @param url    跳转地址
     * @param touser 接收用户
     */
    @Override
    public SendMessageResultVO sendWeChatMessage(String title, String desc, String url, String touser, Integer agentid) {
        try {
            if (weChatConfigurationProperties.isEnable()) {
                //测试模式不推送消息
                NewsMessage message = new NewsMessage();
                message.setTouser(touser);
                message.add(title, desc, url, "");
                message.setAgentid(agentid);
                log.info("--------------------------->" + url);
                //获取应用corpsecret
                String corpsecret = getCorpSecret(agentid);
                //获取token
                String accessToken = getAccessToken(agentid, corpsecret);
                String sendUrl = QwUrlEnum.SEND_MESSAGE_URL.getUrl().concat(accessToken);
                //请求
                return postByString(sendUrl, YcgzUtil.beanToMap(message));
            }
            return null;

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * get请求接口
     *
     * @return
     */
    public SendMessageResultVO postByString(String serverUrl, HashMap<String, Object> bodyMap) {
        try {
            log.info("getByString + 123456111111111111" + serverUrl);
//            //获取返回内容
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);

//            String result = httpUtils.postSendHeaderBodyRaw(serverUrl, headers, JSONUtil.parseObj(bodyMap));

            String result = httpUtils.postSendBody(serverUrl, bodyMap);
            //解析参数
            if (CharSequenceUtil.isBlank(result)) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            //解析返回数据对象
            SendMessageResultVO result1 = JSONUtil.toBean(result, SendMessageResultVO.class);
            //判定请求返回值
            if (!ObjectUtil.equal(Contant.INTEGER_HTTP_OK_0, result1.getErrcode())) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            //返回数据
            return result1;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 获取corpsecret
     *
     * @param agentid
     * @return
     */
    public String getCorpSecret(Integer agentid) {
        try {
            log.info("步骤二");
            if (null == agentid) {
                throw new BizException(CommonConstant.ERROR_500, "未找到企业微信配置内容！");
            }
            //获取所有的模块的配置
            List<ApplicationProperties> agentLists = weChatConfigurationProperties.getApplicationList();
            if (CollUtil.isEmpty(agentLists)) {
                throw new BizException(CommonConstant.ERROR_500, "未找到企业微信配置内容！");
            }
            for (ApplicationProperties agentList : agentLists) {
                //匹配当前的应用下的secret（存在则返回）
                if (CharSequenceUtil.equals(String.valueOf(agentid), agentList.getAgentId())) {
                    return agentList.getSecret();
                }
            }
            //不存在则直接抛出异常
            throw new BizException(CommonConstant.ERROR_500, "未找到企业微信配置内容！");

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * 获取accessToken
     *
     * @param agentid
     * @param corpsecret
     * @return
     */
    public String getAccessToken(Integer agentid, String corpsecret) {
        try {
            log.info("getAccessToken");
            //从redis里面获取本应用生成token
            Object accessToken = redisUtil.get(String.valueOf(agentid));
            if (ObjectUtil.isNotEmpty(accessToken)) {
                return accessToken.toString();
            } else {
                //
                String corpid = weChatConfigurationProperties.getCorpId();
                log.info("--------------->" + corpid);
                if (CharSequenceUtil.isBlank(corpid)) {
                    throw new BizException(CommonConstant.ERROR_500, "未找到企业微信配置内容！");
                }
                //构造请求
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("corpid", corpid);
                //获取agentId下的secret
                map.put("corpsecret", corpsecret);
                //获取access_token
                WXResultVO wxResultVO = getByString(QwUrlEnum.ACCESS_TOKEN.getUrl(), map);
                log.info("用户token信息:" + wxResultVO.toString());
                //存储到redis
                redisUtil.setEx(String.valueOf(agentid), wxResultVO.getAccess_token(), 7199, TimeUnit.SECONDS);

                return wxResultVO.getAccess_token();
            }
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

    /**
     * get请求接口
     *
     * @return
     */
    public WXResultVO getByString(String serverUrl, Map<String, Object> params) {
        try {
            log.info("getByString + 123456111111111111" + serverUrl);
            //获取返回内容
            String result = httpUtils.getSendParamBody(serverUrl, params);
            //解析参数
            if (CharSequenceUtil.isBlank(result)) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            //解析返回数据对象
            WXResultVO result1 = JSONUtil.toBean(result, WXResultVO.class);
            //判定请求返回值
            if (!ObjectUtil.equal(Contant.INTEGER_HTTP_OK_0, result1.getErrcode())) {
                throw new BizException(CommonConstant.ERROR_500, result);
            }
            //返回数据
            return result1;
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }


    /**
     * -发送微信消息到待办事项应用
     *
     * @param title  标题
     * @param desc   描述
     * @param url    跳转地址
     * @param touser 接收用户
     */
    @Override
    public SendMessageResultVO sendWeChatMessagePic(String title, String desc, String url, String touser, String pic, Integer agentid) {
        try {
            if (weChatConfigurationProperties.isEnable()) {
                //测试模式不推送消息
                NewsMessage message = new NewsMessage();
                message.setTouser(touser);
                message.add(title, desc, url, pic);
                message.setAgentid(agentid);
                log.info("--------------------------->" + url);
                //获取应用corpsecret
                String corpsecret = getCorpSecret(agentid);
                //获取token
                String accessToken = getAccessToken(agentid, corpsecret);
                String sendUrl = QwUrlEnum.SEND_MESSAGE_URL.getUrl().concat(accessToken);
                //请求
                return postByString(sendUrl, YcgzUtil.beanToMap(message));
            }
            return null;

        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }

    }

}
