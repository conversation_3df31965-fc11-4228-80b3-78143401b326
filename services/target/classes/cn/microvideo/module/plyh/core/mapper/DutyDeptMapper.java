package cn.microvideo.module.plyh.core.mapper;

import cn.microvideo.module.plyh.core.entity.DutyDept;
import cn.microvideo.module.plyh.core.vo.DutyDeptUserVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 排班部门管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface DutyDeptMapper extends BaseMapper<DutyDept> {

    /**
     * 数据分页查询
     *
     * @param page
     * @param dutyDept
     * @return
     */
    public IPage<DutyDeptUserVO> pageList(Page<DutyDeptUserVO> page, @Param("query") DutyDept dutyDept);


    /**
     * 数据批量查询
     *
     * @param dutyDept
     * @return
     */
    public List<DutyDeptUserVO> queryList(@Param("query") DutyDept dutyDept);

    /**
     * 根据ids获取数据
     *
     * @param holidayIds
     * @return
     */
    public List<DutyDept> selectByHolidayId(@Param("orgId") String orgId, @Param("holidayIds") List<String> holidayIds);

    /**新增部门的人员列表
     * @param orgId
     * @param holidayIds
     * @return
     */
    public List<DutyDept> selectByHolidayIdWithUserDept(@Param("orgId") String orgId, @Param("holidayIds") List<String> holidayIds);


    int findTopByOrderBySortOrderDesc(String groupId);

}
