package cn.microvideo.module.plyh.core.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description: 节假日、部门关系管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Data
@TableName("b_plyh_holiday_dept_user")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "b_plyh_holiday_dept_user对象", description = "节假日、部门关系管理表")
public class HolidayDeptUser {

    /**
     * UUID
     */

    @ApiModelProperty(value = "UUID")
    @TableId("F_VC_ID")
    private String id;
    /**
     * 日期ID
     */

    @ApiModelProperty(value = "日期ID")
    @TableField("F_VC_HOLIDAY_ID")
    private String holidayId;
    /**
     * 部门用户关联ID
     */

    @ApiModelProperty(value = "部门用户关联ID")
    @TableField("F_VC_DEPT_USER_ID")
    private String deptUserId;
    /**
     * 创建人
     */

    @ApiModelProperty(value = "创建人")
    @TableField("F_VC_CREATE_BY")
    private String createBy;
    /**
     * 创建时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @TableField("F_DT_CREATE_TIME")
    private Date createTime;
    /**
     * 排班部门ID
     */

    @ApiModelProperty(value = "排班部门ID")
    @TableField("F_VC_DUTY_DEPT_ID")
    private String dutyDeptId;
    /**
     * 所属月份
     */

    @ApiModelProperty(value = "所属月份")
    @TableField("F_INT_DUTY_MONTH")
    private Integer dutyMonth;
    /**
     * 更新人
     */

    @ApiModelProperty(value = "更新人")
    @TableField("F_VC_UPDATE_BY")
    private String updateBy;
    /**
     * 更新时间
     */

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    @TableField("F_DT_UPDATE_TIME")
    private Date updateTime;

    /**
     * 所属月份
     */
    @ApiModelProperty(value = "所属月份")
    @TableField("F_INT_DUTY_YEAR")
    private Integer dutyYear;
}
