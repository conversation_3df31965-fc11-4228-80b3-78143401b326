package cn.microvideo.module.plyh.core.service;

import cn.microvideo.module.plyh.core.entity.Holiday;
import cn.microvideo.module.plyh.core.vo.AddBatchHolidayVO;
import cn.microvideo.module.plyh.core.vo.HolidayDetailVO;
import cn.microvideo.module.plyh.core.vo.HolidayMonthVO;
import cn.microvideo.module.plyh.core.vo.HolidayTodoDetailVO;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * @Description: 节假日、休息、值班管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface IHolidayService extends IService<Holiday> {

    /**
     * 新增节假日
     * @param holiday
     */
    public void saveByEntity(Holiday holiday);

    /**
     * 批量新增
     * @param addBatchHolidayVO
     */
    public void saveByBatchEntity(AddBatchHolidayVO addBatchHolidayVO);


    /**
     * 根据月份查询数据
     * @param month
     * @return
     */
    public HolidayMonthVO selectByMonth(String month,String type);


    /**
     * 根据月份查询数据
     * @param holidayList
     * @return
     */
    public HolidayMonthVO selectByHolidays(List<Holiday> holidayList,String orgId);
    /**
     * 根据具体时间查询数据
     * @param id
     * @return
     */
    public HolidayTodoDetailVO queryByIdDetailUser(String id);

    /**
     * 数据导入
     * @param inputStream
     */
    public void inputDutyExcel(InputStream inputStream,String month);

    /**
     *导出值班排班的列表
     * @param month
     * @return
     */
    public Workbook exportDutyExcel(String month,String orgName,String orgId);

    /**
     * 刷新数据
     */
    public void refreshHolidayData();


    /**
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public List<Holiday> selectByDateTime(String startTime,String endTime);

    /**
     * 确认某天是不是节假日或者是周六日
     * @param day
     * @return
     */
    public Boolean isHoliday(String day);


}
