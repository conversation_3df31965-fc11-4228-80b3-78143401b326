package cn.microvideo.module.plyh.core.mapper;

import cn.microvideo.module.plyh.core.entity.DutyDept;
import cn.microvideo.module.plyh.core.entity.DutyRelease;
import cn.microvideo.module.plyh.core.vo.DutyDeptUserVO;
import cn.microvideo.module.plyh.core.vo.DutyReleaseSearchVO;
import cn.microvideo.module.plyh.core.vo.DutyReleaseUserVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 值班发布记录表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
public interface DutyReleaseMapper extends BaseMapper<DutyRelease> {

    /**
     * 数据分页查询
     * @param page
     * @param dutyRelease
     * @return
     */
    public IPage<DutyReleaseUserVO> pageList(Page<DutyReleaseUserVO> page, @Param("query") DutyReleaseSearchVO dutyRelease);

}
