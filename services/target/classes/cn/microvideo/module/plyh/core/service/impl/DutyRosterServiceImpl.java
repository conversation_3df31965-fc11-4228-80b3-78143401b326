package cn.microvideo.module.plyh.core.service.impl;

import cn.microvideo.module.plyh.core.entity.DutyRoster;
import cn.microvideo.module.plyh.core.entity.DutyRosterSchedule;
import cn.microvideo.module.plyh.core.mapper.DutyRosterMapper;
import cn.microvideo.module.plyh.core.service.IDutyRosterScheduleService;
import cn.microvideo.module.plyh.core.service.IDutyRosterService;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.vo.DutyRosterAddRequest;
import cn.microvideo.module.plyh.core.vo.DutyRosterEditRequest;
import cn.microvideo.module.plyh.core.vo.DutyRosterVO;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 值班标题主表 服务实现类
 *

 * @since 2025-06-11
 */
@Slf4j
@Service
public class DutyRosterServiceImpl extends ServiceImpl<DutyRosterMapper, DutyRoster> implements IDutyRosterService {

    @Resource
    private IDutyRosterScheduleService dutyRosterScheduleService;

    @Resource
    private UserInfoUtil userInfoUtil;

    @Override
    public IPage<DutyRosterVO> queryPageListByMonth(Page<DutyRosterVO> page, String month) {
        // 获取当前登录用户信息
        MicrovideoUserVO currentUser = userInfoUtil.getHttpSessionUser();
        if (currentUser == null) {
            throw new BizException(CommonConstant.ERROR_500, "未找到当前用户信息");
        }

        // 验证月份格式
        if (month == null || !month.matches("\\d{4}-\\d{2}")) {
            throw new BizException(CommonConstant.ERROR_500, "月份格式错误，请使用 yyyy-MM 格式");
        }

        // 使用关联查询获取值班表及排班时间
        return baseMapper.queryPageListByMonth(page, month, currentUser.getGroupId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DutyRoster addDutyRosterWithSchedules(DutyRosterAddRequest request) {
        // 获取当前登录用户信息
        MicrovideoUserVO currentUser = userInfoUtil.getHttpSessionUser();
        if (currentUser == null) {
            throw new BizException(CommonConstant.ERROR_500, "未找到当前用户信息");
        }

        // 创建值班表主记录
        DutyRoster dutyRoster = new DutyRoster();
        dutyRoster.setTitle(request.getTitle());
        dutyRoster.setOrgId(request.getOrgId() != null ? request.getOrgId() : currentUser.getGroupId());
        dutyRoster.setOrgName(request.getOrgName() != null ? request.getOrgName() : currentUser.getGroupName());
        dutyRoster.setSort(request.getSort());
        dutyRoster.setCreateBy(currentUser.getId());

        // 保存值班表主记录
        this.save(dutyRoster);

        // 保存排班时间记录
        if (request.getScheduleTimes() != null && !request.getScheduleTimes().isEmpty()) {
            for (LocalDate scheduleTime : request.getScheduleTimes()) {
                DutyRosterSchedule schedule = new DutyRosterSchedule();
                schedule.setRosterId(dutyRoster.getId());
                schedule.setScheduleTime(scheduleTime);
                // 计算周几（1=周一，7=周日）
                schedule.setDayOfWeek(scheduleTime.getDayOfWeek().getValue());
                dutyRosterScheduleService.save(schedule);
            }
        }

        return dutyRoster;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DutyRoster editDutyRosterWithSchedules(DutyRosterEditRequest request) {
        // 获取当前登录用户信息
        MicrovideoUserVO currentUser = userInfoUtil.getHttpSessionUser();
        if (currentUser == null) {
            throw new BizException(CommonConstant.ERROR_500,"未找到当前用户信息");
        }

        // 查询原有的值班表记录
        DutyRoster existingDutyRoster = this.getById(request.getId());
        if (existingDutyRoster == null) {
            throw new BizException(CommonConstant.ERROR_500,"未找到要编辑的值班表");
        }

        // 更新值班表主记录
        existingDutyRoster.setTitle(request.getTitle());
        existingDutyRoster.setOrgId(request.getOrgId() != null ? request.getOrgId() : existingDutyRoster.getOrgId());
        existingDutyRoster.setOrgName(request.getOrgName() != null ? request.getOrgName() : existingDutyRoster.getOrgName());
        existingDutyRoster.setSort(request.getSort());
        existingDutyRoster.setUpdateBy(currentUser.getId());

        // 保存值班表主记录
        this.updateById(existingDutyRoster);

        // 删除原有的排班时间记录
        LambdaQueryWrapper<DutyRosterSchedule> deleteWrapper = new LambdaQueryWrapper<DutyRosterSchedule>();
        deleteWrapper.eq(DutyRosterSchedule::getRosterId, request.getId());
        dutyRosterScheduleService.remove(deleteWrapper);

        // 保存新的排班时间记录
        if (request.getScheduleTimes() != null && !request.getScheduleTimes().isEmpty()) {
            for (LocalDate scheduleTime : request.getScheduleTimes()) {
                DutyRosterSchedule schedule = new DutyRosterSchedule();
                schedule.setRosterId(request.getId());
                schedule.setScheduleTime(scheduleTime);
                // 计算周几（1=周一，7=周日）
                schedule.setDayOfWeek(scheduleTime.getDayOfWeek().getValue());
                dutyRosterScheduleService.save(schedule);
            }
        }

        return existingDutyRoster;
    }

}