package cn.microvideo.module.plyh.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.microvideo.module.plyh.commonconstant.Contant;
import cn.microvideo.module.plyh.core.entity.DutyRoster;
import cn.microvideo.module.plyh.core.entity.DutyRosterSchedule;
import cn.microvideo.module.plyh.core.mapper.DutyRosterMapper;
import cn.microvideo.module.plyh.core.service.IDutyRosterScheduleService;
import cn.microvideo.module.plyh.core.service.IDutyRosterService;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.vo.DutyRosterAddRequest;
import cn.microvideo.module.plyh.core.vo.DutyRosterEditRequest;
import cn.microvideo.module.plyh.core.vo.DutyRosterVO;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 值班标题主表 服务实现类
 *

 * @since 2025-06-11
 */
@Slf4j
@Service
public class DutyRosterServiceImpl extends ServiceImpl<DutyRosterMapper, DutyRoster> implements IDutyRosterService {

    @Resource
    private IDutyRosterScheduleService dutyRosterScheduleService;

    @Resource
    private UserInfoUtil userInfoUtil;

    @Override
    public IPage<DutyRosterVO> queryPageListByMonth(Page<DutyRosterVO> page, String month) {

        MicrovideoUserVO currentUser = userInfoUtil.getHttpSessionUser();
        if (currentUser == null) {
            throw new BizException(CommonConstant.ERROR_500, "未找到当前用户信息");
        }
        if (month == null || !month.matches("\\d{4}-\\d{2}")) {
            throw new BizException(CommonConstant.ERROR_500, "月份格式错误，请使用 yyyy-MM 格式");
        }

        return baseMapper.queryPageListByMonth(page, month, currentUser.getGroupId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DutyRoster addDutyRosterWithSchedules(DutyRosterAddRequest request) {

        MicrovideoUserVO currentUser = userInfoUtil.getHttpSessionUser();
        if (currentUser == null) {
            throw new BizException(CommonConstant.ERROR_500, "未找到当前用户信息");
        }
        DutyRoster dutyRoster = new DutyRoster();
        dutyRoster.setTitle(request.getTitle());
        dutyRoster.setOrgId(request.getOrgId() != null ? request.getOrgId() : currentUser.getGroupId());
        dutyRoster.setOrgName(request.getOrgName() != null ? request.getOrgName() : currentUser.getGroupName());
        dutyRoster.setSort(request.getSort());
        dutyRoster.setCreateBy(currentUser.getId());
        dutyRoster.setCreateTime(LocalDateTime.now());
        dutyRoster.setDelFlag(Contant.DEL_FLAG_0);
        dutyRoster.setUpdateBy(currentUser.getId());
        dutyRoster.setUpdateTime(LocalDateTime.now());
        this.save(dutyRoster);
        schedule(dutyRoster.getId(), request.getScheduleTimes());
        return dutyRoster;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DutyRoster editDutyRosterWithSchedules(DutyRosterEditRequest request) {

        MicrovideoUserVO currentUser = userInfoUtil.getHttpSessionUser();
        if (currentUser == null) {
            throw new BizException(CommonConstant.ERROR_500, "未找到当前用户信息");
        }
        DutyRoster existingDutyRoster = this.getById(request.getId());
        if (existingDutyRoster == null) {
            throw new BizException(CommonConstant.ERROR_500, "未找到要编辑的值班表");
        }
        existingDutyRoster.setTitle(request.getTitle());
        existingDutyRoster.setOrgId(request.getOrgId() != null ? request.getOrgId() : existingDutyRoster.getOrgId());
        existingDutyRoster.setOrgName(request.getOrgName() != null ? request.getOrgName() : existingDutyRoster.getOrgName());
        existingDutyRoster.setSort(request.getSort());
        existingDutyRoster.setUpdateBy(currentUser.getId());
        existingDutyRoster.setUpdateBy(currentUser.getId());
        existingDutyRoster.setUpdateTime(LocalDateTime.now());
        this.updateById(existingDutyRoster);
        LambdaQueryWrapper<DutyRosterSchedule> deleteWrapper = new LambdaQueryWrapper<DutyRosterSchedule>();
        deleteWrapper.eq(DutyRosterSchedule::getRosterId, request.getId());
        dutyRosterScheduleService.remove(deleteWrapper);
        schedule(request.getId(), request.getScheduleTimes());
        return existingDutyRoster;
    }

    @Override
    @Transactional
    public void removeBatch(List<String> idList) {

        List<DutyRoster> dutyRosterList = baseMapper.selectBatchIds(idList);
        List<String> removeIds = new ArrayList<>();
        for (DutyRoster dutyRoster : dutyRosterList) {
            if (dutyRoster.getPublishStatus() != null && dutyRoster.getPublishStatus() == 1) {
                throw new BizException(CommonConstant.ERROR_500, "人员安排表已被发布，暂不支持删除");
            }
            removeIds.add(dutyRoster.getId());
        }
        if (CollectionUtil.isNotEmpty(idList)) {
            dutyRosterScheduleService.remove(new QueryWrapper<DutyRosterSchedule>()
                    .lambda().eq(DutyRosterSchedule::getRosterId, removeIds));
            baseMapper.deleteBatchIds(removeIds);
        }
    }

    @Override
    public DutyRosterVO info(String id) {
        DutyRoster dutyRoster = baseMapper.selectById(id);
        if (dutyRoster == null) {
            throw new BizException(CommonConstant.ERROR_500, "数据不存在");
        }

        DutyRosterVO dutyRosterVO = new DutyRosterVO();
        BeanUtil.copyProperties(dutyRoster, dutyRosterVO);

        List<DutyRosterSchedule> dutyRosterScheduleList = dutyRosterScheduleService.list(new QueryWrapper<DutyRosterSchedule>()
                .lambda().eq(DutyRosterSchedule::getRosterId, id));
        List<LocalDate> dateList = dutyRosterScheduleList.stream().map(DutyRosterSchedule::getScheduleTime)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(dateList)) {
            dutyRosterVO.setScheduleTimesStr(StringUtils.join(dateList, "、"));
        }
        dutyRosterVO.setScheduleTimes(dateList);

        return dutyRosterVO;
    }

    private void schedule(String id , List<LocalDate> scheduleTimes) {
        if (CollectionUtil.isNotEmpty(scheduleTimes)) {
            for (LocalDate scheduleTime : scheduleTimes) {
                DutyRosterSchedule schedule = new DutyRosterSchedule();
                schedule.setRosterId(id);
                schedule.setScheduleTime(scheduleTime);
                schedule.setDayOfWeek(scheduleTime.getDayOfWeek().getValue());
                dutyRosterScheduleService.save(schedule);
            }
        }
    }

}