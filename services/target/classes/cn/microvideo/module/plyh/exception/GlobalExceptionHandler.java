package cn.microvideo.module.plyh.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 自定义全局异常处理
 *
 * <AUTHOR>
 * @date 2021/10/24
 */
@Slf4j
@RestControllerAdvice(basePackages = {"cn.microvideo.plyh.portal.application.business.controller"})
public class GlobalExceptionHandler {
    /**
     * 系统业务异常处理
     *
     * @param bizException 自定义业务异常
     * @return 响应实体
     * @date 2020/6/9
     */
    @ExceptionHandler(BizException.class)
    public Result<Object> handleServiceException(BizException bizException) {
        log.error("发生业务异常，异常编码：{}，异常信息：{}", bizException.getCode(), bizException.getMessage());
        return Result.error(bizException.getCode(), bizException.getMessage());
    }
}
