package cn.microvideo.module.plyh.exception;


import lombok.Data;

/**
 * @description: 自定义异常类、处理运行时异常
 * @author: Mr.<PERSON>
 * @date: 2022/6/24 15:15
 */
@Data
public class BizException extends RuntimeException{

    private int code;
    private String message;

    public BizException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
}
