{"groups": [{"name": "docking.services", "type": "cn.microvideo.module.plyh.config.DockingProperties", "sourceType": "cn.microvideo.module.plyh.config.DockingProperties"}, {"name": "microvideo.cas", "type": "cn.microvideo.module.plyh.core.util.CasProperties", "sourceType": "cn.microvideo.module.plyh.core.util.CasProperties"}, {"name": "portal.server", "type": "cn.microvideo.module.plyh.core.util.FaceProperties", "sourceType": "cn.microvideo.module.plyh.core.util.FaceProperties"}, {"name": "qywx", "type": "cn.microvideo.module.plyh.core.util.WeChatConfigurationProperties", "sourceType": "cn.microvideo.module.plyh.core.util.WeChatConfigurationProperties"}], "properties": [{"name": "docking.services.check-service", "type": "java.lang.String", "sourceType": "cn.microvideo.module.plyh.config.DockingProperties"}, {"name": "microvideo.cas.base-server-url", "type": "java.lang.String", "sourceType": "cn.microvideo.module.plyh.core.util.CasProperties"}, {"name": "microvideo.cas.client-host-url", "type": "java.lang.String", "sourceType": "cn.microvideo.module.plyh.core.util.CasProperties"}, {"name": "microvideo.cas.interceptors-ignore-url", "type": "java.lang.String", "sourceType": "cn.microvideo.module.plyh.core.util.CasProperties"}, {"name": "microvideo.cas.server-html-url", "type": "java.lang.String", "sourceType": "cn.microvideo.module.plyh.core.util.CasProperties"}, {"name": "microvideo.cas.server-ignore-url", "type": "java.lang.String", "sourceType": "cn.microvideo.module.plyh.core.util.CasProperties"}, {"name": "microvideo.cas.server-logout-url", "type": "java.lang.String", "sourceType": "cn.microvideo.module.plyh.core.util.CasProperties"}, {"name": "microvideo.cas.session-key", "type": "java.lang.String", "description": "全局会话key", "sourceType": "cn.microvideo.module.plyh.core.util.CasProperties"}, {"name": "portal.server.notice-url", "type": "java.lang.String", "sourceType": "cn.microvideo.module.plyh.core.util.FaceProperties"}, {"name": "portal.server.url", "type": "java.lang.String", "sourceType": "cn.microvideo.module.plyh.core.util.FaceProperties"}, {"name": "qywx.application-list", "type": "java.util.List<cn.microvideo.module.plyh.core.vo.ApplicationProperties>", "description": "应用列表", "sourceType": "cn.microvideo.module.plyh.core.util.WeChatConfigurationProperties"}, {"name": "qywx.callback-list", "type": "java.util.List<cn.microvideo.module.plyh.core.vo.CallbackProperties>", "sourceType": "cn.microvideo.module.plyh.core.util.WeChatConfigurationProperties"}, {"name": "qywx.corp-id", "type": "java.lang.String", "description": "企业号", "sourceType": "cn.microvideo.module.plyh.core.util.WeChatConfigurationProperties"}, {"name": "qywx.enable", "type": "java.lang.Bo<PERSON>an", "description": "是否启用消息推送", "sourceType": "cn.microvideo.module.plyh.core.util.WeChatConfigurationProperties"}, {"name": "qywx.public-path", "type": "java.lang.String", "description": "企业微信公共目录", "sourceType": "cn.microvideo.module.plyh.core.util.WeChatConfigurationProperties"}, {"name": "qywx.url", "type": "java.lang.String", "description": "企业微信api域名", "sourceType": "cn.microvideo.module.plyh.core.util.WeChatConfigurationProperties"}], "hints": []}