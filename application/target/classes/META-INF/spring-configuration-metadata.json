{"groups": [{"name": "bean.custom", "type": "cn.microvideo.plyh.portal.application.config.BeanProperties", "sourceType": "cn.microvideo.plyh.portal.application.config.BeanProperties"}, {"name": "spring.datasource.master", "type": "javax.sql.DataSource", "sourceType": "cn.microvideo.plyh.portal.application.config.BeanConfig", "sourceMethod": "public javax.sql.DataSource masterDataSource() "}, {"name": "spring.datasource.slave1", "type": "javax.sql.DataSource", "sourceType": "cn.microvideo.plyh.portal.application.config.BeanConfig", "sourceMethod": "public javax.sql.DataSource slaveDataSource() "}], "properties": [{"name": "bean.custom.face-server-url", "type": "java.lang.String", "description": "门户OA服务地址", "sourceType": "cn.microvideo.plyh.portal.application.config.BeanProperties"}, {"name": "bean.custom.is-sen-wx-msg", "type": "java.lang.Bo<PERSON>an", "description": "是否发送微信推送", "sourceType": "cn.microvideo.plyh.portal.application.config.BeanProperties"}], "hints": []}