<CorpSetting>
    <!--  是否开启企业微信-->
    <corpOpen>true</corpOpen>
    <corpId>wx01b43f30cc33ff94</corpId>
    <corpSecret>SkEGsv26i1IjD6C7e6LdfKQdEjO67m7oMTnVWneAW_4</corpSecret>
    <corpSSL>false</corpSSL>
    <!-- 可以指定自己的tokenHolderClass，继承与com.riversoft.weixin.common.AccessTokenHolder -->
    <tokenHolderClass>com.riversoft.weixin.common.DefaultAccessTokenHolder</tokenHolderClass>
    <agents>
        <agentSetting>
            <!--  应用id-->
            <agentId>1000006</agentId>
            <!--  应用id-->
            <secret>SkEGsv26i1IjD6C7e6LdfKQdEjO67m7oMTnVWneAW_</secret>
            <!--  应用名称-->
            <name>default</name>
            <!--  回调地址-->
            <callbackUrl>http://test.com</callbackUrl>
            <!-- token-->
            <token>token</token>
            <!-- 加密方式-->
            <aesKey>aesKey</aesKey>
        </agentSetting>
        <agentSetting>
            <agentId>1000007</agentId>
            <!--  应用id-->
            <secret>SkEGsv26i1IjD6C7e6LdfKQdEjO67m7oMTnVWneAW_</secret>
            <name>default</name>
            <callbackUrl>http://test.com</callbackUrl>
            <token>token</token>
            <aesKey>aesKey</aesKey>
        </agentSetting>
    </agents>
</CorpSetting>