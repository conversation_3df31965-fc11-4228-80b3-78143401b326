server:
  port: 8085
  tomcat:
    threads:
      min-spare: 30 #最小线程数
      max: 1000 #最大线程数
    max-connections: 500 #最大连接数
    uri-encoding: UTF-8 #编码方式
    max-http-form-post-size: 0 #post提交数据最大大小，设置为0不限制
    connection-timeout: 10000 #连接超时时间
  servlet:
    context-path: /plyh-schedule-server
knife4j:
  enable: true
  setting:
    language: zh-CN
    enableSwaggerModels: false
    enableHomeCustom: true
    homeCustomLocation: classpath:knife4j/home.md
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Apache License 2.0 | Copyright  2021-[南京感动科技有限公司]
  basic:
    enable: true
    username: microvideo
    password: microvideo@2024
spring:
  servlet:
    multipart:
      # 设置 上传文件的大小
      max-file-size: 200MB
      # 设置 整个请求的大小
      max-request-size: 200MB
  jmx:
    enabled: false
  mvc:
    view:
      prefix: /
      suffix: .html
  thymeleaf:
    prefix: classpath:templates/
    suffix: .html
    check-template-location: true
    cache: false
    servlet:
      content-type: text/html
    mode: HTML
  profiles:
    active: dev

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false #关闭驼峰

management:
  endpoints:
    web:
      exposure:
        include: '*'

#定时任务配置
quartz:
  scheduled:
    #周六日数据刷新的定时任务（每天凌晨一点刷新当前年的）
    synchronousInfo: "0 0 1 * * ?"
#    synchronousInfo: "0 */2 * * * ?"



