package cn.microvideo.plyh.portal.application.business.controller;

import cn.microvideo.module.plyh.core.entity.DutyRoster;
import cn.microvideo.module.plyh.core.entity.DutyRosterSchedule;
import cn.microvideo.module.plyh.core.mapper.DutyRosterMapper;
import cn.microvideo.module.plyh.core.service.IDutyRosterService;
import cn.microvideo.module.plyh.core.service.IDutyRosterScheduleService;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.vo.DutyRosterVO;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.module.plyh.exception.Result;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * DutyRosterController 单元测试
 */
public class DutyRosterControllerTest {

    @Mock
    private IDutyRosterService dutyRosterService;

    @Mock
    private IDutyRosterScheduleService dutyRosterScheduleService;

    @Mock
    private DutyRosterMapper dutyRosterMapper;

    @Mock
    private UserInfoUtil userInfoUtil;

    @InjectMocks
    private DutyRosterController dutyRosterController;

    private DutyRoster testDutyRoster;
    private DutyRosterSchedule testDutyRosterSchedule;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 创建测试数据
        testDutyRoster = new DutyRoster();
        testDutyRoster.setId("test-id-001");
        testDutyRoster.setTitle("测试值班表");
        testDutyRoster.setOrgId("org-001");
        testDutyRoster.setOrgName("测试组织");
        testDutyRoster.setCreateBy("test-user");
        testDutyRoster.setCreateTime(LocalDateTime.now());
        testDutyRoster.setSort(1);

        testDutyRosterSchedule = new DutyRosterSchedule();
        testDutyRosterSchedule.setId("schedule-id-001");
        testDutyRosterSchedule.setRosterId("test-id-001");
        testDutyRosterSchedule.setScheduleTime(LocalDate.now());
    }

    @Test
    void testQueryPageList() {
        // 准备测试数据
        MicrovideoUserVO mockUser = new MicrovideoUserVO();
        mockUser.setGroupId("group-001");
        when(userInfoUtil.getHttpSessionUser()).thenReturn(mockUser);

        Page<DutyRosterVO> mockPage = new Page<>(1, 10);
        when(dutyRosterMapper.queryPageListByMonth(any(Page.class), anyString(), anyString())).thenReturn(mockPage);

        // 执行测试
        Result<IPage<DutyRosterVO>> result = dutyRosterController.queryPageList(
                "2025-01", 1, 10 );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(dutyRosterMapper, times(1)).queryPageListByMonth(any(Page.class), eq("2025-01"), eq("group-001"));
    }

    @Test
    void testAdd() {
        // 准备测试数据
        when(dutyRosterService.save(any(DutyRoster.class))).thenReturn(true);

        // 执行测试
        Result<DutyRoster> result = dutyRosterController.add(testDutyRoster);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("添加成功！", result.getMessage());
        verify(dutyRosterService, times(1)).save(testDutyRoster);
    }

    @Test
    void testEdit() {
        // 准备测试数据
        when(dutyRosterService.updateById(any(DutyRoster.class))).thenReturn(true);

        // 执行测试
        Result<DutyRoster> result = dutyRosterController.edit(testDutyRoster);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("编辑成功！", result.getMessage());
        verify(dutyRosterService, times(1)).updateById(testDutyRoster);
    }

    @Test
    void testDelete() {
        // 准备测试数据
        when(dutyRosterService.removeById(anyString())).thenReturn(true);

        // 执行测试
        Result<?> result = dutyRosterController.delete("test-id-001");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("删除成功！", result.getMessage());
        verify(dutyRosterService, times(1)).removeById("test-id-001");
    }

    @Test
    void testQueryById() {
        // 准备测试数据
        when(dutyRosterService.getById(anyString())).thenReturn(testDutyRoster);

        // 执行测试
        Result<DutyRoster> result = dutyRosterController.queryById("test-id-001");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(testDutyRoster, result.getResult());
        verify(dutyRosterService, times(1)).getById("test-id-001");
    }

    @Test
    void testQueryByIdNotFound() {
        // 准备测试数据
        when(dutyRosterService.getById(anyString())).thenReturn(null);

        // 执行测试
        Result<DutyRoster> result = dutyRosterController.queryById("non-existent-id");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到对应实体", result.getMessage());
        verify(dutyRosterService, times(1)).getById("non-existent-id");
    }

    @Test
    void testAddSchedule() {
        // 准备测试数据
        when(dutyRosterScheduleService.save(any(DutyRosterSchedule.class))).thenReturn(true);

        // 执行测试
        Result<DutyRosterSchedule> result = dutyRosterController.addSchedule(testDutyRosterSchedule);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("添加成功！", result.getMessage());
        verify(dutyRosterScheduleService, times(1)).save(testDutyRosterSchedule);
    }

    @Test
    void testQueryScheduleByRosterId() {
        // 准备测试数据
        List<DutyRosterSchedule> scheduleList = Arrays.asList(testDutyRosterSchedule);
        when(dutyRosterScheduleService.list(any())).thenReturn(scheduleList);

        // 执行测试
        Result<List<DutyRosterSchedule>> result = dutyRosterController.queryScheduleByRosterId("test-id-001");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(scheduleList, result.getResult());
        verify(dutyRosterScheduleService, times(1)).list(any());
    }
}
