package cn.microvideo.demo.test.dict;

import cn.microvideo.plyh.portal.application.MicrovideoGzReportApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description
 * @com 感动科技
 * @date 2021/11/23 14:03
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MicrovideoGzReportApplication.class)
@ActiveProfiles("test")
public class App1Test {
    @Test
    public void ftpTest() {
        try {
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
        }
    }
}
