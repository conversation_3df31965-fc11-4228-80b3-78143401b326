package cn.microvideo.plyh.portal.application.business.controller;

import cn.microvideo.module.plyh.core.entity.HolidayDeptUser;
import cn.microvideo.module.plyh.core.service.IHolidayDeptUserService;
import cn.microvideo.module.plyh.core.vo.AddHolidayUserVO;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.log.annotation.PlyhLog;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 节假日、部门关系管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "节假日、部门关系管理表")
@RestController
@RequestMapping("/holidaydeptuser")
public class HolidayDeptUserController {
    @Resource
    private IHolidayDeptUserService holidayDeptUserService;

    /**
     * 分页列表查询
     *
     * @param holidayDeptUser 实体对象
     * @param pageNo          页数
     * @param pageSize        页数大小
     * @param req             请求参数
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、部门关系管理表-分页列表查询", notes = "节假日、部门关系管理表-分页列表查询")
    @GetMapping(value = "/list")
    @PlyhLog(action = "节假日、部门关系管理表-分页列表查询")
    public Result<IPage<HolidayDeptUser>> queryPageList(HolidayDeptUser holidayDeptUser,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                        HttpServletRequest req) {
        Result<IPage<HolidayDeptUser>> result = new Result<IPage<HolidayDeptUser>>();
        QueryWrapper<HolidayDeptUser> queryWrapper = new QueryWrapper<HolidayDeptUser>();
        Page<HolidayDeptUser> page = new Page<HolidayDeptUser>(pageNo, pageSize);
        IPage<HolidayDeptUser> pageList = holidayDeptUserService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     *
     * @param holidayDeptUser 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、部门关系管理表-添加", notes = "节假日、部门关系管理表-添加")
    @PostMapping(value = "/add")
    @PlyhLog(action = "节假日、部门关系管理表-添加")
    public Result<HolidayDeptUser> add(@RequestBody HolidayDeptUser holidayDeptUser) {
        Result<HolidayDeptUser> result = new Result<HolidayDeptUser>();
        try {
            holidayDeptUserService.save(holidayDeptUser);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 添加
     *
     * @param holidayDeptUser 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、部门关系管理表-批量新增", notes = "节假日、部门关系管理表-批量新增")
    @PostMapping(value = "/addBatch")
    @PlyhLog(action = "节假日、部门关系管理表-批量新增")
    public Result<HolidayDeptUser> addBatch(@RequestBody List<AddHolidayUserVO> holidayDeptUser) {
        Result<HolidayDeptUser> result = new Result<HolidayDeptUser>();
        try {
            holidayDeptUserService.saveByBatchEntity(holidayDeptUser);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500(e.getMessage());
        }
        return result;
    }

    /**
     * 编辑
     *
     * @param holidayDeptUser 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、部门关系管理表-编辑", notes = "节假日、部门关系管理表-编辑")
    @PostMapping(value = "/edit")
    @PlyhLog(action = "节假日、部门关系管理表-编辑")
    public Result<HolidayDeptUser> edit(@RequestBody HolidayDeptUser holidayDeptUser) {
        Result<HolidayDeptUser> result = new Result<HolidayDeptUser>();
        HolidayDeptUser holidayDeptUserEntity = holidayDeptUserService.getById(holidayDeptUser.getId());
        if (holidayDeptUserEntity == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = holidayDeptUserService.updateById(holidayDeptUser);
            if (ok) {
                result.success("修改成功!");
            }
        }
        return result;
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、部门关系管理表-通过id删除", notes = "节假日、部门关系管理表-通过id删除")
    @GetMapping(value = "/delete")
    @PlyhLog(action = "节假日、部门关系管理表-通过id删除")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            holidayDeptUserService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 主键Ids
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、部门关系管理表-批量删除", notes = "节假日、部门关系管理表-批量删除")
    @GetMapping(value = "/deleteBatch")
    @PlyhLog(action = "节假日、部门关系管理表-批量删除")
    public Result<HolidayDeptUser> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<HolidayDeptUser> result = new Result<HolidayDeptUser>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.holidayDeptUserService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、部门关系管理表-通过id查询", notes = "节假日、部门关系管理表-通过id查询")
    @GetMapping(value = "/queryById")
    @PlyhLog(action = "节假日、部门关系管理表-通过id查询")
    public Result<HolidayDeptUser> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<HolidayDeptUser> result = new Result<HolidayDeptUser>();
        HolidayDeptUser holidayDeptUser = holidayDeptUserService.getById(id);
        if (holidayDeptUser == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(holidayDeptUser);
            result.setSuccess(true);
        }
        return result;
    }


}
