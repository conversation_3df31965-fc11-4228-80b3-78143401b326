package cn.microvideo.plyh.portal.application.filter;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.framework.core.basic.session.util.MicrovideoSessionUtil;
import cn.microvideo.framework.support.qs.util.MicrovideoQsUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.util.CasProperties;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.util.YcgzUtil;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.module.plyh.exception.BizException;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.util.JwtUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.unicon.cas.client.configuration.CasClientConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
@Slf4j
public class AuthorityInterceptor implements HandlerInterceptor {

    @Resource
    private CasProperties casProperties;

    @Resource
    private CasClientConfigurationProperties casClientConfiguration;

    @Resource
    private UserInfoUtil userInfoUtil;

    //方法执行前执行的接口
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("进入到拦截器中:preHandle(),拦截URL：" + request.getServletPath());
        // 获取请求头中手机号
        String token = request.getHeader("token");
        if (CharSequenceUtil.isBlank(token)) {
            ajaxHttpToLogin(response);
            return false;
        }
        boolean isOk = JwtUtil.verifyToken(token);
        if (!isOk) {
            ajaxHttpToLogin(response);
            return false;
        }else{
            //校验人员信息
            MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
            if(ObjectUtil.isEmpty(userVO)){
                ajaxHttpToLogin(response);
                return false;
            }
        }
        return true;
    }



    /**
     * ajax请求标记
     *
     * @param
     * @param response
     */
    private void ajaxHttpToLogin(HttpServletResponse response) {
        try {
            //这里响应状态码为自定义，前端根据状态去处理
            response.setStatus(CommonConstant.OK_200);//403 禁止
            response.setContentType("text/html;charset=utf-8");
            Result result = new Result();
            result.setCode(CommonConstant.NO_LOGIN);
            result.setMessage("登录超时!");
            result.setResult(casClientConfiguration.getServerLoginUrl().concat("?service=").concat(YcgzUtil.encodeUTF8(casProperties.getBaseServerUrl())));
            response.getWriter().print(JSONObject.toJSONString(result));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //方法执行后执行的方法
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        //log.info("进入到拦截器中:postHandle(),拦截URL："+request.getServletPath());
    }


    //返回处理的方法
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        //log.info("进入到拦截器中:afterCompletion(),拦截URL："+request.getServletPath());

    }
}
