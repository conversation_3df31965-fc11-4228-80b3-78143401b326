package cn.microvideo.plyh.portal.application.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * 通过返回新的CorsFilter解决跨域问题【全局】
 */
@Configuration
public class CrosConfig {

    @Bean
    public CorsFilter getCorsFilter() {
        // 1、配置CROS信息
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedOrigin("*"); // 放行哪些请求源
        corsConfiguration.addAllowedMethod("*"); // 放行哪些请求方式（如： GET、POST...）
        corsConfiguration.addAllowedHeader("*"); // 放行哪些请求头
        corsConfiguration.setMaxAge(3600L); // 跨域请求最大有效时长

        // 2、对接口配置跨域设置
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfiguration);

        // 3、返回新的CorsFilter
        return new CorsFilter(source);
    }
}
