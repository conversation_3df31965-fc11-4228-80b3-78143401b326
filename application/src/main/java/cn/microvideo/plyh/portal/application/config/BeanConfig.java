package cn.microvideo.plyh.portal.application.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * @description: Bean注入配置类
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2023/2/20 16:15
 */
@Configuration
public class BeanConfig {

    /**
     * <p>主库数据源</p>
     *
     * @return 主库数据源
     */
    @Primary
    @Bean(name = "masterDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.master")
    public DataSource masterDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * <p>从库数据源</p>
     *
     * @return 从库数据源
     */
    @Bean(name = "slaveDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.slave1")
    public DataSource slaveDataSource() {
        return DataSourceBuilder.create().build();
    }


}

