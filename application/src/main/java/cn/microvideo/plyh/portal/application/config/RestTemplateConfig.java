package cn.microvideo.plyh.portal.application.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * rest配置类
 */
@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        //链接请求时间
        httpRequestFactory.setConnectionRequestTimeout(20000);
        //连接超时时间
        httpRequestFactory.setConnectTimeout(4000);
        //读取超时时间,避免接口无响应
        httpRequestFactory.setReadTimeout(15000);
        //封装配置
        RestTemplate restTemplate = new RestTemplate(httpRequestFactory);
        return restTemplate;
    }

}
