package cn.microvideo.plyh.portal.application.filter;

import cn.hutool.core.text.CharSequenceUtil;
import cn.microvideo.module.plyh.core.util.CasProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 拦截器需要配置才可以使用，也可以在这里注入其他的类
 */

@Configuration
public class InterceptorAdapterConfig implements WebMvcConfigurer {

    @Resource
    private AuthorityInterceptor authorityInterceptor;
    @Resource
    private CasProperties casProperties;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        List<String> patterns = new ArrayList<>();
        //放行配置
        if (CharSequenceUtil.isNotBlank(casProperties.getInterceptorsIgnoreUrl())) {
            patterns = Arrays.asList(casProperties.getInterceptorsIgnoreUrl().split("\\|"));
        }
        registry.addInterceptor(authorityInterceptor)
                .addPathPatterns("/**") //所有的请求都要拦截。
                .excludePathPatterns(patterns); //将不需要拦截的接口请求排除在外
    }


    /**
     * 放行跨域请求
     *
     * @param registry 注册器
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry
                // 拦截所有的请求
                .addMapping("/**")
                .allowCredentials(true)
                // 允许跨域的方法，可以单独配置
                .allowedMethods("*")
                // 允许跨域的请求头，可以单独配置
                .allowedHeaders("*");
    }

}
