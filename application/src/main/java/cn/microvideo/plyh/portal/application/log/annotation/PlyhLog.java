package cn.microvideo.plyh.portal.application.log.annotation;

import cn.microvideo.jchc.common.logger.ETerminal;

import java.lang.annotation.*;


/**
 * 日志注解
 * <AUTHOR>
 *
 */
@Target({ ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PlyhLog {
	/**
	 * 应用
	 * @return
	 */
	public String appCode() default "协同办公";
	/**
	 * 模块
	 * @return
	 */
	public String module() default "排班管理";
	/**
	 * 动作
	 * @return
	 */
	public String action() default "";
	/**
	 * 终端
	 * @return
	 */
	ETerminal terminal() default ETerminal.PC;
}
