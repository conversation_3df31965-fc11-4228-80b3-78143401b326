package cn.microvideo.plyh.portal.application.business.controller;

import cn.microvideo.module.plyh.core.entity.DutyRoster;
import cn.microvideo.module.plyh.core.entity.DutyRosterSchedule;
import cn.microvideo.module.plyh.core.service.IDutyRosterService;
import cn.microvideo.module.plyh.core.service.IDutyRosterScheduleService;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.vo.DutyRosterVO;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.log.annotation.PlyhLog;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Api(tags = "值班表管理")
@RestController
@RequestMapping("/dutyRoster")
public class DutyRosterController {

    @Resource
    private IDutyRosterService dutyRosterService;

    @Resource
    private IDutyRosterScheduleService dutyRosterScheduleService;

    @Resource
    private UserInfoUtil userInfoUtil;

    /**
     * 分页列表查询 - 按月份查询当前用户单位的值班表
     *
     * @param month    查询月份 (格式: yyyy-MM)
     * @param pageNo   页数
     * @param pageSize 页数大小
     * @param req      请求参数
     * @return 返回对象
     */
    @ApiOperation(value = "值班表-分页列表查询", notes = "值班表-按月份分页列表查询")
    @GetMapping(value = "/list")
    @PlyhLog(action = "值班表-分页列表查询")
    public Result<IPage<DutyRosterVO>> queryPageList(@RequestParam(name = "month", required = true) String month,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        Result<IPage<DutyRosterVO>> result = new Result<IPage<DutyRosterVO>>();
        try {
            // 获取当前登录用户信息
            MicrovideoUserVO currentUser = userInfoUtil.getHttpSessionUser();
            if (currentUser == null) {
                result.error500("未找到当前用户信息");
                return result;
            }

            // 解析月份参数
            String[] monthParts = month.split("-");
            if (monthParts.length != 2) {
                result.error500("月份格式错误，请使用 yyyy-MM 格式");
                return result;
            }

            int year = Integer.parseInt(monthParts[0]);
            int monthValue = Integer.parseInt(monthParts[1]);

            // 构建查询条件 - 根据当前用户的单位ID查询
            LambdaQueryWrapper<DutyRoster> queryWrapper = new LambdaQueryWrapper<DutyRoster>();
            queryWrapper.eq(DutyRoster::getOrgId, currentUser.getGroupId());
            queryWrapper.orderByDesc(DutyRoster::getCreateTime);

            // 分页查询值班表
            Page<DutyRoster> page = new Page<DutyRoster>(pageNo, pageSize);
            IPage<DutyRoster> dutyRosterPage = dutyRosterService.page(page, queryWrapper);

            // 组装返回数据
            List<DutyRosterVO> dutyRosterVOList = new ArrayList<>();
            for (DutyRoster dutyRoster : dutyRosterPage.getRecords()) {
                DutyRosterVO dutyRosterVO = new DutyRosterVO();
                dutyRosterVO.setId(dutyRoster.getId());
                dutyRosterVO.setTitle(dutyRoster.getTitle());
                dutyRosterVO.setOrgId(dutyRoster.getOrgId());
                dutyRosterVO.setOrgName(dutyRoster.getOrgName());
                dutyRosterVO.setSort(dutyRoster.getSort());

                // 查询该值班表在指定月份的排班时间
                LambdaQueryWrapper<DutyRosterSchedule> scheduleWrapper = new LambdaQueryWrapper<DutyRosterSchedule>();
                scheduleWrapper.eq(DutyRosterSchedule::getRosterId, dutyRoster.getId());

                // 添加月份过滤条件
                LocalDate monthStart = LocalDate.of(year, monthValue, 1);
                LocalDate monthEnd = monthStart.withDayOfMonth(monthStart.lengthOfMonth());
                scheduleWrapper.ge(DutyRosterSchedule::getScheduleTime, monthStart);
                scheduleWrapper.le(DutyRosterSchedule::getScheduleTime, monthEnd);
                scheduleWrapper.orderByAsc(DutyRosterSchedule::getScheduleTime);

                List<DutyRosterSchedule> schedules = dutyRosterScheduleService.list(scheduleWrapper);
                List<LocalDate> scheduleTimes = schedules.stream()
                        .map(DutyRosterSchedule::getScheduleTime)
                        .collect(Collectors.toList());
                dutyRosterVO.setScheduleTimes(scheduleTimes);

                dutyRosterVOList.add(dutyRosterVO);
            }

            // 构建分页结果
            Page<DutyRosterVO> resultPage = new Page<>(pageNo, pageSize);
            resultPage.setRecords(dutyRosterVOList);
            resultPage.setTotal(dutyRosterPage.getTotal());
            resultPage.setSize(dutyRosterPage.getSize());
            resultPage.setCurrent(dutyRosterPage.getCurrent());
            resultPage.setPages(dutyRosterPage.getPages());

            result.setSuccess(true);
            result.setResult(resultPage);
        } catch (Exception e) {
            log.error("查询失败", e);
            result.error500("查询失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 添加
     *
     * @param dutyRoster 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "值班表-添加", notes = "值班表-添加")
    @PostMapping(value = "/add")
    @PlyhLog(action = "值班表-添加")
    public Result<DutyRoster> add(@RequestBody DutyRoster dutyRoster) {
        Result<DutyRoster> result = new Result<DutyRoster>();
        try {
            dutyRosterService.save(dutyRoster);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑
     *
     * @param dutyRoster 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "值班表-编辑", notes = "值班表-编辑")
    @PostMapping(value = "/edit")
    @PlyhLog(action = "值班表-编辑")
    public Result<DutyRoster> edit(@RequestBody DutyRoster dutyRoster) {
        Result<DutyRoster> result = new Result<DutyRoster>();
        try {
            dutyRosterService.updateById(dutyRoster);
            result.success("编辑成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "值班表-通过id删除", notes = "值班表-通过id删除")
    @GetMapping(value = "/delete")
    @PlyhLog(action = "值班表-通过id删除")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        Result<?> result = new Result<>();
        try {
            dutyRosterService.removeById(id);
            result.success("删除成功！");
        } catch (Exception e) {
            log.error("删除失败 :{}", e.getMessage());
            result.error500(e.getMessage());
        }
        return result;
    }

    /**
     * 批量删除
     *
     * @param ids 主键集合
     * @return 返回对象
     */
    @ApiOperation(value = "值班表-批量删除", notes = "值班表-批量删除")
    @GetMapping(value = "/deleteBatch")
    @PlyhLog(action = "值班表-批量删除")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<?> result = new Result<>();
        try {
            dutyRosterService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("批量删除成功！");
        } catch (Exception e) {
            log.error("批量删除失败 :{} ", e.getMessage());
            result.error500(e.getMessage());
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "值班表-通过id查询", notes = "值班表-通过id查询")
    @GetMapping(value = "/queryById")
    @PlyhLog(action = "值班表-通过id查询")
    public Result<DutyRoster> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<DutyRoster> result = new Result<DutyRoster>();
        DutyRoster dutyRoster = dutyRosterService.getById(id);
        if (dutyRoster == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(dutyRoster);
            result.setSuccess(true);
        }
        return result;
    }

    // ==================== DutyRosterSchedule 相关方法 ====================

    /**
     * 排班时间-分页列表查询
     *
     * @param dutyRosterSchedule 实体对象
     * @param pageNo             页数
     * @param pageSize           页数大小
     * @param req                请求参数
     * @return 返回对象
     */
    @ApiOperation(value = "排班时间-分页列表查询", notes = "排班时间-分页列表查询")
    @GetMapping(value = "/schedule/list")
    @PlyhLog(action = "排班时间-分页列表查询")
    public Result<IPage<DutyRosterSchedule>> querySchedulePageList(DutyRosterSchedule dutyRosterSchedule,
                                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                   HttpServletRequest req) {
        Result<IPage<DutyRosterSchedule>> result = new Result<IPage<DutyRosterSchedule>>();
        LambdaQueryWrapper<DutyRosterSchedule> queryWrapper = new LambdaQueryWrapper<DutyRosterSchedule>();

        // 添加查询条件
        if (dutyRosterSchedule.getRosterId() != null && !dutyRosterSchedule.getRosterId().trim().isEmpty()) {
            queryWrapper.eq(DutyRosterSchedule::getRosterId, dutyRosterSchedule.getRosterId());
        }
        if (dutyRosterSchedule.getScheduleTime() != null) {
            queryWrapper.eq(DutyRosterSchedule::getScheduleTime, dutyRosterSchedule.getScheduleTime());
        }

        // 按排班时间排序
        queryWrapper.orderByAsc(DutyRosterSchedule::getScheduleTime);

        Page<DutyRosterSchedule> page = new Page<DutyRosterSchedule>(pageNo, pageSize);
        IPage<DutyRosterSchedule> pageList = dutyRosterScheduleService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 排班时间-添加
     *
     * @param dutyRosterSchedule 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "排班时间-添加", notes = "排班时间-添加")
    @PostMapping(value = "/schedule/add")
    @PlyhLog(action = "排班时间-添加")
    public Result<DutyRosterSchedule> addSchedule(@RequestBody DutyRosterSchedule dutyRosterSchedule) {
        Result<DutyRosterSchedule> result = new Result<DutyRosterSchedule>();
        try {
            dutyRosterScheduleService.save(dutyRosterSchedule);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 排班时间-编辑
     *
     * @param dutyRosterSchedule 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "排班时间-编辑", notes = "排班时间-编辑")
    @PostMapping(value = "/schedule/edit")
    @PlyhLog(action = "排班时间-编辑")
    public Result<DutyRosterSchedule> editSchedule(@RequestBody DutyRosterSchedule dutyRosterSchedule) {
        Result<DutyRosterSchedule> result = new Result<DutyRosterSchedule>();
        try {
            dutyRosterScheduleService.updateById(dutyRosterSchedule);
            result.success("编辑成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 排班时间-通过id删除
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "排班时间-通过id删除", notes = "排班时间-通过id删除")
    @GetMapping(value = "/schedule/delete")
    @PlyhLog(action = "排班时间-通过id删除")
    public Result<?> deleteSchedule(@RequestParam(name = "id", required = true) String id) {
        Result<?> result = new Result<>();
        try {
            dutyRosterScheduleService.removeById(id);
            result.success("删除成功！");
        } catch (Exception e) {
            log.error("删除失败: {}", e.getMessage());
            result.error500(e.getMessage());
        }
        return result;
    }

    /**
     * 排班时间-批量删除
     *
     * @param ids 主键集合
     * @return 返回对象
     */
    @ApiOperation(value = "排班时间-批量删除", notes = "排班时间-批量删除")
    @GetMapping(value = "/schedule/deleteBatch")
    @PlyhLog(action = "排班时间-批量删除")
    public Result<?> deleteScheduleBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<?> result = new Result<>();
        try {
            dutyRosterScheduleService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("批量删除成功！");
        } catch (Exception e) {
            log.error("批量删除失败: {}", e.getMessage());
            result.error500(e.getMessage());
        }
        return result;
    }

    /**
     * 排班时间-通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "排班时间-通过id查询", notes = "排班时间-通过id查询")
    @GetMapping(value = "/schedule/queryById")
    @PlyhLog(action = "排班时间-通过id查询")
    public Result<DutyRosterSchedule> queryScheduleById(@RequestParam(name = "id", required = true) String id) {
        Result<DutyRosterSchedule> result = new Result<DutyRosterSchedule>();
        DutyRosterSchedule dutyRosterSchedule = dutyRosterScheduleService.getById(id);
        if (dutyRosterSchedule == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(dutyRosterSchedule);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 排班时间-根据值班表ID查询列表
     *
     * @param rosterId 值班表ID
     * @return 返回对象
     */
    @ApiOperation(value = "排班时间-根据值班表ID查询列表", notes = "排班时间-根据值班表ID查询列表")
    @GetMapping(value = "/schedule/queryByRosterId")
    @PlyhLog(action = "排班时间-根据值班表ID查询列表")
    public Result<List<DutyRosterSchedule>> queryScheduleByRosterId(@RequestParam(name = "rosterId", required = true) String rosterId) {
        Result<List<DutyRosterSchedule>> result = new Result<List<DutyRosterSchedule>>();
        try {
            LambdaQueryWrapper<DutyRosterSchedule> queryWrapper = new LambdaQueryWrapper<DutyRosterSchedule>();
            queryWrapper.eq(DutyRosterSchedule::getRosterId, rosterId);
            queryWrapper.orderByAsc(DutyRosterSchedule::getScheduleTime);
            List<DutyRosterSchedule> scheduleList = dutyRosterScheduleService.list(queryWrapper);
            result.setResult(scheduleList);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error("查询失败 : {}", e.getMessage());
            result.error500("查询失败");
        }
        return result;
    }
}