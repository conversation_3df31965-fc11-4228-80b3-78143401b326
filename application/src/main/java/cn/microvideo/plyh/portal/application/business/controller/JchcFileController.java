package cn.microvideo.plyh.portal.application.business.controller;

import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.entity.JchcFile;
import cn.microvideo.module.plyh.core.service.IJchcFileService;
import cn.microvideo.module.plyh.core.util.CasProperties;
import cn.microvideo.module.plyh.exception.BizException;
import cn.microvideo.module.plyh.exception.Result;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;

/**
 * @Description: 文件管理表
 * @Author: spring-boot
 * @Date: 2023-08-31
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "文件管理表")
@RestController
@RequestMapping("/jchcfile")
public class JchcFileController {
    @Resource
    private IJchcFileService jchcFileService;

    /**
     * 分页列表查询
     *
     * @param jchcFile 实体对象
     * @param pageNo   页数
     * @param pageSize 页数大小
     * @param req      请求参数
     * @return 返回对象
     */
    @ApiOperation(value = "文件管理表-分页列表查询", notes = "文件管理表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<JchcFile>> queryPageList(JchcFile jchcFile,
                                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                 HttpServletRequest req) {
        Result<IPage<JchcFile>> result = new Result<IPage<JchcFile>>();
        QueryWrapper<JchcFile> queryWrapper = new QueryWrapper<JchcFile>();
        Page<JchcFile> page = new Page<JchcFile>(pageNo, pageSize);
        IPage<JchcFile> pageList = jchcFileService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     *
     * @param jchcFile 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "文件管理表-添加", notes = "文件管理表-添加")
    @PostMapping(value = "/add")
    public Result<JchcFile> add(@RequestBody JchcFile jchcFile) {
        Result<JchcFile> result = new Result<JchcFile>();
        try {
            jchcFileService.saveByEntity(jchcFile);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑
     *
     * @param jchcFile 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "文件管理表-编辑", notes = "文件管理表-编辑")
    @PostMapping(value = "/edit")
    public Result<JchcFile> edit(@RequestBody JchcFile jchcFile) {
        Result<JchcFile> result = new Result<JchcFile>();
        JchcFile jchcFileEntity = jchcFileService.getById(jchcFile.getId());
        if (jchcFileEntity == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = jchcFileService.updateById(jchcFile);
            if (ok) {
                result.success("修改成功!");
            }
        }

        return result;
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "文件管理表-通过id删除", notes = "文件管理表-通过id删除")
    @GetMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            jchcFileService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 主键Ids
     * @return 返回对象
     */
    @ApiOperation(value = "文件管理表-批量删除", notes = "文件管理表-批量删除")
    @GetMapping(value = "/deleteBatch")
    public Result<JchcFile> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<JchcFile> result = new Result<JchcFile>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.jchcFileService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "文件管理表-通过id查询", notes = "文件管理表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<JchcFile> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<JchcFile> result = new Result<JchcFile>();
        JchcFile jchcFile = jchcFileService.getById(id);
        if (jchcFile == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(jchcFile);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 文件上传
     *
     * @param origin 文件
     * @return 结果
     * @throws IOException 异常
     */

    @ApiOperation(value = "文件管理表-文件上传", notes = "文件管理表-文件上传")
    @ResponseBody
    @PostMapping("/upload")
    public Result<JchcFile> upload(@RequestParam("fileContent") MultipartFile origin, JchcFile file) {
        Result<JchcFile> result = new Result<JchcFile>();
        try {
            result.setResult(jchcFileService.uploadDocView(origin, file));
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }

    /**
     * 文件上传
     *
     * @param origin 文件
     * @return 结果
     * @throws IOException 异常
     */

    @ApiOperation(value = "文件管理表-文件上传", notes = "文件管理表-文件上传")
    @ResponseBody
    @PostMapping("/uploadFile")
    public Result<JchcFile> uploadFile(@RequestParam("fileContent") MultipartFile origin, JchcFile file) {
        Result<JchcFile> result = new Result<JchcFile>();
        try {
            result.setResult(jchcFileService.uploadFile(origin, file));
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }

    /**
     * 根据附件ID下载附件
     *
     * @param id
     * @param response
     * @throws IOException
     */
    @ApiOperation(value = "文件管理表-本地文件下载", notes = "文件管理表-本地文件下载")
    @ResponseBody
    @GetMapping("/download/{id}")
    public void download(@PathVariable("id") String id, HttpServletResponse response) {
        jchcFileService.download(id, response);
    }


    /**
     * 根据附件ID下载附件
     *
     * @param fileCode
     * @param response
     * @throws IOException
     */
    @ApiOperation(value = "文件管理表-根据code下载文件", notes = "文件管理表-根据code下载文件")
    @ResponseBody
    @GetMapping("/downloadByCode/{fileCode}")
    public void downloadByCode(@PathVariable("fileCode") String fileCode, HttpServletResponse response) {
        jchcFileService.downloadByCode(fileCode, response);
    }


}
