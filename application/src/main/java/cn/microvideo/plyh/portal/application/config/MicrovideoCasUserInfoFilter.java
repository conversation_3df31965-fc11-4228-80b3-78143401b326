//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.microvideo.plyh.portal.application.config;

import cn.microvideo.framework.core.basic.session.util.MicrovideoSessionUtil;
import cn.microvideo.framework.core.util.string.MicrovideoStringUtil;
import cn.microvideo.framework.support.qs.util.MicrovideoQsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;

public class MicrovideoCasUserInfoFilter implements Filter {
    private static final Logger log = LoggerFactory.getLogger(MicrovideoCasUserInfoFilter.class);
    private String sessionKey;

    public MicrovideoCasUserInfoFilter(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public void init(FilterConfig filterConfig) throws ServletException {
    }

    public void destroy() {
    }

    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request_ = (HttpServletRequest) request;
        String uid = cn.microvideo.plyh.portal.application.config.MicrovideoCasUtil.getAccountNameFromCas(request_);
        if (MicrovideoStringUtil.isNotBlank(uid)) {
            this.setSessionUser(uid);
        }

        chain.doFilter(request, response);
    }

    private void setSessionUser(String account) {
        log.info(account);
        //MicrovideoSessionUtil.setHttpSessionUser(session, MicrovideoQsUtil.getUserByAccount(account, null), this.sessionKey);
    }
}
