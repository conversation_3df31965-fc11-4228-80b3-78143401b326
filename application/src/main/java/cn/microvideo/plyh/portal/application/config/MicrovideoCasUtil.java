//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.microvideo.plyh.portal.application.config;

import org.jasig.cas.client.authentication.AttributePrincipal;
import org.jasig.cas.client.validation.Assertion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;

public class MicrovideoCasUtil {
    private static final Logger log = LoggerFactory.getLogger(MicrovideoCasUtil.class);

    public MicrovideoCasUtil() {
    }

    public static String getAccountNameFromCas(HttpServletRequest request) {
        Assertion assertion = (Assertion)request.getSession().getAttribute("_const_cas_assertion_");
        if (assertion != null) {
            AttributePrincipal principal = assertion.getPrincipal();
            return principal.getName();
        } else {
            return null;
        }
    }
}
