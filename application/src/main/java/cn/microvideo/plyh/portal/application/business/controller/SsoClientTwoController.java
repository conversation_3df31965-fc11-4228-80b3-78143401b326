package cn.microvideo.plyh.portal.application.business.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.framework.support.qs.util.MicrovideoQsUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.util.CasProperties;
import cn.microvideo.module.plyh.core.util.RedisUtil;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.module.plyh.exception.BizException;
import cn.microvideo.plyh.portal.application.util.JwtUtil;
import net.unicon.cas.client.configuration.CasClientConfigurationProperties;
import org.jasig.cas.client.authentication.AttributePrincipal;
import org.jasig.cas.client.util.AbstractCasFilter;
import org.jasig.cas.client.validation.Assertion;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.concurrent.TimeUnit;

@Controller
public class SsoClientTwoController {
    @Resource
    private CasProperties casProperties;

    @Resource
    private CasClientConfigurationProperties casClientConfiguration;

    @Resource
    private RedisUtil redisUtil;


    @RequestMapping("/login")
    public String login(String url, HttpSession session, HttpServletResponse response, HttpServletRequest request) throws UnsupportedEncodingException {
        //如果未登录
        Assertion assertion = (Assertion) session.getAttribute(AbstractCasFilter.CONST_CAS_ASSERTION);
        if (null == assertion) {
            String redirectUrl = casClientConfiguration.getServerLoginUrl().concat("?service=").concat(URLEncoder.encode(casProperties.getBaseServerUrl(), "UTF-8"));
            return "redirect:" + redirectUrl;
        }
        //如果未获取到信息的话
        AttributePrincipal principal = assertion.getPrincipal();
        if (null == principal) {
            String redirectUrl = casClientConfiguration.getServerLoginUrl().concat("?service=").concat(URLEncoder.encode(casProperties.getBaseServerUrl(), "UTF-8"));
            return "redirect:" + redirectUrl;
        }
        String loginName = principal.getName();

        MicrovideoSessionUser user = MicrovideoQsUtil.getUserByAccount(loginName);
        //存储用户数据
        //获取token
        String token = JwtUtil.getToken(user.getName(), user.getAccount());
        if (CharSequenceUtil.isBlank(token)) {
            throw new BizException(CommonConstant.NO_LOGIN, "登录失败！");
        }
        MicrovideoUserVO microvideoUserVO = BeanUtil.copyProperties(user, MicrovideoUserVO.class);
        //数据存储时间8小时
        redisUtil.setEx(token, JSONUtil.toJsonStr(microvideoUserVO), 8, TimeUnit.HOURS);
        //文件预览的cookie
        setViewCookie(response, loginName);
        //返回token
        setTokenCookie(response, token);
        if (CharSequenceUtil.isNotBlank(url)) {
            return "redirect:" + url;
        } else {
            return "redirect:" + casProperties.getServerHtmlUrl();
        }
    }

    /**
     * 文件预览
     */
    public void setViewCookie(HttpServletResponse response, String userId) {
        //将数据装进cookier返回给前端
        Cookie cookie1 = new Cookie("uid", userId);
        cookie1.setMaxAge(30000);
        cookie1.setPath("/");
        response.addCookie(cookie1);
    }

    /**
     * token 的返回
     */
    public void setTokenCookie(HttpServletResponse response, String token) {
        //将数据装进cookier返回给前端
        Cookie cookiet = new Cookie("plyh-token", token);
        cookiet.setMaxAge(28800);
        cookiet.setPath("/");
        response.addCookie(cookiet);
    }

    /**
     * 重新封装请求 redirect_uri
     *
     * @param url
     * @param param
     * @param key
     * @return
     */
    public String joinUrl(String url, String param, String key) {
        if (CharSequenceUtil.isNotBlank(url)) {
            if (url.contains("?")) {
                return url.concat("&".concat(key + "=").concat(param));
            } else {
                return url.concat("?".concat(key + "=").concat(param));
            }
        }
        return url;
    }
}