package cn.microvideo.plyh.portal.application.business.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.framework.support.qs.util.MicrovideoQsUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.service.WXService;
import cn.microvideo.module.plyh.core.util.RedisUtil;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.module.plyh.exception.BizException;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.util.JwtUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <b>描述:</b>
 *
 * <p>微信Controller</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2019年4月9日下午2:38:53
 * @since JDK1.8
 */
@RestController
@RequestMapping(WeixinController.REQUEST_MAPPING)
public class WeixinController {
    /**
     * -微信Controller路径
     */
    public static final String REQUEST_MAPPING = "/wechat";
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private WXService wxService;


    /**
     * 拦截所有请求
     */
    @RequestMapping("/**")
    public void doProxy(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        StringBuilder uri = new StringBuilder(request.getRequestURI());
        String find = request.getContextPath() + REQUEST_MAPPING;
        //唯独这个接口不过滤
        if (!uri.toString().contains("/wechat/login")) {
            String path = uri.toString().replaceFirst(find, "");
            request.getRequestDispatcher(path).forward(request, response);
        }
    }


    /**
     * 根据微信参数获取用户信息
     *
     * @param
     * @param request
     * @return
     */
    @RequestMapping(value = "/login")
    public Result<String> login(@RequestParam(name = "appCode", required = true) String appCode, @RequestParam(name = "appData", required = true) Integer appData, HttpServletResponse response, HttpServletRequest request) {
        Result<String> result = new Result<>();
        try {
            //通过企业微信获取用户的信息
            String mobile = wxService.selectByToken(appCode, appData);
//            String mobile = "***********";
            //文件预览的cookie
            setViewCookie(response, mobile);
            //如果
            MicrovideoSessionUser userVo = MicrovideoQsUtil.getUserByAccount(mobile);
            //复制数据
            MicrovideoUserVO microvideoUserVO = BeanUtil.copyProperties(userVo, MicrovideoUserVO.class);
            //生成token
            String token = JwtUtil.getToken(microvideoUserVO.getName(), microvideoUserVO.getAccount());
            //数据存储时间8小时
            redisUtil.setEx(token, JSONUtil.toJsonStr(microvideoUserVO), 9, TimeUnit.HOURS);
            result.setResult(token);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }

    /**
     *
     */
    public void setViewCookie(HttpServletResponse response, String userId) {
        //将数据装进cookier返回给前端
        Cookie cookie1 = new Cookie("uid", userId);
        cookie1.setMaxAge(30000);
        cookie1.setPath("/");
        response.addCookie(cookie1);
    }

}
