package cn.microvideo.plyh.portal.application.business.controller;

import cn.microvideo.module.plyh.core.entity.ReleaseDay;
import cn.microvideo.module.plyh.core.service.IReleaseDayService;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.log.annotation.PlyhLog;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * @Description: 发布记录日期关联表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "发布记录日期关联表")
@RestController
@RequestMapping("/releaseday")
public class ReleaseDayController {
    @Resource
    private IReleaseDayService releaseDayService;

    /**
     * 分页列表查询
     *
     * @param releaseDay 实体对象
     * @param pageNo     页数
     * @param pageSize   页数大小
     * @param req        请求参数
     * @return 返回对象
     */
    @ApiOperation(value = "发布记录日期关联表-分页列表查询", notes = "发布记录日期关联表-分页列表查询")
    @GetMapping(value = "/list")
    @PlyhLog(action="发布记录日期关联表-分页列表查询")
    public Result<IPage<ReleaseDay>> queryPageList(ReleaseDay releaseDay,
                                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                   HttpServletRequest req) {
        Result<IPage<ReleaseDay>> result = new Result<IPage<ReleaseDay>>();
        QueryWrapper<ReleaseDay> queryWrapper = new QueryWrapper<ReleaseDay>();
        Page<ReleaseDay> page = new Page<ReleaseDay>(pageNo, pageSize);
        IPage<ReleaseDay> pageList = releaseDayService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     *
     * @param releaseDay 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "发布记录日期关联表-添加", notes = "发布记录日期关联表-添加")
    @PostMapping(value = "/add")
    @PlyhLog(action="发布记录日期关联表-添加")
    public Result<ReleaseDay> add(@RequestBody ReleaseDay releaseDay) {
        Result<ReleaseDay> result = new Result<ReleaseDay>();
        try {
            releaseDayService.save(releaseDay);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑
     *
     * @param releaseDay 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "发布记录日期关联表-编辑", notes = "发布记录日期关联表-编辑")
    @PostMapping(value = "/edit")
    @PlyhLog(action="发布记录日期关联表-编辑")
    public Result<ReleaseDay> edit(@RequestBody ReleaseDay releaseDay) {
        Result<ReleaseDay> result = new Result<ReleaseDay>();
        ReleaseDay releaseDayEntity = releaseDayService.getById(releaseDay.getId());
        if (releaseDayEntity == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = releaseDayService.updateById(releaseDay);
            if (ok) {
                result.success("修改成功!");
            }
        }

        return result;
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "发布记录日期关联表-通过id删除", notes = "发布记录日期关联表-通过id删除")
    @GetMapping(value = "/delete")
    @PlyhLog(action="发布记录日期关联表-通过id删除")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            releaseDayService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 主键Ids
     * @return 返回对象
     */
    @ApiOperation(value = "发布记录日期关联表-批量删除", notes = "发布记录日期关联表-批量删除")
    @GetMapping(value = "/deleteBatch")
    @PlyhLog(action="发布记录日期关联表-批量删除")
    public Result<ReleaseDay> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<ReleaseDay> result = new Result<ReleaseDay>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.releaseDayService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "发布记录日期关联表-通过id查询", notes = "发布记录日期关联表-通过id查询")
    @GetMapping(value = "/queryById")
    @PlyhLog(action="发布记录日期关联表-通过id查询")
    public Result<ReleaseDay> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<ReleaseDay> result = new Result<ReleaseDay>();
        ReleaseDay releaseDay = releaseDayService.getById(id);
        if (releaseDay == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(releaseDay);
            result.setSuccess(true);
        }
        return result;
    }


}
