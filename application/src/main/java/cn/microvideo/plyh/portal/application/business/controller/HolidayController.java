package cn.microvideo.plyh.portal.application.business.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.commonconstant.Contant;
import cn.microvideo.module.plyh.core.entity.Holiday;
import cn.microvideo.module.plyh.core.service.IHolidayService;
import cn.microvideo.module.plyh.core.vo.AddBatchHolidayVO;
import cn.microvideo.module.plyh.core.vo.HolidayMonthVO;
import cn.microvideo.module.plyh.core.vo.HolidaySearchVO;
import cn.microvideo.module.plyh.core.vo.HolidayTodoDetailVO;
import cn.microvideo.module.plyh.exception.BizException;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.log.annotation.PlyhLog;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description: 节假日、休息、值班管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "节假日、休息、值班管理表")
@RestController
@RequestMapping("/holiday")
public class HolidayController {
    @Resource
    private IHolidayService holidayService;

    /**
     * 分页列表查询
     *
     * @param holiday  实体对象
     * @param pageNo   页数
     * @param pageSize 页数大小
     * @param req      请求参数
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-分页列表查询", notes = "节假日、休息、值班管理表-分页列表查询")
    @GetMapping(value = "/list")
    @PlyhLog(action = "节假日、休息、值班管理表-分页列表查询")
    public Result<IPage<Holiday>> queryPageList(Holiday holiday,
                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                HttpServletRequest req) {
        Result<IPage<Holiday>> result = new Result<IPage<Holiday>>();
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<Holiday>();
        Page<Holiday> page = new Page<Holiday>(pageNo, pageSize);
        IPage<Holiday> pageList = holidayService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 分页列表查询
     *
     * @param holiday 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-全量查询列表（根据开始时间和结束时间）", notes = "节假日、休息、值班管理表-全量查询列表（根据开始时间和结束时间）")
    @GetMapping(value = "/queryList")
    @PlyhLog(action = "节假日、休息、值班管理表-全量查询列表（根据开始时间和结束时间）")
    public Result<List<Holiday>> queryList(HolidaySearchVO holiday) {
        Result<List<Holiday>> result = new Result<List<Holiday>>();
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<Holiday>();
        formatField(holiday, queryWrapper);
        List<Holiday> pageList = holidayService.list(queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 分页列表查询
     *
     * @param month 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-根据月份查询数据列表", notes = "节假日、休息、值班管理表-全量查询列表（根据开始时间和结束时间）")
    @GetMapping(value = "/queryMonthList")
    @PlyhLog(action = "节假日、休息、值班管理表-根据月份查询数据列表")
    public Result<List<Holiday>> queryMonthList(String month) {
        Result<List<Holiday>> result = new Result<List<Holiday>>();
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<Holiday>();
        formatFieldMonth(month, queryWrapper);
        List<Holiday> pageList = holidayService.list(queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 分页列表查询
     *
     * @param year 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-全量查询列表（根据年份查询,有则根据字段，没有则获取当前年）", notes = "节假日、休息、值班管理表-全量查询列表（根据年份查询，有则根据字段，没有则获取当前年）")
    @GetMapping(value = "/queryListByYear")
    @PlyhLog(action = "节假日、休息、值班管理表-全量查询列表（根据年份查询，有则根据字段，没有则获取当前年）")
    public Result<List<Holiday>> queryListByYear(String year) {
        Result<List<Holiday>> result = new Result<List<Holiday>>();
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<Holiday>();
        formatFieldYearNotWeekend(year, queryWrapper);
        List<Holiday> pageList = holidayService.list(queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 初始化查询条件
     *
     * @param year
     * @param queryWrapper
     */
    private void formatFieldYearNotWeekend(String year, QueryWrapper<Holiday> queryWrapper) {
        //根据时间排序
        queryWrapper.orderByAsc("F_DT_HOLIDAY_DATA");
        //大于等于开始时间
        if (CharSequenceUtil.isNotBlank(year)) {
            queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y' )", year);
        } else {
            queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y' )", DateUtil.format(new Date(), "yyyy"));
        }
        //排除掉周六、日数据
        queryWrapper.notIn("F_INT_HOLIDAY_TYPE", Contant.DATE_TYPE_2);

    }

    /**
     * 初始化查询条件
     *
     * @param year
     * @param queryWrapper
     */
//    private void formatFieldYear(String year, QueryWrapper<Holiday> queryWrapper) {
//        //根据时间排序
//        queryWrapper.orderByAsc("F_DT_HOLIDAY_DATA");
//        //大于等于开始时间
//        if (CharSequenceUtil.isNotBlank(year)) {
//            queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y' )", year);
//        } else {
//            queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y' )", DateUtil.format(new Date(), "yyyy"));
//        }
//
//    }

    /**
     * 初始化查询条件
     *
     * @param month
     * @param queryWrapper
     */
    private void formatFieldMonth(String month, QueryWrapper<Holiday> queryWrapper) {
        //根据时间排序
        queryWrapper.orderByAsc("F_DT_HOLIDAY_DATA");
        //大于等于开始时间
        if (CharSequenceUtil.isNotBlank(month)) {
            queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m' )", month);
        } else {
            queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m' )", DateUtil.format(new Date(), "yyyy-MM"));
        }
        queryWrapper.notIn("F_INT_HOLIDAY_TYPE", Contant.DATE_TYPE_3);

    }


    /**
     * 分页列表查询
     *
     * @param month 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-根据月份查询数据", notes = "节假日、休息、值班管理表-根据月份查询数据")
    @GetMapping(value = "/queryByMonth")
    @PlyhLog(action = "节假日、休息、值班管理表-根据月份查询数据")
    public Result<HolidayMonthVO> queryByMonth(String month) {
        Result<HolidayMonthVO> result = new Result<HolidayMonthVO>();
        HolidayMonthVO pageList = holidayService.selectByMonth(month, Contant.DETAILS_DATA_TYPE);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 分页列表查询
     *
     * @param month 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-新增值班根据月份查询数据", notes = "节假日、休息、值班管理表-根据月份查询数据")
    @GetMapping(value = "/AddByMonthDetails")
    @PlyhLog(action = "节假日、休息、值班管理表-新增值班根据月份查询数据")
    public Result<HolidayMonthVO> AddByMonthDetails(String month) {
        Result<HolidayMonthVO> result = new Result<HolidayMonthVO>();
        HolidayMonthVO pageList = holidayService.selectByMonth(month, Contant.ADD_DATA_TYPE);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 分页列表查询
     *
     * @param id 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-根据holidayId查询当前详情", notes = "节假日、休息、值班管理表-根据holidayId查询当前详情")
    @GetMapping(value = "/queryByIdDetailUser")
    @PlyhLog(action = "节假日、休息、值班管理表-根据holidayId查询当前详情")
    public Result<HolidayTodoDetailVO> queryByIdDetailUser(@RequestParam(name = "id", required = true) String id) {
        Result<HolidayTodoDetailVO> result = new Result<HolidayTodoDetailVO>();
        result.setResult(holidayService.queryByIdDetailUser(id));
        result.setSuccess(true);
        return result;
    }


    /**
     * 初始化查询条件
     *
     * @param holiday
     * @param queryWrapper
     */
    private void formatField(HolidaySearchVO holiday, QueryWrapper<Holiday> queryWrapper) {
        //根据时间排序
        queryWrapper.orderByAsc("F_DT_HOLIDAY_DATA");
        //大于等于开始时间
        if (CharSequenceUtil.isNotBlank(holiday.getStartTime())) {
            queryWrapper.ge("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m-%d' )", holiday.getStartTime());
        }
        //小于等于结束时间
        if (CharSequenceUtil.isNotBlank(holiday.getEndTime())) {
            queryWrapper.le("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y-%m-%d' )", holiday.getEndTime());
        }
    }

    /**
     * 添加
     *
     * @param holiday 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-添加", notes = "节假日、休息、值班管理表-添加")
    @PostMapping(value = "/add")
    @PlyhLog(action = "节假日、休息、值班管理表-添加")
    public Result<Holiday> add(@RequestBody Holiday holiday) {
        Result<Holiday> result = new Result<Holiday>();
        try {
            holidayService.saveByEntity(holiday);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 添加
     *
     * @param addBatchHolidayVO 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-批量新增", notes = "节假日、休息、值班管理表-批量新增")
    @PostMapping(value = "/addBatchEntity")
    @PlyhLog(action = "节假日、休息、值班管理表-批量新增")
    public Result<Holiday> addBatchEntity(@RequestBody AddBatchHolidayVO addBatchHolidayVO) {
        Result<Holiday> result = new Result<Holiday>();
        try {
            holidayService.saveByBatchEntity(addBatchHolidayVO);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.setCode(CommonConstant.TEXT_ERROR_CODE);
            result.setMessage(e.getMessage());
        }
        return result;
    }

    /**
     * 编辑
     *
     * @param holiday 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-编辑", notes = "节假日、休息、值班管理表-编辑")
    @PostMapping(value = "/edit")
    @PlyhLog(action = "节假日、休息、值班管理表-编辑")
    public Result<Holiday> edit(@RequestBody Holiday holiday) {
        Result<Holiday> result = new Result<Holiday>();
        Holiday holidayEntity = holidayService.getById(holiday.getId());
        if (holidayEntity == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = holidayService.updateById(holiday);
            if (ok) {
                result.success("修改成功!");
            }
        }

        return result;
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-通过id删除", notes = "节假日、休息、值班管理表-通过id删除")
    @GetMapping(value = "/delete")
    @PlyhLog(action = "节假日、休息、值班管理表-通过id删除")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            holidayService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 主键Ids
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-批量删除", notes = "节假日、休息、值班管理表-批量删除")
    @GetMapping(value = "/deleteBatch")
    @PlyhLog(action = "节假日、休息、值班管理表-批量删除")
    public Result<Holiday> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<Holiday> result = new Result<Holiday>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.holidayService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-通过id查询", notes = "节假日、休息、值班管理表-通过id查询")
    @GetMapping(value = "/queryById")
    @PlyhLog(action = "节假日、休息、值班管理表-通过id查询")
    public Result<Holiday> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<Holiday> result = new Result<Holiday>();
        Holiday holiday = holidayService.getById(id);
        if (holiday == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(holiday);
            result.setSuccess(true);
        }
        return result;
    }


    /**
     * 上传文件
     *
     * @return
     * @throws Exception
     * @throws
     */
    @ApiOperation(value = "节假日、休息、值班管理表-值班排班排班列表导入", notes = "节假日、休息、值班管理表-值班排班排班列表导入")
    @ResponseBody
    @PostMapping("/inputDutyExcel")
    @PlyhLog(action = "节假日、休息、值班管理表-值班排班排班列表导入")
    public Result<String> inputDutyExcel(@RequestParam("fileContent") MultipartFile origin, String month) {
        Result<String> result = new Result<String>();
        try {
            //获取文件名称
            try (InputStream input = origin.getInputStream()) {
                holidayService.inputDutyExcel(input, month);
            }
            result.success("导入成功！");
        } catch (Exception e) {
            result.setCode(CommonConstant.TEXT_ERROR_CODE);
            result.setMessage(e.getMessage());
        }
        return result;
    }


    /**
     * 、
     * 组织树导出成Excel，并支持树的排序
     *
     * @param response
     * @throws IOException
     */
    @ApiOperation(value = "节假日、休息、值班管理表-值班排班排班列表导出", notes = "节假日、休息、值班管理表-值班排班排班列表导出")
    @ResponseBody
    @GetMapping("/exportDutyExcel")
    @PlyhLog(action = "节假日、休息、值班管理表-值班排班排班列表导出")
    public void exportDutyExcel(@RequestParam(name = "month", required = true) String month,
                                @RequestParam(name = "orgName", required = true) String orgName,
                                @RequestParam(name = "orgId", required = true) String orgId,
                                HttpServletResponse response) throws IOException {
        Workbook workbook = holidayService.exportDutyExcel(month, orgName, orgId);
        String fileName = getWorkBookName(month, orgName);
        try (OutputStream output = response.getOutputStream()) {
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ";" + "filename*=utf-8''" + fileName);
            workbook.write(output);
        }
    }

    /**
     * @param month
     * @return
     */
    public String getWorkBookName(String month, String orgName) {
        try {
            Date date = DateUtil.parse(month, "yyyy-MM");
            return String.format(Contant.WORKBOOK_TITLE_NAME, orgName, DateUtil.format(date, "yyyy"), DateUtil.format(date, "MM"));
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }


    /**
     * 分页列表查询
     *
     * @param month 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-考勤系统对接接口", notes = "节假日、休息、值班管理表-考勤系统对接接口")
    @GetMapping(value = "/api/getList")
    @PlyhLog(action = "节假日、休息、值班管理表-考勤系统对接接口")
    public Result<List<Holiday>> getList(@RequestParam(name = "month", required = true) String month) {
        Result<List<Holiday>> result = new Result<List<Holiday>>();
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<Holiday>();
        formatFieldMonth(month, queryWrapper);
        List<Holiday> pageList = holidayService.list(queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 分页列表查询
     *
     * @param day 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-考勤系统对接接口", notes = "节假日、休息、值班管理表-考勤系统对接接口")
    @GetMapping(value = "/api/isHoliday")
    @PlyhLog(action = "节假日、休息、值班管理表-考勤系统对接接口")
    public Result<Boolean> isHoliday(@RequestParam(name = "day", required = true) String day) {
        Result<Boolean> result = new Result<Boolean>();
        result.setSuccess(true);
        result.setResult(holidayService.isHoliday(day));
        return result;
    }

    /**
     * 分页列表查询
     *
     * @param year 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "节假日、休息、值班管理表-全量查询列表（根据年份查询）", notes = "节假日、休息、值班管理表-全量查询列表（根据年份查询）")
    @GetMapping(value = "/api/synQueryListByYear")
    @PlyhLog(action = "节假日、休息、值班管理表-全量查询列表（根据年份查询）")
    public Result<List<Holiday>> synQueryListByYear(@RequestParam(name = "year", required = true) String year) {
        Result<List<Holiday>> result = new Result<List<Holiday>>();
        QueryWrapper<Holiday> queryWrapper = new QueryWrapper<Holiday>();
        queryWrapper.eq("DATE_FORMAT( F_DT_HOLIDAY_DATA, '%Y' )", year);
        List<Holiday> pageList = holidayService.list(queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 分页列表查询
     *
     * @return 返回对象
     */
    @ApiOperation(value = "同步数据到考勤系统", notes = "同步数据到考勤系统")
    @GetMapping(value = "/api/synchronousInfo")
    @PlyhLog(action = "同步数据到考勤系统")
    public Result<?> synchronousInfo() {
        try {
            Result<?> result = new Result<>();
            holidayService.refreshHolidayData();
            return result.success("同步成功！");
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }

}
