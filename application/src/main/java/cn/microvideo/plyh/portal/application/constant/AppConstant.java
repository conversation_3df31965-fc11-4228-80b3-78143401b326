package cn.microvideo.plyh.portal.application.constant;

public class AppConstant {
    //rabbitmq的路由配置
    //测试路由1
    public static final String QUEUE_A="YcgzQueueA";
    //测试路由2
    public static final String QUEUE_B="YcgzQueueB";
    //交换机
    public static final String EXCHANGE="YcgzExchange";

    //DIRECR模式路由
    public static final String DIRECR_QUEUE="DirectQueue";
    //DIRECR模式交换机
    public static final String DIRECR_EXCHANGE="DirectExchange";
    //DIRECR模式
    public static final String DIRECR_ROOUTING="DirectRouting";
    //任务默认审批状态
    public static final Integer STATUS_0 = 0;

}
