package cn.microvideo.plyh.portal.application.business.controller;

import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.entity.DeptUser;
import cn.microvideo.module.plyh.core.service.IDeptUserService;
import cn.microvideo.module.plyh.core.vo.BatchDeptUserVO;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.log.annotation.PlyhLog;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 值班部门人员管理表
 * @Author: spring-boot
 * @Date:   2024-02-23
 * @Version: V1.0
 */
@Slf4j
@Api(tags="值班部门人员管理表")
@RestController
@RequestMapping("/deptuser")
public class DeptUserController {
	@Resource
	private IDeptUserService deptUserService;
	
	/**
	  * 分页列表查询
	 * @param deptUser 实体对象
	 * @param pageNo  页数
	 * @param pageSize  页数大小
	 * @param req  请求参数
	 * @return  返回对象
	 */
	@ApiOperation(value="值班部门人员管理表-分页列表查询", notes="值班部门人员管理表-分页列表查询")
	@GetMapping(value = "/list")
	@PlyhLog(action = "值班部门人员管理表-分页列表查询")
	public Result<IPage<DeptUser>> queryPageList(DeptUser deptUser,
												 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
												 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
												 HttpServletRequest req) {
		Result<IPage<DeptUser>> result = new Result<IPage<DeptUser>>();
		QueryWrapper<DeptUser> queryWrapper = new QueryWrapper<DeptUser>();
		Page<DeptUser> page = new Page<DeptUser>(pageNo, pageSize);
		IPage<DeptUser> pageList = deptUserService.page(page, queryWrapper);
		result.setSuccess(true);
		result.setResult(pageList);
		return result;
	}
	
	/**
	  *   添加
	 * @param deptUser  实体对象
	 * @return 返回对象
	 */
	@ApiOperation(value="值班部门人员管理表-添加", notes="值班部门人员管理表-添加")
	@PostMapping(value = "/add")
	@PlyhLog(action = "值班部门人员管理表-添加")
	public Result<DeptUser> add(@RequestBody DeptUser deptUser) {
		Result<DeptUser> result = new Result<DeptUser>();
		try {
			deptUserService.save(deptUser);
			result.success("添加成功！");
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			result.error500("操作失败");
		}
		return result;
	}

	 /**
	  *   添加
	  * @param batchDeptUserVO  实体对象
	  * @return 返回对象
	  */
	 @ApiOperation(value="值班部门人员管理表-批量新增人员数据", notes="值班部门人员管理表-批量新增人员数据")
	 @PostMapping(value = "/addBatchUsers")
	 @PlyhLog(action = "值班部门人员管理表-批量新增人员数据")
	 public Result<DeptUser> addBatchUsers(@RequestBody BatchDeptUserVO batchDeptUserVO) {
		 Result<DeptUser> result = new Result<DeptUser>();
		 try {
			 deptUserService.addBatchUsers(batchDeptUserVO);
			 result.success("添加成功！");
		 } catch (Exception e) {
			 log.error(e.getMessage(),e);
			 result.setCode(CommonConstant.TEXT_ERROR_CODE);
			 result.setMessage(e.getMessage());
		 }
		 return result;
	 }
	
	/**
	  *  编辑
	 * @param deptUser  实体对象
	 * @return 返回对象
	 */
	@ApiOperation(value="值班部门人员管理表-编辑", notes="值班部门人员管理表-编辑")
	@PostMapping(value = "/edit")
	@PlyhLog(action = "值班部门人员管理表-编辑")
	public Result<DeptUser> edit(@RequestBody DeptUser deptUser) {
		Result<DeptUser> result = new Result<DeptUser>();
		DeptUser deptUserEntity = deptUserService.getById(deptUser.getId());
		if(deptUserEntity==null) {
			result.error500("未找到对应实体");
		}else {
			boolean ok = deptUserService.updateById(deptUser);
			if(ok) {
				result.success("修改成功!");
			}
		}
		
		return result;
	}
	
	/**
	  *   通过id删除
	 * @param id  主键
	 * @return  返回对象
	 */
	@ApiOperation(value="值班部门人员管理表-通过id删除", notes="值班部门人员管理表-通过id删除")
	@GetMapping(value = "/delete")
	@PlyhLog(action = "值班部门人员管理表-通过id删除")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		try {
			deptUserService.removeById(id);
		} catch (Exception e) {
			log.error("删除失败",e.getMessage());
			return Result.error("删除失败!");
		}
		return Result.ok("删除成功!");
	}
	
	/**
	  *  批量删除
	 * @param ids 主键Ids
	 * @return  返回对象
	 */
	@ApiOperation(value="值班部门人员管理表-批量删除", notes="值班部门人员管理表-批量删除")
	@GetMapping(value = "/deleteBatch")
	@PlyhLog(action = "值班部门人员管理表-批量删除")
	public Result<DeptUser> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		Result<DeptUser> result = new Result<DeptUser>();
		if(ids==null || "".equals(ids.trim())) {
			result.error500("参数不识别！");
		}else {
			this.deptUserService.removeByIds(Arrays.asList(ids.split(",")));
			result.success("删除成功!");
		}
		return result;
	}
	
	/**
	  * 通过id查询
	 * @param id  主键
	 * @return  返回对象
	 */
	@ApiOperation(value="值班部门人员管理表-通过id查询", notes="值班部门人员管理表-通过id查询")
	@GetMapping(value = "/queryById")
	@PlyhLog(action = "值班部门人员管理表-通过id查询")
	public Result<DeptUser> queryById(@RequestParam(name="id",required=true) String id) {
		Result<DeptUser> result = new Result<DeptUser>();
		DeptUser deptUser = deptUserService.getById(id);
		if(deptUser==null) {
			result.error500("未找到对应实体");
		}else {
			result.setResult(deptUser);
			result.setSuccess(true);
		}
		return result;
	}


	/**
	 * 通过id查询
	 * @param id  主键
	 * @return  返回对象
	 */
	@ApiOperation(value="值班部门人员管理表-通过部门id查询部门下人员", notes="值班部门人员管理表-通过部门id查询部门下人员")
	@GetMapping(value = "/queryByDeptId")
	@PlyhLog(action = "值班部门人员管理表-通过部门id查询部门下人员")
	public Result<List<DeptUser>> queryByDeptId(@RequestParam(name="id",required=true) String id) {
		Result<List<DeptUser>> result = new Result<List<DeptUser>>();
		result.setResult(deptUserService.selectByFormId(id));
		result.setSuccess(true);
		return result;
	}


}
