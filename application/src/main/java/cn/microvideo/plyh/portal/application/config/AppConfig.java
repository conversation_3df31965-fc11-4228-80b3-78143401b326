package cn.microvideo.plyh.portal.application.config;

import cn.microvideo.module.plyh.core.util.CasProperties;
import net.unicon.cas.client.configuration.CasClientConfigurerAdapter;
import net.unicon.cas.client.configuration.EnableCasClient;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <b>描述:</b>
 *
 * <p>程序配置类</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2019年3月28日下午3:36:08
 * @since JDK1.8
 */
@Configuration
@EnableCasClient
public class AppConfig extends CasClientConfigurerAdapter {

    @Resource
    private CasProperties casProperties;


    /**
     * <AUTHOR>
     * @since 2021/11/24 11:30
     * authentication filter 配置参数修改.
     */
    @Override
    public void configureAuthenticationFilter(final FilterRegistrationBean authenticationFilter) {
        super.configureAuthenticationFilter(authenticationFilter);
        Map<String, String> initParameters = authenticationFilter.getInitParameters();
        List<String> urls = new LinkedList<String>();
        urls.add(casProperties.getServerIgnoreUrl());
        initParameters.put("ignorePattern", String.join("|", urls));
    }

    @Override
    public void configureValidationFilter(final FilterRegistrationBean validationFilter) {
        Map<String, String> initParameters = validationFilter.getInitParameters();
        initParameters.put("encodeServiceUrl", "false");
    }


}
