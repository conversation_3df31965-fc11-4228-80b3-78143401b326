package cn.microvideo.plyh.portal.application.config;

import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.service.IHolidayService;
import cn.microvideo.module.plyh.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 同步组织和用户数据
 */
@Slf4j
@Component
@EnableAsync
@EnableScheduling
public class TransfersQuartz {
    @Resource
    private IHolidayService holidayService;

    /**
     * 每30分钟一次
     */
    @Async
    @Scheduled(cron = "${quartz.scheduled.synchronousInfo}")
    public void synchronousInfo() {
        try {
            log.info("开始刷新节假日数据--------->");
            holidayService.refreshHolidayData();
            log.info("<---------刷新节假日数据结束！");
            //释放锁，避免造成死锁
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
    }
}
