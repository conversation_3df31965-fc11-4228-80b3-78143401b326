package cn.microvideo.plyh.portal.application.log.annotation.handler.handler;

import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;

import cn.microvideo.jchc.common.logger.ETerminal;
import cn.microvideo.jchc.common.logger.entity.Log;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.vo.MicrovideoUserVO;
import cn.microvideo.plyh.portal.application.log.annotation.PlyhLog;
import cn.microvideo.plyh.portal.application.util.IpUtils;
import cn.microvideo.sdk.qywx.utils.HttpUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

/**
 * 注解@Logger的处理器
 */
@Aspect
@Component
@Slf4j
public class LoggerHandler {
	/**
	 * 定义一个公共的切点
	 */
	@Pointcut("@annotation(cn.microvideo.plyh.portal.application.log.annotation.PlyhLog)")
	public void performance() {
	}
	/**
	 * 日志写入接口地址
	 */
	@Value("${plyh.log.server.url}")
	private String plyhLogUrl;

	@Resource
	private UserInfoUtil userInfoUtil;

	/**
	 * 用户信息在会话中的名称
	 */
	@Value("${microvideo.cas.sessionKey}")
	String KEY_SESSION_SECURITY;
	/**
	 * 终端类型
	 */
	String TERMINAL_TYPE = "remote_client";
	/**
	 * 微信终端
	 */
	String TERMINAL_WEIXIN = "weixin";
	/**
	 * 改造后收集特定数据
	 * 
	 */
	@Before("performance()")
	public void after(JoinPoint point) {
		try {
			Signature signature = point.getSignature();
			Method method = ((MethodSignature) signature).getMethod();

			PlyhLog logger = method.getAnnotation(PlyhLog.class);

			RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
			HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
			String jsonString = "";
			Map<String,String[]> parameterMap  = request.getParameterMap();
			if(parameterMap.size() > 0) {
				Map<String, String[]> newParameterMap = new HashMap<>(parameterMap);
				Iterator<Map.Entry<String, String[]>> it = newParameterMap.entrySet().iterator();
				while (it.hasNext()) {
					Map.Entry<String, String[]> entry = it.next();
					if (StringUtils.isEmpty(entry.getValue()[0]))
						it.remove();//使用迭代器的remove()方法删除元素
				}
				ObjectMapper objectMapper = new ObjectMapper();
				jsonString = objectMapper.writeValueAsString(newParameterMap);
			}


			Log loggerInfo = new Log();
			MicrovideoSessionUser mvuser = (MicrovideoSessionUser) request.getSession().getAttribute(KEY_SESSION_SECURITY);
			if(mvuser!=null){
				loggerInfo.setDeptCode(mvuser.getDeptCode());
				loggerInfo.setAccount(mvuser.getAccount());
				loggerInfo.setUserName(mvuser.getName());
				loggerInfo.setGroupNum(mvuser.getGroupId());
				loggerInfo.setDepId(mvuser.getDeptId());
				if ("平陆运河集团".equals(mvuser.getShortGroupName())){
					loggerInfo.setOrgDepName("集团本部/" + mvuser.getDepartmentPath());
				}else {
					loggerInfo.setOrgDepName(mvuser.getDepartmentPath());
				}
			}else{
				MicrovideoUserVO userVO = userInfoUtil.getHttpSessionUser();
				if (userVO != null){
					loggerInfo.setDeptCode(userVO.getDeptCode());
					loggerInfo.setAccount(userVO.getAccount());
					loggerInfo.setUserName(userVO.getName());
					loggerInfo.setGroupNum(userVO.getGroupId());
					loggerInfo.setDepId(userVO.getDeptId());
					if ("平陆运河集团".equals(userVO.getShortGroupName())){
						loggerInfo.setOrgDepName("集团本部/" + userVO.getDepartmentPath());
					}else {
						loggerInfo.setOrgDepName(userVO.getDepartmentPath());
					}
				}else{

					loggerInfo.setDeptCode("第三方服务调用");
					loggerInfo.setAccount("第三方服务调用");
					loggerInfo.setUserName("第三方服务调用");
					loggerInfo.setGroupNum("第三方服务调用");
					loggerInfo.setDepId("第三方服务调用");
					loggerInfo.setOrgDepName("第三方服务调用");

				}

			}
			request.setAttribute("ignoreError", true);

			loggerInfo.setModule(logger.module());
			loggerInfo.setAction(logger.action());
			loggerInfo.set_Id(getUUID());
			String parameter = (String) request.getAttribute(TERMINAL_TYPE);//微信转发类型
			if (parameter != null) {
				if (parameter.equals(TERMINAL_WEIXIN)) {
					loggerInfo.setTerminal(ETerminal.WECHAT);
				}
			} else {
				loggerInfo.setTerminal(logger.terminal());
			}
			loggerInfo.setTimestamp(new Timestamp(System.currentTimeMillis()));

			loggerInfo.setLocalHost(request.getLocalAddr());
			loggerInfo.setRemoteHost(IpUtils.getIpAddress(request));
			loggerInfo.setAppCode(logger.appCode());
			loggerInfo.setUnitType("内部");
//			log.setCategoryCode("work");
			send(loggerInfo,jsonString);
		} catch (Exception e) {
			log.error("日志写入失败",e);

		}
	}


	public static String getUUID() {
		UUID uuid = UUID.randomUUID();
		String s_uuid = uuid.toString().replace("-", "");
		return s_uuid;
	}

//	/**
//	 * 获取客户端真实的IP地址
//	 * @param request 请求对象
//	 * @return IP地址
//	 */
//	public static String getRealClientIPAddr(HttpServletRequest request) {
//		String ip = request.getRemoteAddr();
//		String xff = request.getHeader("x-forwarded-for");
//		if (StringUtils.isNotBlank(xff) && !"unknown".equalsIgnoreCase(xff)) {
//			String[] ips = xff.split(",");
//			for (int i = 0; i < ips.length; i++) {
//				if (!ips[i].trim().equals("unknown")) {
//					ip = ips[i].trim();
//					break;
//				}
//			}
//		}
//		return ip;
//	}



	/**
	 * 发送到日志收集
	 * @param logger
	 */
	private void send(Log logger,String jsonString) {
		Map<String, String> javaBean2Map;
		try {
			javaBean2Map=new HashMap<>();
			javaBean2Map.put("_id", logger.get_Id());
			javaBean2Map.put("account", logger.getAccount());
			javaBean2Map.put("userName", logger.getUserName());
			javaBean2Map.put("depId", logger.getDepId());
			javaBean2Map.put("deptCode", logger.getDeptCode());
			javaBean2Map.put("orgDepName", logger.getOrgDepName());
			javaBean2Map.put("remoteHost", logger.getRemoteHost());
			javaBean2Map.put("unitType", logger.getUnitType());
			javaBean2Map.put("groupNum", logger.getGroupNum());
			javaBean2Map.put("appCode", logger.getAppCode());
			javaBean2Map.put("module", logger.getModule());
			javaBean2Map.put("action", logger.getAction());
			javaBean2Map.put("terminal", logger.getTerminal().name());
			javaBean2Map.put("localHost", logger.getLocalHost());//服务器地址
			javaBean2Map.put("remark", jsonString);//你的内容
			HttpUtils.post(plyhLogUrl, javaBean2Map);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


}