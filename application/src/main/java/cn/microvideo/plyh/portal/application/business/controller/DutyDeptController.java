package cn.microvideo.plyh.portal.application.business.controller;

import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.entity.DutyDept;
import cn.microvideo.module.plyh.core.service.IDutyDeptService;
import cn.microvideo.module.plyh.core.vo.DutyDeptUserVO;
import cn.microvideo.module.plyh.core.vo.UpdateOrderRequest;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.log.annotation.PlyhLog;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 排班部门管理表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "排班部门管理表")
@RestController
@RequestMapping("/dutydept")
public class DutyDeptController {
    @Resource
    private IDutyDeptService dutyDeptService;

    /**
     * 分页列表查询
     *
     * @param dutyDept 实体对象
     * @param pageNo   页数
     * @param pageSize 页数大小
     * @param req      请求参数
     * @return 返回对象
     */
    @ApiOperation(value = "排班部门管理表-分页列表查询", notes = "排班部门管理表-分页列表查询")
    @GetMapping(value = "/list")
    @PlyhLog(action = "排班部门管理表-分页列表查询")
    public Result<IPage<DutyDeptUserVO>> queryPageList(DutyDept dutyDept,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest req) {
        Result<IPage<DutyDeptUserVO>> result = new Result<IPage<DutyDeptUserVO>>();
        Page<DutyDeptUserVO> page = new Page<DutyDeptUserVO>(pageNo, pageSize);
        //初始化查询条件
        IPage<DutyDeptUserVO> pageList = dutyDeptService.pageList(page, dutyDept);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 全量数据查询
     *
     * @param dutyDept 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "排班部门管理表-全量数据查询", notes = "排班部门管理表-全量数据查询")
    @GetMapping(value = "/queryList")
    @PlyhLog(action = "排班部门管理表-全量数据查询")
    public Result<List<DutyDeptUserVO>> queryList(DutyDept dutyDept) {
        Result<List<DutyDeptUserVO>> result = new Result<List<DutyDeptUserVO>>();
        //初始化查询条件
        List<DutyDeptUserVO> pageList = dutyDeptService.queryList(dutyDept);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 添加
     *
     * @param dutyDept 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "排班部门管理表-添加", notes = "排班部门管理表-添加")
    @PostMapping(value = "/add")
    @PlyhLog(action = "排班部门管理表-添加")
    public Result<DutyDept> add(@RequestBody DutyDept dutyDept) {
        Result<DutyDept> result = new Result<DutyDept>();
        try {
            dutyDeptService.saveByEntity(dutyDept);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500(e.getMessage());
        }
        return result;
    }

    /**
     * 添加
     *
     * @param dutyDept 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "排班部门管理表-添加部门和用户", notes = "排班部门管理表-添加部门和用户")
    @PostMapping(value = "/addWithUsers")
    @PlyhLog(action = "排班部门管理表-添加部门和用户")
    public Result<DutyDept> addWithUsers(@RequestBody DutyDeptUserVO dutyDept) {
        Result<DutyDept> result = new Result<DutyDept>();
        try {
            dutyDeptService.addWithUsers(dutyDept);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 添加
     *
     * @param dutyDept 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "排班部门管理表-添加部门和用户", notes = "排班部门管理表-添加部门和用户")
    @PostMapping(value = "/updateWithUsers")
    @PlyhLog(action = "排班部门管理表-添加部门和用户")
    public Result<DutyDept> updateWithUsers(@RequestBody DutyDeptUserVO dutyDept) {
        Result<DutyDept> result = new Result<DutyDept>();
        try {
            dutyDeptService.updateWithUsers(dutyDept);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑
     *
     * @param dutyDept 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "排班部门管理表-编辑", notes = "排班部门管理表-编辑")
    @PostMapping(value = "/edit")
    @PlyhLog(action = "排班部门管理表-编辑")
    public Result<DutyDept> edit(@RequestBody DutyDept dutyDept) {
        Result<DutyDept> result = new Result<DutyDept>();
        DutyDept dutyDeptEntity = dutyDeptService.getById(dutyDept.getId());
        if (dutyDeptEntity == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = dutyDeptService.updateByEntity(dutyDept);
            if (ok) {
                result.success("修改成功!");
            }
        }

        return result;
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "排班部门管理表-通过id删除", notes = "排班部门管理表-通过id删除")
    @GetMapping(value = "/delete")
    @PlyhLog(action = "排班部门管理表-通过id删除")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        Result result = new Result();
        try {
            dutyDeptService.removeByFormId(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            result.setCode(CommonConstant.TEXT_ERROR_CODE);
            result.setMessage(e.getMessage());
            return result;
        }
        return result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 主键Ids
     * @return 返回对象
     */
    @ApiOperation(value = "排班部门管理表-批量删除", notes = "排班部门管理表-批量删除")
    @GetMapping(value = "/deleteBatch")
    @PlyhLog(action = "排班部门管理表-批量删除")
    public Result<DutyDept> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<DutyDept> result = new Result<DutyDept>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.dutyDeptService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "排班部门管理表-通过id查询", notes = "排班部门管理表-通过id查询")
    @GetMapping(value = "/queryById")
    @PlyhLog(action = "排班部门管理表-通过id查询")
    public Result<DutyDeptUserVO> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<DutyDeptUserVO> result = new Result<DutyDeptUserVO>();
        try {
            DutyDeptUserVO dutyDept = dutyDeptService.getByFormId(id);
            result.setResult(dutyDept);
            result.setSuccess(true);
        } catch (Exception e) {
            //返回错误数据详情
            result.error500(e.getMessage());
        }
        return result;
    }




    @PostMapping("/move")
    public Result<String> UpdateOrder(@RequestBody List<String> orderedIds) {
        Result<String> result = new Result<>();
        try {
            dutyDeptService.moveItem(orderedIds);
            result.success("更新成功");
        } catch (Exception e) {
            result.error500(e.getMessage());
        }
        return result;
    }




}
