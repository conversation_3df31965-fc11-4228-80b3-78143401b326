package cn.microvideo.plyh.portal.application.util;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.util.SM4Util;
import cn.microvideo.module.plyh.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class JwtUtil {
    private static final String KEY = "PLYH_KEY";

    public static String getToken(String userName, String passWord) {
        DateTime now = DateTime.now();
        DateTime newTime = now.offsetNew(DateField.MINUTE, 480);

        Map<String, Object> payload = new HashMap<String, Object>();
        //签发时间
        payload.put(JWTPayload.ISSUED_AT, now);
        //过期时间
        payload.put(JWTPayload.EXPIRES_AT, newTime);
        //生效时间
        payload.put(JWTPayload.NOT_BEFORE, now);
        //载荷
        payload.put("userName", userName);
        if (CharSequenceUtil.isBlank(passWord)) {
            throw new BizException(CommonConstant.ERROR_500, "账户或密码错误！");
        }
        //数据加密
        payload.put("passWord", SM4Util.encryptValue(CommonConstant.SECRET_KEY, passWord.trim()));
        log.info("---------开始生成toekn----------");
        String token = JWTUtil.createToken(payload, KEY.getBytes());
        return token;

    }

    /**
     * 验证token
     *
     * @param token
     * @return
     */
    public static boolean verifyToken(String token) {
        JWT jwt = JWTUtil.parseToken(token);
        jwt.setKey(KEY.getBytes());
        return jwt.validate(0);
    }


}
