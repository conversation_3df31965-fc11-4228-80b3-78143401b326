package cn.microvideo.plyh.portal.application.business.controller;

import cn.microvideo.module.plyh.core.entity.DutyRelease;
import cn.microvideo.module.plyh.core.service.IDutyReleaseService;
import cn.microvideo.module.plyh.core.vo.AddDutyReleaseVO;
import cn.microvideo.module.plyh.core.vo.DutyReleaseSearchVO;
import cn.microvideo.module.plyh.core.vo.DutyReleaseUserVO;
import cn.microvideo.module.plyh.core.vo.ReleaseHolidayDetailVO;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.log.annotation.PlyhLog;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * @Description: 值班发布记录表
 * @Author: spring-boot
 * @Date: 2024-02-23
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "值班发布记录表")
@RestController
@RequestMapping("/dutyrelease")
public class DutyReleaseController {
    @Resource
    private IDutyReleaseService dutyReleaseService;

    /**
     * 分页列表查询
     *
     * @param dutyRelease 实体对象
     * @param pageNo      页数
     * @param pageSize    页数大小
     * @param req         请求参数
     * @return 返回对象
     */
    @ApiOperation(value = "值班发布记录表-分页列表查询", notes = "值班发布记录表-分页列表查询")
    @GetMapping(value = "/list")
    @PlyhLog(action = "值班发布记录表-分页列表查询")
    public Result<IPage<DutyReleaseUserVO>> queryPageList(DutyReleaseSearchVO dutyRelease,
                                                          @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                          @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                          HttpServletRequest req) {
        Result<IPage<DutyReleaseUserVO>> result = new Result<IPage<DutyReleaseUserVO>>();
        Page<DutyReleaseUserVO> page = new Page<DutyReleaseUserVO>(pageNo, pageSize);
        IPage<DutyReleaseUserVO> pageList = dutyReleaseService.pageList(page, dutyRelease);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     *
     * @param dutyRelease 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "值班发布记录表-添加", notes = "值班发布记录表-添加")
    @PostMapping(value = "/add")
    @PlyhLog(action = "值班发布记录表-添加")
    public Result<DutyRelease> add(@RequestBody DutyRelease dutyRelease) {
        Result<DutyRelease> result = new Result<DutyRelease>();
        try {
            dutyReleaseService.save(dutyRelease);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 添加
     *
     * @param dutyRelease 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "值班发布记录表-值班发布", notes = "值班发布记录表-值班发布")
    @PostMapping(value = "/addRelease")
    @PlyhLog(action = "值班发布记录表-值班发布")
    public Result<DutyRelease> addRelease(@RequestBody AddDutyReleaseVO dutyRelease) {
        Result<DutyRelease> result = new Result<DutyRelease>();
        try {
            dutyReleaseService.addRelease(dutyRelease);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑
     *
     * @param dutyRelease 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "值班发布记录表-编辑", notes = "值班发布记录表-编辑")
    @PostMapping(value = "/edit")
    @PlyhLog(action = "值班发布记录表-编辑")
    public Result<DutyRelease> edit(@RequestBody DutyRelease dutyRelease) {
        Result<DutyRelease> result = new Result<DutyRelease>();
        DutyRelease dutyReleaseEntity = dutyReleaseService.getById(dutyRelease.getId());
        if (dutyReleaseEntity == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = dutyReleaseService.updateById(dutyRelease);
            if (ok) {
                result.success("修改成功!");
            }
        }

        return result;
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "值班发布记录表-通过id删除", notes = "值班发布记录表-通过id删除")
    @GetMapping(value = "/delete")
    @PlyhLog(action = "值班发布记录表-通过id删除")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            dutyReleaseService.removeByCancelId(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 主键Ids
     * @return 返回对象
     */
    @ApiOperation(value = "值班发布记录表-批量删除", notes = "值班发布记录表-批量删除")
    @GetMapping(value = "/deleteBatch")
    @PlyhLog(action = "值班发布记录表-批量删除")
    public Result<DutyRelease> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<DutyRelease> result = new Result<DutyRelease>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.dutyReleaseService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "值班发布记录表-通过id查询", notes = "值班发布记录表-通过id查询")
    @GetMapping(value = "/queryById")
    @PlyhLog(action = "值班发布记录表-通过id查询")
    public Result<DutyRelease> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<DutyRelease> result = new Result<DutyRelease>();
        DutyRelease dutyRelease = dutyReleaseService.getById(id);
        if (dutyRelease == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(dutyRelease);
            result.setSuccess(true);
        }
        return result;
    }


    /**
     * 通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "值班发布记录表-通过id查询发布的详情", notes = "值班发布记录表-通过id查询发布的详情")
    @GetMapping(value = "/queryByHolidays")
    @PlyhLog(action = "值班发布记录表-通过id查询发布的详情")
    public Result<ReleaseHolidayDetailVO> queryByHolidays(@RequestParam(name = "id", required = true) String id) {
        Result<ReleaseHolidayDetailVO> result = new Result<ReleaseHolidayDetailVO>();
        ReleaseHolidayDetailVO holidayMonthVO = dutyReleaseService.getByReleaseId(id);
        result.setResult(holidayMonthVO);
        result.setSuccess(true);
        return result;
    }


}
