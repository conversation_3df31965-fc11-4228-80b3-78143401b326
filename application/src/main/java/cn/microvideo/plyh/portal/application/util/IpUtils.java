package cn.microvideo.plyh.portal.application.util;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

public class IpUtils {

    /**
     * 获取Ip地址
     *
     * @param request the request
     * @return 返回对象
     */
    public static String getIpAddress(HttpServletRequest request) {
        String xip = request.getHeader("X-Real-IP");
        String xFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotEmpty(xFor) && !"unKnown".equalsIgnoreCase(xFor)) {
            //多次反向代理后会有多个ip值，第一个ip才是真实ip
            int index = xFor.indexOf(",");
            if (index != -1) {
                return xFor.substring(0, index);
            } else {
                return xFor;
            }
        }
        xFor = xip;

        if (StringUtils.isNotEmpty(xFor) && !"unknown".equalsIgnoreCase(xFor)) {
            return xFor;
        }
        if (isNotPublicIp(xFor)) {
            xFor = request.getHeader("Proxy-Client-IP");
        }
        if (isNotPublicIp(xFor)) {
            xFor = request.getHeader("WL-Proxy-Client-IP");
        }
        if (isNotPublicIp(xFor)) {
            xFor = request.getHeader("HTTP_CLIENT_IP");
        }
        if (isNotPublicIp(xFor)) {
            xFor = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (isNotPublicIp(xFor)) {
            xFor = request.getRemoteAddr();
        }
        return xFor;
    }



    private static boolean isNotPublicIp(String xFor) {
        String unknown = "unknown";
        return StringUtils.isBlank(xFor) || unknown.equalsIgnoreCase(xFor);
    }


}
