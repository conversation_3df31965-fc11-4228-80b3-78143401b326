package cn.microvideo.plyh.portal.application.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.session.data.redis.config.ConfigureRedisAction;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;

@Configuration
//设置session过期时间,默认是1800秒（当前是4个小时）
@EnableRedisHttpSession(redisNamespace = HttpSessionConfig.REDIS_NAMESPACE, maxInactiveIntervalInSeconds = 28800)
public class HttpSessionConfig {
    public static final String REDIS_NAMESPACE = "spring:session";

    @Bean
    public static ConfigureRedisAction configureRedisAction() {
        return ConfigureRedisAction.NO_OP;
    }


    /**
     * custom cookie
     * @return CookieSerializer
     */
//	@Bean
//	public CookieSerializer cookieSerializer() {
//		DefaultCookieSerializer serializer = new DefaultCookieSerializer();
//		//<1> session's cookie name
//		serializer.setCookieName("spring:session:seal");
//		//<2> cookie path
//		serializer.setCookiePath("/");
//		//<3> Domain Name Pattern
//		//设置session的domain的拦截,(带域名千万别过滤)
////		serializer.setDomainNamePattern("^.+?\\.(\\w+\\.[a-z]+)$");
//		return serializer;
//	}
}
