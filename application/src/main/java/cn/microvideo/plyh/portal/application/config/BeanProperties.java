package cn.microvideo.plyh.portal.application.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @description: 基础配置类
 * @author: Mr<PERSON>
 * @date: 2023/2/14 16:06
 */
@Component
@Data
@ConfigurationProperties(prefix = "bean.custom")
public class BeanProperties {
    /**
     * 是否发送微信推送
     */
    private Boolean isSenWxMsg;
    /**
     * 门户OA服务地址
     */
    private String faceServerUrl;

}
