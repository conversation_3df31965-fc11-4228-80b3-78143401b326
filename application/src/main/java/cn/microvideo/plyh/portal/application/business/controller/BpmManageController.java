package cn.microvideo.plyh.portal.application.business.controller;

import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.framework.core.basic.session.util.MicrovideoSessionUtil;
import cn.microvideo.module.plyh.core.entity.BpmManage;
import cn.microvideo.module.plyh.core.service.IBpmManageService;
import cn.microvideo.module.plyh.core.util.CasProperties;
import cn.microvideo.module.plyh.core.vo.*;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.plyh.portal.application.log.annotation.PlyhLog;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: bpm流程调用表
 * @Author: spring-boot
 * @Date: 2023-09-05
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "bpm流程调用表")
@RestController
@RequestMapping("/bpmmanage")
public class BpmManageController {
    @Resource
    private IBpmManageService bpmManageService;
    /**
     * Cas配置类
     */
    @Resource
    private CasProperties casProperties;

    /**
     * 分页列表查询
     *
     * @param bpmManage 实体对象
     * @param pageNo    页数
     * @param pageSize  页数大小
     * @param req       请求参数
     * @return 返回对象
     */
    @ApiOperation(value = "bpm流程调用表-分页列表查询", notes = "bpm流程调用表-分页列表查询")
    @GetMapping(value = "/list")
    @PlyhLog(action = "bpm流程调用表-分页列表查询")
    public Result<IPage<BpmManage>> queryPageList(BpmManage bpmManage,
                                                  @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                  HttpServletRequest req) {
        Result<IPage<BpmManage>> result = new Result<IPage<BpmManage>>();
        QueryWrapper<BpmManage> queryWrapper = new QueryWrapper<BpmManage>();
        Page<BpmManage> page = new Page<BpmManage>(pageNo, pageSize);
        IPage<BpmManage> pageList = bpmManageService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     *
     * @param bpmManage 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "bpm流程调用表-添加", notes = "bpm流程调用表-添加")
    @PostMapping(value = "/add")
    @PlyhLog(action = "bpm流程调用表-添加")
    public Result<BpmManage> add(@RequestBody BpmManage bpmManage) {
        Result<BpmManage> result = new Result<BpmManage>();
        try {
            bpmManageService.save(bpmManage);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 编辑
     *
     * @param bpmManage 实体对象
     * @return 返回对象
     */
    @ApiOperation(value = "bpm流程调用表-编辑", notes = "bpm流程调用表-编辑")
    @PostMapping(value = "/edit")
    @PlyhLog(action = "bpm流程调用表-编辑")
    public Result<BpmManage> edit(@RequestBody BpmManage bpmManage) {
        Result<BpmManage> result = new Result<BpmManage>();
        BpmManage bpmManageEntity = bpmManageService.getById(bpmManage.getUuid());
        if (bpmManageEntity == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = bpmManageService.updateById(bpmManage);
            if (ok) {
                result.success("修改成功!");
            }
        }

        return result;
    }

    /**
     * 通过id删除
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "bpm流程调用表-通过id删除", notes = "bpm流程调用表-通过id删除")
    @GetMapping(value = "/delete")
    @PlyhLog(action = "bpm流程调用表-通过id删除")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            bpmManageService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 启动流程
     *
     * @return 返回对象
     */
    @ApiOperation(value = "bpm流程调用表-启动流程", notes = "bpm流程调用表-启动流程")
    @GetMapping(value = "/startBpm")
    @PlyhLog(action = "bpm流程调用表-启动流程")
    public Result<?> startBpm() {
        try {
//            StratReturnVO stratReturnVO =bpmManageService.startBpm();
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("启动失败!");
        }
        return Result.ok("启动成功!");
    }

    /**
     * 批量删除
     *
     * @param ids 主键Ids
     * @return 返回对象
     */
    @ApiOperation(value = "bpm流程调用表-批量删除", notes = "bpm流程调用表-批量删除")
    @GetMapping(value = "/deleteBatch")
    @PlyhLog(action = "bpm流程调用表-批量删除")
    public Result<BpmManage> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<BpmManage> result = new Result<BpmManage>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.bpmManageService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id 主键
     * @return 返回对象
     */
    @ApiOperation(value = "bpm流程调用表-通过id查询", notes = "bpm流程调用表-通过id查询")
    @GetMapping(value = "/queryById")
    @PlyhLog(action = "bpm流程调用表-通过id查询")
    public Result<BpmManage> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<BpmManage> result = new Result<BpmManage>();
        BpmManage bpmManage = bpmManageService.getById(id);
        if (bpmManage == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(bpmManage);
            result.setSuccess(true);
        }
        return result;
    }


    /**
     * 根据taskId查询 审批流程节点和人员
     *
     * @param subNodeVO
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-获取审批流程节点和人员", notes = "bpm流程调用表-审批流程节点和人员")
    @PostMapping(value = "/getSubNode")
    @PlyhLog(action = "bpm流程调用表-获取审批流程节点和人员")
    public Result<List<ActivityProIdUserVO>> getSubNode(@RequestBody SubNodeVO subNodeVO) {
        Result<List<ActivityProIdUserVO>> result = new Result<List<ActivityProIdUserVO>>();
        List<ActivityProIdUserVO> list = bpmManageService.getSubNode(subNodeVO);
        result.setResult(list);
        result.setSuccess(true);
        return result;
    }


    /**
     * 根据taskId查询 审批流程节点和人员
     *
     * @param agreeActiviotyVO
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-同意", notes = "bpm流程调用表-同意")
    @PostMapping(value = "/agreeActivity")
    @PlyhLog(action = "bpm流程调用表-同意")
    public Result<StratReturnVO> getSubNode(@RequestBody AgreeActiviotyVO agreeActiviotyVO, HttpSession session) {
        Result<StratReturnVO> result = new Result<StratReturnVO>();
        MicrovideoSessionUser user = MicrovideoSessionUtil.getHttpSessionUser(session, casProperties.getSessionKey());
        StratReturnVO stratReturnVO = bpmManageService.agreeActivity(agreeActiviotyVO, user);
        result.setResult(stratReturnVO);
        result.setSuccess(true);
        return result;
    }

    /**
     * 根据taskId查询 审批流程节点和人员
     *
     * @param
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-获取可退回的节点", notes = "bpm流程调用表-获取可退回的节点")
    @GetMapping(value = "/getBackNodes")
    @PlyhLog(action = "bpm流程调用表-获取可退回的节点")
    public Result<List<ActivityBackVO>> getBackNodes(@RequestParam(name = "taskId", required = true) String taskId) {
        Result<List<ActivityBackVO>> result = new Result<List<ActivityBackVO>>();
        List<ActivityBackVO> list = bpmManageService.getBackNodes(taskId);
        result.setResult(list);
        result.setSuccess(true);
        return result;
    }


    /**
     * 根据taskId查询 审批流程节点和人员
     *
     * @param agreeActiviotyVO
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-任务跳转（退回可以用这个）", notes = "bpm流程调用表-任务跳转（退回可以用这个）")
    @PostMapping(value = "/freeJump")
    @PlyhLog(action = "bpm流程调用表-任务跳转")
    public Result<StratReturnVO> freeJump(@RequestBody AgreeActiviotyVO agreeActiviotyVO, HttpSession session) {
        Result<StratReturnVO> result = new Result<StratReturnVO>();
        MicrovideoSessionUser user = MicrovideoSessionUtil.getHttpSessionUser(session, casProperties.getSessionKey());
        bpmManageService.freeJump(agreeActiviotyVO, user);
        result.setSuccess(true);
        return result;
    }

    /**
     * 根据taskId查询 审批流程节点和人员
     *
     * @param agreeActiviotyVO
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-会签退回", notes = "bpm流程调用表-会签退回")
    @PostMapping(value = "/back/hq")
    @PlyhLog(action = "bpm流程调用表-会签退回")
    public Result<StratReturnVO> backHq(@RequestBody AgreeActiviotyVO agreeActiviotyVO, HttpSession session) {
        Result<StratReturnVO> result = new Result<StratReturnVO>();
        MicrovideoSessionUser user = MicrovideoSessionUtil.getHttpSessionUser(session, casProperties.getSessionKey());
        bpmManageService.backHq(agreeActiviotyVO, user);
        result.setSuccess(true);
        return result;
    }


    /**
     * 根据taskId查询 审批流程节点和人员
     *
     * @param procId
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-查询流程审批记录）", notes = "bpm流程调用表-查询流程审批记录")
    @GetMapping(value = "/nodeOpinion")
    @PlyhLog(action = "bpm流程调用表-查询流程审批记录")
    public Result<List<NodeOpinionVO>> nodeOpinion(@RequestParam(name = "procId", required = true) String procId) {
        Result<List<NodeOpinionVO>> result = new Result<List<NodeOpinionVO>>();
        result.setResult(bpmManageService.nodeOpinion(procId));
        result.setSuccess(true);
        return result;
    }

    /**
     * 根据taskId查询 审批流程节点和人员
     *
     * @param
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-根据modeKey获取节点下的所有人员或者部门）", notes = "bpm流程调用表-根据modeKey获取节点下的所有人员或者部门")
    @GetMapping(value = "/queryAllUserByModelKey")
    @PlyhLog(action = "bpm流程调用表-根据modeKey获取节点下的所有人员或者部门")
    public Result<List<NodeUserVO>> queryAllUserByModelKey() {
        Result<List<NodeUserVO>> result = new Result<List<NodeUserVO>>();
        result.setResult(bpmManageService.queryAllUserByModelKey());
        result.setSuccess(true);
        return result;
    }

    /**
     * 根据taskId查询 审批流程节点和人员
     *
     * @param
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-根据任务ID(待办)查询配置的用户", notes = "bpm流程调用表-根据任务ID(待办)查询配置的用户")
    @GetMapping(value = "/smsUsersByTaskId")
    @PlyhLog(action = "bpm流程调用表-根据任务ID(待办)查询配置的用户")
    public Result<String> smsUsersByTaskId(@RequestParam(name = "taskId", required = true) String taskId) {
        Result<String> result = new Result<String>();
        result.setResult(bpmManageService.smsUsersByTaskId(taskId));
        result.setSuccess(true);
        return result;
    }


    /**
     * 根据taskId查询 审撤销（召回）任务
     *
     * @param cancelVO
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-撤销（召回）任务", notes = "bpm流程调用表-撤销（召回）任务")
    @PostMapping(value = "/cx")
    @PlyhLog(action = "bpm流程调用表-撤销（召回）任务")
    public Result<StratReturnVO> cx(@RequestBody AgreeActiviotyVO cancelVO, HttpSession session) {
        Result<StratReturnVO> result = new Result<StratReturnVO>();
        MicrovideoSessionUser user = MicrovideoSessionUtil.getHttpSessionUser(session, casProperties.getSessionKey());
        bpmManageService.cancel(cancelVO, user);
        result.setSuccess(true);
        return result;
    }


    /**
     * 根据taskId查询 审撤销（召回）任务
     *
     * @param cancelVO
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-任务中断-直接结束流程", notes = "bpm流程调用表-任务中断-直接结束流程")
    @PostMapping(value = "/terminate")
    @PlyhLog(action = "bpm流程调用表-任务中断-直接结束流程")
    public Result<StratReturnVO> terminate(@RequestBody AgreeActiviotyVO cancelVO, HttpSession session) {
        Result<StratReturnVO> result = new Result<StratReturnVO>();
        MicrovideoSessionUser user = MicrovideoSessionUtil.getHttpSessionUser(session, casProperties.getSessionKey());
        bpmManageService.terminate(cancelVO, user);
        result.setSuccess(true);
        return result;
    }


    /**
     * 根据taskId查询 审撤销（召回）任务
     *
     * @param zbTaskVO
     * @return
     */
    @ApiOperation(value = "bpm流程调用表-任务转办", notes = "bpm流程调用表-任务转办")
    @PostMapping(value = "/transfer")
    @PlyhLog(action = "bpm流程调用表-任务转办")
    public Result<StratReturnVO> transfer(@RequestBody AgreeActiviotyVO zbTaskVO, HttpSession session) {
        Result<StratReturnVO> result = new Result<StratReturnVO>();
        MicrovideoSessionUser user = MicrovideoSessionUtil.getHttpSessionUser(session, casProperties.getSessionKey());
        bpmManageService.transfer(zbTaskVO, user);
        result.setSuccess(true);
        return result;
    }


}
