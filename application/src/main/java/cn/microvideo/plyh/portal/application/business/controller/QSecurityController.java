package cn.microvideo.plyh.portal.application.business.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.microvideo.framework.core.basic.session.user.MicrovideoSessionUser;
import cn.microvideo.framework.core.basic.session.util.MicrovideoSessionUtil;
import cn.microvideo.module.plyh.commonconstant.CommonConstant;
import cn.microvideo.module.plyh.core.util.CasProperties;
import cn.microvideo.module.plyh.core.util.QsecurityUtils;
import cn.microvideo.module.plyh.core.util.UserInfoUtil;
import cn.microvideo.module.plyh.core.vo.*;
import cn.microvideo.module.plyh.exception.BizException;
import cn.microvideo.module.plyh.exception.Result;
import cn.microvideo.qsc.client.api.QsApi;
import cn.microvideo.qsc.client.entity.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>描述:</b>
 *
 * <p>QSecurity相关接口调用</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @Date 2019年12月20日上午9:39:47
 * @since JDK1.8
 */
@RestController
@RequestMapping("/qsecurity")
@Api(tags = {"qs管理"})
public class QSecurityController {

    @Resource
    private UserInfoUtil userInfoUtil;

    /**
     * 获取当前用户导航菜单
     *
     * @return
     */
    @GetMapping("/navs")
    @ApiOperation(value = "获取当前用户导航菜单", notes = "获取当前用户导航菜单")
    public Result<List<Nav>> listUserNavs(HttpSession session) {
        Result<List<Nav>> result = new Result<>();
        try {
            MicrovideoUserVO user = userInfoUtil.getHttpSessionUser();
            List<Nav> navs = QsecurityUtils.listUserNavs(user);
            result.setResult(navs);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }


    /**
     * 人员组织架构接口
     *
     * @param id      节点ID
     * @return
     */
    @GetMapping("/zTreeQSecurity")
    @ApiOperation(value = "获取当前单位组织树", notes = "获取当前单位组织树")
    public Result<List<ZtreeNode>> zTreeQSecurity(String id) {
        Result<List<ZtreeNode>> result = new Result<>();
        try {
            if (CharSequenceUtil.isBlank(id)) {
                MicrovideoUserVO user = userInfoUtil.getHttpSessionUser();
                id = user.getGroupId();
            }
            List<ZtreeNode> nodes = QsecurityUtils.zTreeQSecurityByGroupId(id);
            result.setResult(nodes);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }


    /**
     * 人员组织架构接口
     *
     * @param userName 节点ID
     * @param session  会话
     * @return
     */
    @GetMapping("/zTreeQSecurityByUserName")
    @ApiOperation(value = "获取当前单位组织树(支持人员姓名查询)", notes = "获取当前单位组织树(支持人员姓名查询)")
    public Result<List<ZtreeNode>> zTreeQSecurityByUserName(String userName, String id, HttpSession session) {
        Result<List<ZtreeNode>> result = new Result<>();
        try {
            List<ZtreeNode> nodes = new ArrayList<>();
            if (CharSequenceUtil.isBlank(userName)) {
                MicrovideoUserVO user = userInfoUtil.getHttpSessionUser();
                if (CharSequenceUtil.isBlank(id)) {
                    id = user.getGroupId();
                }
                nodes = QsecurityUtils.zTreeQSecurity(id);
            } else {
                nodes = QsecurityUtils.selectByUserName(userName);
            }
            result.setResult(nodes);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }


    /**
     * 人员组织架构接口
     *
     * @param id      节点ID
     * @param session 会话
     * @return
     */
    @GetMapping("/groupQSecurity")
    @ApiOperation(value = "获取当前单位组织（不带人）", notes = "获取当前单位组织（不带人）")
    public Result<List<ZtreeNode>> groupQSecurity(String id, HttpSession session) {
        Result<List<ZtreeNode>> result = new Result<>();
        try {
            if (CharSequenceUtil.isBlank(id)) {
                MicrovideoUserVO user = userInfoUtil.getHttpSessionUser();
                id = user.getGroupId();
            }
            List<ZtreeNode> nodes = QsecurityUtils.zTreeNodes(id);
            result.setResult(nodes);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }


    /**
     * 人员组织架构接口
     *
     * @param id      节点ID
     * @param session 会话
     * @return
     */
    @GetMapping("/deptQSecurity")
    @ApiOperation(value = "获取当前部门组织树人员", notes = "获取当前部门组织树人员")
    public Result<List<ZtreeNode>> deptQSecurity(String id, HttpSession session) {
        Result<List<ZtreeNode>> result = new Result<>();
        try {
            if (CharSequenceUtil.isBlank(id)) {
                MicrovideoUserVO user = userInfoUtil.getHttpSessionUser();
                id = user.getDeptId();
            }
            List<ZtreeNode> nodes = QsecurityUtils.zTreeQSecurity(id);
            result.setResult(nodes);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }


    /**
     * 人员组织架构接口
     *
     * @param session 会话
     * @return
     */
    @GetMapping("/deptQSecurityNotSelf")
    @ApiOperation(value = "获取当前部门组织树人员（不包含自己）", notes = "获取当前部门组织树人员（不包含自己）")
    public Result<List<ZtreeNode>> deptQSecurityNotSelf(HttpSession session) {
        Result<List<ZtreeNode>> result = new Result<>();
        try {
            String userId = "";
            String orgId = "";
            MicrovideoUserVO user = userInfoUtil.getHttpSessionUser();
            if (null == user) {
                throw new BizException(CommonConstant.ERROR_500, "未找到当前人信息！");
            }
            List<ZtreeNode> nodes = QsecurityUtils.zTreeDeptUsersNoSelf(user.getGroupId(), user.getDeptId(), user.getId());
            result.setResult(nodes);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }

    /**
     * 获取用户所在部门集合
     *
     * @return session
     */
    @GetMapping("/user/depts")
    @ApiOperation(value = "获取用户所在部门集合", notes = "获取用户所在部门集合")
    public Result<Set<UserChange>> getUserDepts(HttpSession session) {
        Result<Set<UserChange>> result = new Result<>();
        try {
            MicrovideoUserVO userVo = userInfoUtil.getHttpSessionUser();
            String account = userVo.getAccount();
            User user = QsApi.getApi().getPersonByPid(account);
            Set<UserChange> changes = user.getOrgList().stream().map(o -> {
                UserChange change = new UserChange();
                change.setDeptId(o.getOuid());
                change.setDeptName(o.getOuname());
                change.setOrgaId(o.getOid());
                change.setOrgaName(o.getOname());
                return change;
            }).collect(Collectors.toSet());
            result.setResult(changes);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }

    /**
     * 获取当前用户4v菜单
     *
     * @param session 会话
     * @return session
     */
    @GetMapping("/navs4v")
    @ApiOperation(value = "获取当前用户4v菜单", notes = "获取当前用户4v菜单")
    public Result<List<Nav>> listUserNavs4v(HttpSession session) {
        Result<List<Nav>> result = new Result<>();
        try {
            MicrovideoUserVO user = userInfoUtil.getHttpSessionUser();
            List<Nav> navs = QsecurityUtils.listUserNavs4v(user);
            result.setResult(navs);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }

    /**
     * 获取用户权限集合
     *
     * @return session
     */
    @GetMapping("/list-auths")
    @ApiOperation(value = "获取用户权限集合", notes = "获取用户权限集合")
    public Result<Set<String>> listAuths() {
        Result<Set<String>> result = new Result<>();
        try {
            MicrovideoUserVO userVo = userInfoUtil.getHttpSessionUser();
            Set<String> auths = QsecurityUtils.getAuths(userVo);
            result.setResult(auths);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }


    /**
     * 批量验证用户权限
     *
     * @param auths   待验证权限集合
     * @param session 会话
     * @return session
     */
    @PostMapping("/has-auths")
    @ApiOperation(value = "批量验证用户权限", notes = "批量验证用户权限")
    public Result<Map<String, Boolean>> hasAuths(@RequestBody List<String> auths, HttpSession session) {
        Result<Map<String, Boolean>> result = new Result<>();
        try {
            MicrovideoUserVO userVo = userInfoUtil.getHttpSessionUser();
            Map<String, Boolean> hasAuths = QsecurityUtils.hasAuths(auths, userVo);
            result.setResult(hasAuths);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }


    /**
     * 获取当前登录人账号和密码
     *
     * @param
     * @return
     */
    @GetMapping("/get-user")
    @ApiOperation(value = "获取当前登录人账号和密码", notes = "获取当前登录人账号和密码")
    public Result<UserInfoVO> getUser(HttpSession session) {
        Result<UserInfoVO> result = new Result<>();
        try {
            MicrovideoUserVO userVo = userInfoUtil.getHttpSessionUser();
            //复制数据
            UserInfoVO userInfoVO = BeanUtil.copyProperties(userVo, UserInfoVO.class);
            if (!Objects.isNull(userVo)) {
                String password = QsecurityUtils.getUserPass(userVo);
                //获取用户密码
                userInfoVO.setPassWord(password);
            }
            result.setResult(userInfoVO);
            result.setSuccess(true);
        } catch (Exception e) {
            throw new BizException(CommonConstant.ERROR_500, e.getMessage());
        }
        return result;
    }

    /**
     * 获取登录用户信息
     *
     * @return
     */
    @ApiOperation(value = "获取登录用户信息")
    @GetMapping("/getUserInfo")
    public Result<MicrovideoUserVO> getUserInfo() {
        Result<MicrovideoUserVO> result = new Result<MicrovideoUserVO>();
        result.setResult(userInfoUtil.getHttpSessionUser());
        result.setSuccess(true);
        return result;
    }

}
