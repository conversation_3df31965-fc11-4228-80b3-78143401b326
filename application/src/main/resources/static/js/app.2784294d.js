(function(e){function t(t){for(var a,r,c=t[0],s=t[1],l=t[2],d=0,p=[];d<c.length;d++)r=c[d],Object.prototype.hasOwnProperty.call(i,r)&&i[r]&&p.push(i[r][0]),i[r]=0;for(a in s)Object.prototype.hasOwnProperty.call(s,a)&&(e[a]=s[a]);u&&u(t);while(p.length)p.shift()();return o.push.apply(o,l||[]),n()}function n(){for(var e,t=0;t<o.length;t++){for(var n=o[t],a=!0,c=1;c<n.length;c++){var s=n[c];0!==i[s]&&(a=!1)}a&&(o.splice(t--,1),e=r(r.s=n[0]))}return e}var a={},i={app:0},o=[];function r(t){if(a[t])return a[t].exports;var n=a[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.m=e,r.c=a,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(n,a,function(t){return e[t]}.bind(null,a));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="/seal/";var c=window["webpackJsonp"]=window["webpackJsonp"]||[],s=c.push.bind(c);c.push=t,c=c.slice();for(var l=0;l<c.length;l++)t(c[l]);var u=s;o.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("cd49")},"00cb":function(e,t,n){},"02212":function(e,t,n){"use strict";n("973f")},"03c0":function(e,t,n){},"0600":function(e,t,n){},"147b":function(e,t,n){"use strict";n("3645")},"1b5a":function(e,t,n){},"1b61":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAADeklEQVQ4T43US2xUZRQH8P//u3dehUKrBMGFKUh49DGSdGEETUoMG8En6Q1tp8wUIyVdKRBNakzGGILKxsSFitLKVKY4Y22I8RGI2h0uRCydaa2OY6MYkRaJhqHzuPc7ZqYUbLUyd/fdk++Xc853coh538bQYFUe2QaDri0iup5AhRA5gikN/YUb/OZcrzU5/97smTcDYXV3oHa1z8QeEo8TcAngQMQGaRJQpTPwuZbCG8mJHxIYCtvz4eugsC4Y26SUOgRglYh8rRQHc3bhW4cq44b2msINMMyHBdIkwBUReSE5MfbpfJSAsDY4UGso/TaBO7Wol5HLDyZ+W3kZQ1tuZhAOq7pfaqtMbWx1xHmJhCbsrvO93w8BYX2j5A2BgZWmaR8G+SDE2D9d6RtIvf5QbqEeNTV9aV5efamJwndEmFJagsMR69cb4D2h+BMa+jUo9Rmy+edGom1XFsJm/zfueasiW7htnwHZC5rdLnOy/+yRzkIxzoZgvI+UzaLttpFI65lbYbPxtaEP1nugTwow6nHbT5090jo1A4ZiwyAyzOet88cDF8oGW6LLPF7XUUDWTWu9LXVs548zYEfsZwAJydodif7W38sF17R9ssTnvnYQ0Ns19WPJnp3DJdAfiqUFSGsgkHzXulguuDHUW+Vg8aukbHWAbclea/R6hvGvAFmUt/HIeJ/1U7lgXSi2gkRUAdVXbTya7rOKlRZLfv8VgC1CdSDRsyMOUMpBG3bF7oPCCYAfZTLyfDpu/VkC6zv6NxHGUYCTeVuC5WRZ3xK9g17XIULudxzpTEasodlE6G+PLNKGt4vAM0LGVT53+P9eu7Eluizrde1VIk8DPDFdyHWnjgf+mrMc/O2R5dr0dhPYAXBIa/0mTI5lnMy1moka+4JvxPBV316hvcZdSowgIM0AfARP5SgvjvdY4//aNsUmK7ALlLZSUHhGFM5R8IdoVipqP4AHQFBEPhaqSxQJgUg52n529FhrstTDfz7Aut0nKz06t1mAdkDuBahACKTIQEQwBlERFnKn3YtdOltQuxX0finOMbAveRXjc8AZPKzW71pVrehaoWDWkFgqCtOS12k6+mLCNqcQt4p7EWva3lvicXufNKAPCPCdBg7+BzhnaIjmmEK8WS80To3NsaXZCnYqFhcFTt8KLGck4W//cDkNvV2Unvob5EuG9+CJn7oAAAAASUVORK5CYII="},2272:function(e,t,n){e.exports=n.p+"img/newagree.eb77f955.png"},"2b35":function(e,t,n){"use strict";n("b6bc")},"2db4":function(e,t,n){"use strict";n("c01d")},"2dfe":function(e,t,n){"use strict";n("03c0")},"2ffd":function(e,t,n){"use strict";n("0600")},"30b7":function(e,t,n){},"32f0":function(e,t,n){},3645:function(e,t,n){},3862:function(e,t,n){"use strict";n("32f0")},"3a57":function(e,t,n){"use strict";n("f2dd")},"3e72":function(e,t,n){"use strict";n("e6f6")},4114:function(e,t,n){},4151:function(e,t,n){},"42a1":function(e,t,n){"use strict";n("91ed")},4549:function(e,t,n){},4642:function(e,t,n){},4678:function(e,t,n){var a={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df4","./fa.js":"8df4","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b4","./gd.js":"f6b4","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function i(e){var t=o(e);return n(t)}function o(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}i.keys=function(){return Object.keys(a)},i.resolve=o,e.exports=i,i.id="4678"},"4b91":function(e,t,n){},"541b":function(e,t,n){"use strict";n("c75c")},5633:function(e,t,n){"use strict";n("f3c3")},"591e":function(e,t,n){"use strict";n("4b91")},"5a38":function(e,t,n){"use strict";n("4642")},"6e08":function(e,t,n){"use strict";n("86a3")},7457:function(e,t,n){},"7d1f":function(e,t,n){"use strict";n("30b7")},"7f41":function(e,t,n){"use strict";n("cd64")},"845e":function(e,t,n){},8488:function(e,t,n){"use strict";n("c5a5")},"86a3":function(e,t,n){},"87e4":function(e,t,n){},"8a32":function(e,t,n){},"8c4a":function(e,t,n){"use strict";n("9c65")},"91ed":function(e,t,n){},9227:function(e,t,n){},"96e9":function(e,t,n){},"970c":function(e,t,n){"use strict";n("fd4b")},"973f":function(e,t,n){},"9ad4":function(e,t,n){"use strict";n("fb7a")},"9bc4":function(e,t,n){"use strict";n("4114")},"9c65":function(e,t,n){},"9e50":function(e,t,n){},a295:function(e,t,n){"use strict";n("9e50")},a2f0:function(e,t,n){},a36c:function(e,t,n){"use strict";n("845e")},a50a:function(e,t,n){},a599:function(e,t,n){"use strict";n("7457")},a655:function(e,t,n){},a74f:function(e,t,n){"use strict";n("00cb")},ac30:function(e,t,n){"use strict";n("8a32")},b32a:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAARCAYAAADQWvz5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQ1IDc5LjE2MzQ5OSwgMjAxOC8wOC8xMy0xNjo0MDoyMiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTkgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjQ0OTNDM0QyRDNCNTExRTlCMDlDQTNBRTU1MjkzOUQxIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjQ0OTNDM0QzRDNCNTExRTlCMDlDQTNBRTU1MjkzOUQxIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NDQ5M0MzRDBEM0I1MTFFOUIwOUNBM0FFNTUyOTM5RDEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NDQ5M0MzRDFEM0I1MTFFOUIwOUNBM0FFNTUyOTM5RDEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz76PGL7AAABFElEQVR42pRUyw2CQBBVQgF04FqBlIAnPZLoXahAqUBKwA7kbiJHb0AjQgnYgbNkNhlfdiFO8thkPm933+ywfB92C4tFhDOvAfsGQkO48Tra+vEaVw8IdFHNiAWJicUiLmM/RIrQ8SnmLOJcZSOqcJcZ07lPJLoQNiKpZKChP+yO+wSJpBWEBIpK9hWQe9UfX7MSVhDUYm650FjCuTXkKjqV8h26BBay0NYtQ+bNiIkncjbDnyAqWbtQ6KiJTi6ixkGCmshrSrIPve7GE4XYNamJ0Sy0dK2SV8thl1rohA1Ay+U76gkpFLm6Kf0pXavHEbkTsj9GJOMa6/QXLGo7QdByTjHX/oanW8GvZGBhexv7V4ABAEQTPnoSourqAAAAAElFTkSuQmCC"},b421:function(e,t,n){"use strict";n("1b5a")},b560:function(e,t,n){"use strict";n("d5fc")},b6bc:function(e,t,n){},b83d:function(e,t,n){},b860:function(e,t,n){},b917:function(e,t,n){},b9db:function(e,t,n){"use strict";n("a655")},bacb:function(e,t,n){"use strict";n("c74b")},c01d:function(e,t,n){},c32f:function(e,t,n){"use strict";n("9227")},c5a5:function(e,t,n){},c74b:function(e,t,n){},c75c:function(e,t,n){},c7dc:function(e,t,n){},c87d:function(e,t,n){e.exports=n.p+"img/seal.19fd9c11.png"},cd32:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAB1klEQVQ4jZ2UTUhUURTHf2NTCDqNymiS6EpwsH2IIuMH4iIhKRkh2+QiGF34hSvRTSouHMdVLUNIhHTTOhRNkj6MNmGuXKi4eCgS6YsZnJ6c5x2Z7m0U/cPjce85/989975zn2frcTP/UQPQrt5lKrwNLAFvgUXd4tXGQeAVUGeyKVfPc2AZiACbqWBWWmII+JIBoqtO5YZ0kFTyDvAZlszyKU8wBfIArwF/Rktm+ZXXI6BGoCo9tXjoBf6WR4Y7t7aeksmX+rR4GwUU1iOH87PkhTu4/aD1H0gg0sfh3IyxgDBu9FSWx4CC9NnkwT7xjR8EIr04ts3NuyUuxIqOYX/7bFCAPOmjOHDLCAHZwXsUD4+5J2BNjWOvfzJylBKytbgxreQNFLoQ52+SLN/FH1Qacg+o0AM5NSECXf1YsXGcRIKiwWE4SXK0umRAgF2vaqwKHVLYPeBC7K9n25HzuTM4gpM84Xjtgw76KFub1Wfzw0+xpifOIaI/39exoqPkP3lmlAO8kcOWhlzTe+kKktWqpSIHkGV+XQMink5hpO6a3OKHwO8rQCRXPD9lkH77V4D7qtTLJDmSKx5X+v9IKqsGmoA2dQ9LpeGAHWAVWADeqyM5E3AKRq96fViexL4AAAAASUVORK5CYII="},cd49:function(e,t,n){"use strict";n.r(t);n("e260"),n("e6cf"),n("cca6"),n("a79d");var a=n("7a23");function i(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("router-view"),s=Object(a["resolveComponent"])("a-config-provider");return Object(a["openBlock"])(),Object(a["createBlock"])(s,{locale:e.locale},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c)]})),_:1},8,["locale"])}var o=n("d4ec"),r=n("bee2"),c=n("257e"),s=n("262e"),l=n("2caf"),u=n("ade3"),d=(n("99af"),n("ce1f")),p=n("eb60"),h=n("5502"),f=Object(h["a"])({state:{AllNode:[],userInfo:{},permission:{application:!0}},mutations:{setNode:function(e,t){e.AllNode=t},setUser:function(e,t){e.userInfo=t.user},setPermission:function(e,t){e.permission=t.permission}},actions:{},modules:{}}),b=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"locale",p["a"]),e}return Object(r["a"])(n,[{key:"initALLNode",value:function(){this.$axios.get("/bpmmanage/queryAllUserByModelKey?modelKey=yy").then((function(e){var t=e.data;f.commit("setNode",t)}))}},{key:"mounted",value:function(){this.initALLNode()}}]),n}(d["b"]),m=(n("2ffd"),n("6b0d")),g=n.n(m);const v=g()(b,[["render",i]]);var j=v,y=n("6c02"),O={class:"home"},k={class:"main"},w={class:"main-left"},x=["src"];function N(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("Head"),s=Object(a["resolveComponent"])("Left");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",O,[Object(a["createVNode"])(c,{ref:"head",onPrint:e.handlePrint},null,8,["onPrint"]),Object(a["createElementVNode"])("div",k,[Object(a["createElementVNode"])("div",w,[Object(a["createVNode"])(s,{ref:"left"},null,512)]),Object(a["createElementVNode"])("div",{class:"main-right",style:Object(a["normalizeStyle"])(e.styleFlag?{backgroundColor:"#fdfdde"}:null)},[(Object(a["openBlock"])(),Object(a["createBlock"])(Object(a["resolveDynamicComponent"])(e.currentTabComponent),{ref:"right",itemData:e.itemData,key:e.randomKey},null,8,["itemData"]))],4),Object(a["createElementVNode"])("iframe",{src:e.printUrl,id:"printIframe",style:{display:"none"}},null,8,x)])])}n("d3b7"),n("25f0");var E=n("9ab4"),I={class:"head"},C={class:"admin"},D={key:0,class:"head-btn-con"},S=["onClick"],T=["title","innerHTML"],A={class:"rightBtnList"};function V(e,t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",I,[Object(a["createElementVNode"])("div",C,[Object(a["createElementVNode"])("p",{class:"head-name",onClick:t[0]||(t[0]=function(t){return e.queryTable("")})},"用印申请审批单")]),e.showApplication?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",D,[Object(a["createElementVNode"])("button",{id:"apply",class:"head-apply head-yellow",onClick:t[1]||(t[1]=function(t){return e.toApply()})},"申请"),Object(a["withDirectives"])(Object(a["createElementVNode"])("div",{class:"head-btn-tips"},Object(a["toDisplayString"])(e.applyCount),513),[[a["vShow"],e.applyCount>0]])])):Object(a["createCommentVNode"])("",!0),(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.aggregation,(function(t){return Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:t.taskId},["Apply"!=t.taskId?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{key:0,class:"head-btn-con head-list-con",onClick:function(n){return e.queryTable(t.taskId)}},[Object(a["createElementVNode"])("button",{title:t.taskName,innerHTML:t.taskName},null,8,T),Object(a["withDirectives"])(Object(a["createElementVNode"])("div",{class:"head-btn-tips"},Object(a["toDisplayString"])(t.number),513),[[a["vShow"],t.number]])],8,S)):Object(a["createCommentVNode"])("",!0)],64)})),128)),e.showSet?(Object(a["openBlock"])(),Object(a["createElementBlock"])("button",{key:1,id:"setUser",class:"head-yellow permission",onClick:t[2]||(t[2]=function(){return e.openSetting&&e.openSetting.apply(e,arguments)})},"权限配置")):Object(a["createCommentVNode"])("",!0),Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("div",A,[e.showSave?(Object(a["openBlock"])(),Object(a["createElementBlock"])("i",{key:0,title:"保存为草稿",id:"save",onClick:t[4]||(t[4]=function(){return e.save&&e.save.apply(e,arguments)}),class:"saveBtn"})):Object(a["createCommentVNode"])("",!0),e.showPrint?(Object(a["openBlock"])(),Object(a["createElementBlock"])("i",{key:1,id:"print",class:"printBtn",onClick:t[5]||(t[5]=function(t){return e.$emit("print")})})):Object(a["createCommentVNode"])("",!0),e.showSubmit?(Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{key:2,id:"saveAndSubmit",onClick:t[6]||(t[6]=function(t){return e.submit()}),class:"submitBtn"},"提交")):Object(a["createCommentVNode"])("",!0)])])}n("b0c0");var B=function(e){return Object(a["pushScopeId"])("data-v-7621fc59"),e=e(),Object(a["popScopeId"])(),e},$={class:"set"},L={class:"container"},U={class:"title"},F=B((function(){return Object(a["createElementVNode"])("span",{style:{"font-size":"30px"}}," 用印申请单",-1)})),_={class:"table"},R=B((function(){return Object(a["createElementVNode"])("colgroup",null,[Object(a["createElementVNode"])("col",{width:"145px"}),Object(a["createElementVNode"])("col",{width:"145px"}),Object(a["createElementVNode"])("col")],-1)})),M={id:"activity-body"},P=["onClick"],q={key:0,style:{position:"absolute",top:"4px",left:"22px",color:"red","font-size":"22px"}},z={class:"user-td",colspan:"2"},H=Object(a["createTextVNode"])("、"),W=["rowspan"],Q={colspan:"2"},Y={colspan:"2"};function G(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("Tag"),s=Object(a["resolveComponent"])("files");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",$,[Object(a["createElementVNode"])("div",L,[Object(a["createElementVNode"])("p",U,[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.jchcUser.groupName)+" ",1),F]),Object(a["createElementVNode"])("table",_,[R,Object(a["createElementVNode"])("tbody",M,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.setData.listActModelUsers,(function(t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("tr",{key:t.id},[Object(a["createElementVNode"])("td",null,[Object(a["createElementVNode"])("p",{onClick:function(n){return e.toPerson(t)},class:"activity",style:{position:"relative"}},["部门审核"==t.name||"职能部门"==t.name?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",q,"*")):Object(a["createCommentVNode"])("",!0),Object(a["createTextVNode"])(" "+Object(a["toDisplayString"])(t.name),1)],8,P)]),Object(a["createElementVNode"])("td",z,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.setData.actUsers[t.uuid],(function(n,i){return Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:i},[Object(a["createTextVNode"])(Object(a["toDisplayString"])(n.userName+"("+n.groupName+"/"+n.deptName+")")+" ",1),i+1!=e.setData.actUsers[t.uuid].length?(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:0},[H],64)):Object(a["createCommentVNode"])("",!0)])})),128))])])})),128)),Object(a["createElementVNode"])("tr",null,[Object(a["createElementVNode"])("td",{rowspan:e.setData.signetType.length+1},"印章配置",8,W)]),(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.setData.signetType,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("tr",{key:n},[Object(a["createElementVNode"])("td",null,Object(a["toDisplayString"])(t.name),1),Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(c,{value:t,uuid:t.uuid,onReflash:e.reflash},null,8,["value","uuid","onReflash"])])])})),128)),Object(a["createElementVNode"])("tr",null,[Object(a["createElementVNode"])("td",null,[e.uploadParams?(Object(a["openBlock"])(),Object(a["createBlock"])(s,{key:0,upload:!0,showUploadList:!1,params:{formId:e.jchcUser.groupId,kind:7},button:"文本模板",onUpload:e.handleUploadTextFile,codeTo:e.codeTo},null,8,["params","onUpload","codeTo"])):Object(a["createCommentVNode"])("",!0)]),Object(a["createElementVNode"])("td",Q,[e.uploadParams?(Object(a["openBlock"])(),Object(a["createBlock"])(s,{key:0,ref:"textFile",params:{formId:e.jchcUser.groupId,kind:7},remove:!0},null,8,["params"])):Object(a["createCommentVNode"])("",!0)])]),Object(a["createElementVNode"])("tr",null,[Object(a["createElementVNode"])("td",null,[e.uploadParams?(Object(a["openBlock"])(),Object(a["createBlock"])(s,{key:0,upload:!0,showUploadList:!1,params:{formId:e.jchcUser.groupId,kind:8},button:"规章制度",onUpload:e.handleUploadFile},null,8,["params","onUpload"])):Object(a["createCommentVNode"])("",!0)]),Object(a["createElementVNode"])("td",Y,[e.uploadParams?(Object(a["openBlock"])(),Object(a["createBlock"])(s,{key:0,ref:"rules",params:{formId:e.jchcUser.groupId,kind:8},remove:!0},null,8,["params"])):Object(a["createCommentVNode"])("",!0)])])])])])])}var K=n("cd32"),X=n.n(K),J=n("fbc1"),Z=n.n(J),ee={class:"tag"},te={class:"button-list"},ne=["onClick"],ae=["onClick"],ie={class:"add-container"};function oe(e,t,n,i,o,r){Object(a["resolveComponent"])("user-set-tree");return Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],null,[Object(a["createElementVNode"])("div",ee,[Object(a["createElementVNode"])("ul",te,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.value.child,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("li",{key:t.id},[Object(a["createElementVNode"])("p",{class:"button",onClick:function(n){return e.editType(t)}},Object(a["toDisplayString"])(t.name),9,ne),Object(a["createElementVNode"])("img",{src:X.a,class:"delete",onClick:function(a){return e.deleteType(t,n)},alt:""},null,8,ae)])})),128))]),Object(a["createElementVNode"])("div",ie,[Object(a["createElementVNode"])("img",{src:Z.a,onClick:t[0]||(t[0]=function(){return e.addType&&e.addType.apply(e,arguments)}),alt:""})])]),(Object(a["openBlock"])(),Object(a["createBlock"])(a["Teleport"],{to:"body"},[Object(a["createCommentVNode"])("",!0)]))],64)}var re=n("1da1"),ce=(n("96cf"),n("a434"),n("f86c")),se=n.n(ce),le=function(e){return Object(a["pushScopeId"])("data-v-16b84483"),e=e(),Object(a["popScopeId"])(),e},ue={class:"set-tree"},de=le((function(){return Object(a["createElementVNode"])("div",{class:"title",style:{}},"用印申请单权限配置11",-1)})),pe={class:"activity"},he=le((function(){return Object(a["createElementVNode"])("p",{class:"activity-name beixuan"},"备选人员",-1)})),fe={class:"tree-con"},be={class:"activity"},me=le((function(){return Object(a["createElementVNode"])("p",{class:"activity-name yixuan"},"已选人员",-1)})),ge={class:"tree-con",id:"userList"},ve=["uuid","userId"],je=["onClick"];function ye(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-ztree");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",ue,[de,Object(a["createElementVNode"])("div",pe,[he,Object(a["createElementVNode"])("div",fe,[Object(a["createVNode"])(c,{settings:e.settings,onOnCheck:e.onCheck,onOnCreated:e.onCreated},null,8,["settings","onOnCheck","onOnCreated"])])]),Object(a["createElementVNode"])("div",be,[me,Object(a["createElementVNode"])("div",ge,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.checkedUsers,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{class:"user-list",key:t.uuid,uuid:t.uuid,userId:t.userId},[Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(t.userName),1),Object(a["createElementVNode"])("img",{onClick:function(a){return e.deleteUser(t,n,null)},src:se.a},null,8,je)],8,ve)})),128))])])])}n("ac1f"),n("5319");var Oe=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"checkedUsers",[]),Object(u["a"])(Object(c["a"])(e),"addUsers",void 0),Object(u["a"])(Object(c["a"])(e),"fieldType",void 0),Object(u["a"])(Object(c["a"])(e),"actId",void 0),Object(u["a"])(Object(c["a"])(e),"title",""),Object(u["a"])(Object(c["a"])(e),"zTree",{}),Object(u["a"])(Object(c["a"])(e),"settings",{async:{enable:!0,autoParam:["id"],type:"get",url:"",dataFilter:function(t,n,a){for(var i=a.data,o=0,r=i.length;o<r;o++){i[o].name=i[o].name.replace(/\.n/g,".");for(var c=0;c<e.checkedUsers.length;c++)i[o].id===e.checkedUsers[c].userId&&(i[o].checked=!0)}return i}},check:{enable:!0,autoCheckTrigger:!0,chkStyle:"checkbox",radioType:"all",chkboxType:{Y:"s",N:"ps"}},data:{key:{},simpleData:{enable:!0,idKey:"id",pIdKey:"Pid",rootPId:"-1"}}}),e}return Object(r["a"])(n,[{key:"beforeCreate",value:function(){this.settings.async.url="/plyh/qsecurity/zTreeQSecurity"}},{key:"created",value:function(){this.checkedUsers=this.addUsers}},{key:"requestUrlHandler",get:function(){var e=this;return function(t){var n={INSERT:{defConfigUser:"/activityuser/insert",supervisors:"/activityuser/insertSupervisors"},DELETE:{defConfigUser:"/activityuser/deleteSignetActUser/",supervisors:"/activityuser/deleteSupervisor/"}};return n[t][e.fieldType]}}},{key:"onCreated",value:function(e){this.zTree=e}},{key:"deleteUser",value:function(e,t,n){var a=this;this.$logo(!0),this.$axios.get(this.requestUrlHandler("DELETE")+e.uuid).then((function(i){if(a.$logo(!1),0!==i.code)return a.$toast(i.msg),!1;a.checkedUsers.splice(t,1);var o=a.zTree.getNodeByParam("id",e.userId);null==o||n||a.zTree.checkNode(o)})).catch((function(e){e&&a.$toast(e.toString())}))}},{key:"onCheck",value:function(e,t,n){var a=this;if(n.checked)this.$logo(!0),this.$axios.post(this.requestUrlHandler("INSERT"),{signetId:this.actId,userId:n.id,userName:n.name,deptId:n.extAttrs.deptId,deptName:n.extAttrs.deptName,groupId:n.extAttrs.orgId,groupName:n.extAttrs.orgName}).then((function(e){if(a.$logo(!1),0!==e.code)return a.$toast(e.msg),!1;a.checkedUsers.push({uuid:e.data,userId:n.id,userName:n.name,deptId:n.extAttrs.deptId,groupId:n.extAttrs.orgId,deptName:n.extAttrs.deptName,groupName:n.extAttrs.orgName,signetId:a.actId})})).catch((function(e){e&&a.$toast(e.toString())}));else for(var i=0;i<this.checkedUsers.length;i++){var o=this.checkedUsers[i];o.userId===n.id&&this.deleteUser(o,i,!0)}}}]),n}(d["b"]);Oe=Object(E["a"])([Object(d["a"])({components:{},props:{fieldType:String,actId:String,addUsers:Array}})],Oe);var ke=Oe;n("7d1f");const we=g()(ke,[["render",ye],["__scopeId","data-v-16b84483"]]);var xe=we,Ne=(n("a15b"),n("d81d"),function(e){return Object(a["pushScopeId"])("data-v-4e9f2c04"),e=e(),Object(a["popScopeId"])(),e}),Ee={class:"add-company"},Ie={class:"input-con"},Ce=Ne((function(){return Object(a["createElementVNode"])("p",{class:"title"},"印章名称：",-1)})),De={class:"input-con"},Se=Ne((function(){return Object(a["createElementVNode"])("p",{class:"title"},"印章保管：",-1)})),Te=["value"],Ae={class:"input-con"},Ve=Ne((function(){return Object(a["createElementVNode"])("p",{class:"title"},"监印人：",-1)})),Be=["value"],$e={class:"input-con"},Le=Ne((function(){return Object(a["createElementVNode"])("p",{class:"title",style:{"margin-left":"14px"}},"电子章：",-1)})),Ue={class:"input-con"},Fe=Ne((function(){return Object(a["createElementVNode"])("p",{class:"title"},"上传印模：",-1)})),_e={class:"signet-model"},Re={class:"files"},Me=Object(a["createTextVNode"])(" 上传印模 "),Pe={class:"btn-con"};function qe(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("el-switch"),s=Object(a["resolveComponent"])("vue-picture-cropper");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Ee,[Object(a["createElementVNode"])("div",Ie,[Ce,Object(a["withDirectives"])(Object(a["createElementVNode"])("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=function(t){return e.insertValue=t}),placeholder:"请输入印章名称",maxlength:"20"},null,512),[[a["vModelText"],e.insertValue]])]),Object(a["createElementVNode"])("div",De,[Se,Object(a["createElementVNode"])("input",{class:"input-save",type:"text",onClick:t[1]||(t[1]=function(t){return e.addType("defConfigUser")}),value:e.defConfigUser.length?e.defConfigUser.map((function(e){return e.name})).join(","):"",placeholder:"请选择印章保管"},null,8,Te)]),Object(a["createElementVNode"])("div",Ae,[Ve,Object(a["createElementVNode"])("input",{class:"input-save",type:"text",onClick:t[2]||(t[2]=function(t){return e.addType("supervisors")}),value:e.supervisors.length?e.supervisors.map((function(e){return e.name})).join(","):"",placeholder:"请选择监印人"},null,8,Be)]),Object(a["createElementVNode"])("div",$e,[Le,Object(a["createVNode"])(c,{modelValue:e.switchvalue,"onUpdate:modelValue":t[3]||(t[3]=function(t){return e.switchvalue=t}),"active-color":"#13ce66","inactive-color":"#ff4949","active-text":"有","inactive-text":"无",class:"switch"},null,8,["modelValue"])]),Object(a["createElementVNode"])("div",Ue,[Fe,Object(a["createElementVNode"])("div",_e,[Object(a["createVNode"])(s,{boxStyle:{width:"275px",height:"275px",backgroundColor:"#f8f8f8",margin:"right",aspectRatio:1},key:e.randomkey,img:e.picture,options:{viewMode:1,dragMode:"crop",aspectRatio:1,preview:"preview",autoCropArea:1}},null,8,["img"]),Object(a["createElementVNode"])("div",Re,[Object(a["createElementVNode"])("input",{style:{opacity:"0"},type:"file",onChange:t[4]||(t[4]=function(){return e.file&&e.file.apply(e,arguments)}),accept:"image/*",value:""},null,32),Me])])]),Object(a["createElementVNode"])("div",Pe,[Object(a["createElementVNode"])("p",{onClick:t[5]||(t[5]=function(){return e.enterType&&e.enterType.apply(e,arguments)})},"保存")])])}var ze=function(e){return Object(a["pushScopeId"])("data-v-43696786"),e=e(),Object(a["popScopeId"])(),e},He={class:"set-tree"},We=ze((function(){return Object(a["createElementVNode"])("div",{class:"title",style:{}},"用印申请单印章配置",-1)})),Qe={class:"activity"},Ye=ze((function(){return Object(a["createElementVNode"])("p",{class:"activity-name beixuan"},"备选人员",-1)})),Ge={class:"tree-con"},Ke={class:"activity"},Xe=ze((function(){return Object(a["createElementVNode"])("p",{class:"activity-name yixuan"},"已选人员",-1)})),Je={class:"tree-con",id:"userList"},Ze=["uuid","userId"],et=["onClick"];function tt(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-ztree");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",He,[We,Object(a["createElementVNode"])("div",Qe,[Ye,Object(a["createElementVNode"])("div",Ge,[Object(a["createVNode"])(c,{settings:e.settings,onOnCheck:e.onCheck,onOnCreated:e.onCreated,ref:"ztree"},null,8,["settings","onOnCheck","onOnCreated"])])]),Object(a["createElementVNode"])("div",Ke,[Xe,Object(a["createElementVNode"])("div",Je,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.checkedUsers,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{class:"user-list",key:t.uuid,uuid:t.uuid,userId:t.userId},[Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(t.name),1),Object(a["createElementVNode"])("img",{onClick:function(a){return e.deleteUser(t,n,null)},src:se.a,alt:""},null,8,et)],8,Ze)})),128))])])])}n("159b"),n("c740");var nt=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"code",void 0),Object(u["a"])(this,"actId",void 0),Object(u["a"])(this,"title",void 0),Object(u["a"])(this,"type",void 0),Object(u["a"])(this,"outData",void 0),Object(u["a"])(this,"addUsers",void 0)},at=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"checkedUsers",[]),Object(u["a"])(Object(c["a"])(e),"zTree",{}),Object(u["a"])(Object(c["a"])(e),"settings",{async:{enable:!0,autoParam:["id"],type:"get",url:"",dataFilter:function(t,n,a){var i=a.data;if(console.log(i,"nodesnodes"),1==e.type)for(var o=0,r=i.length;o<r;o++){i[o].name=i[o].name.replace(/\.n/g,".");for(var c=0;c<e.checkedUsers.length;c++)i[o].id===e.checkedUsers[c].userId&&(i[o].checked=!0)}else for(var s=0,l=i.length;s<l;s++){i[s].name=i[s].name.replace(/\.n/g,".");for(var u=0;u<e.addUsers.length;u++)i[s].id===e.addUsers[u].id&&(i[s].checked=!0)}return i}},check:{enable:!0,autoCheckTrigger:!0,chkStyle:"yyjl"===e.code?"radio":"checkbox",radioType:"all",chkboxType:{Y:"s",N:"ps"}},data:{key:{},simpleData:{enable:!0,idKey:"id",pIdKey:"Pid",rootPId:"-1"}}}),e}return Object(r["a"])(n,[{key:"beforeCreate",value:function(){this.settings.async.url="/plyh/qsecurity/zTreeQSecurity"}},{key:"created",value:function(){1==this.type?this.initCheckedUser():this.checkedUsers=this.addUsers}},{key:"onCreated",value:function(e){this.zTree=e}},{key:"initCheckedUser",value:function(){var e=this;if(!this.actId)return!1;this.$axios.get("/defConfigUser/getActUsersBySignetId?signetId="+this.actId).then((function(t){return e.$logo(!1),0!==t.code?(e.$toast(t.msg),!1):(e.checkedUsers=t.data,e.checkedUsers.forEach((function(e){e.name=e.userName,e.id=e.userId})),e.$refs.ztree.initTree(),!1)})).catch((function(t){t&&e.$toast(t.toString())}))}},{key:"deleteUser",value:function(e,t,n){var a=this;if(1===this.type){if(!this.actId)return;this.$logo(!0),this.$axios.get("/defConfigUser/deleteSignetActUser/"+e.uuid).then((function(i){if(a.$logo(!1),0!==i.code)return a.$toast(i.msg),!1;a.checkedUsers.splice(t,1);var o=a.zTree.getNodeByParam("id",e.id);null==o||n||a.zTree.checkNode(o)})).catch((function(e){a.$toast(e.toString())}))}else{var i=this.zTree.getNodeByParam("id",e.id);null!=i&&this.zTree.checkNode(i),this.checkedUsers.splice(t,1)}}},{key:"getList",value:function(){var e=this;this.$axios.get("/defConfigUser/getActUsersBySignetId?signetId="+this.actId).then((function(t){return e.$logo(!1),0!==t.code?(e.$toast(t.msg),!1):(e.checkedUsers=t.data,e.checkedUsers.forEach((function(e){e.name=e.userName,e.id=e.userId})),!1)})).catch((function(t){t&&e.$toast(t.toString())}))}},{key:"onCheck",value:function(e,t,n){var a=this;if(1==this.type)if(n.checked){var i=this.checkedUsers.findIndex((function(e){return e.id==n.id}));if(-1==i){if(this.checkedUsers.push({id:n.id,userId:n.id,name:n.name}),!this.actId)return;this.$logo(!0),this.$axios.post("/activityuser/insert",{signetId:this.actId,userId:n.id,userName:n.name,deptId:n.extAttrs.deptId,deptName:n.extAttrs.deptName,groupId:n.extAttrs.orgId,groupName:n.extAttrs.orgName}).then((function(e){if(a.$logo(!1),a.getList(),0!==e.code)return a.$toast(e.msg),!1})).catch((function(e){a.$toast(e.toString())}))}}else{var o=this.checkedUsers.findIndex((function(e){return e.id==n.id}));-1!=o&&(this.deleteUser(this.checkedUsers[o],o,!0),this.getList())}else if(n.checked)this.outData.push({checked:!1,deptId:n.extAttrs.deptId,deptName:n.extAttrs.deptName,groupId:n.extAttrs.orgId,groupName:n.extAttrs.orgName,id:n.id,name:n.name});else{var r=this.checkedUsers.findIndex((function(e){return e.id==n.id}));-1!=r&&this.checkedUsers.splice(r,1)}}}]),n}(d["b"].with(nt));n("f5b1");const it=g()(at,[["render",tt],["__scopeId","data-v-43696786"]]);var ot=it,rt=n("92ec"),ct=n.n(rt),st=n("5c12"),lt=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"outData",void 0),Object(u["a"])(this,"value",void 0),Object(u["a"])(this,"dialog",void 0)},ut=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"insertValue",""),Object(u["a"])(Object(c["a"])(e),"defConfigUser",[]),Object(u["a"])(Object(c["a"])(e),"supervisors",[]),Object(u["a"])(Object(c["a"])(e),"model",!1),Object(u["a"])(Object(c["a"])(e),"randomkey",1e3*Math.random()),Object(u["a"])(Object(c["a"])(e),"modelSrc",""),Object(u["a"])(Object(c["a"])(e),"crap",!1),Object(u["a"])(Object(c["a"])(e),"previews",{}),Object(u["a"])(Object(c["a"])(e),"picture",void 0),Object(u["a"])(Object(c["a"])(e),"form",void 0),Object(u["a"])(Object(c["a"])(e),"switchvalue",!1),e}return Object(r["a"])(n,[{key:"handleUploadFile",value:function(e){this.$refs.rules.pushFile(e)}},{key:"file",value:function(e){var t=this,n=new FileReader;n.readAsDataURL(e.target.files[0]),n.onloadend=function(e){t.picture=e.target.result,t.randomkey=1e3*Math.random()}}},{key:"addType",value:function(e){this.$dialog({title:"印章配置",max:!1,width:580,height:495,content:{component:ot,props:{addUsers:this[e],type:0,actId:"",title:this.insertValue,outData:this[e]}}})}},{key:"enterType",value:function(){var e=this;if(!this.insertValue)return this.$toast("请填写印章名称"),!1;if(!this.defConfigUser.length)return this.$toast("请选择盖章人"),!1;if(!this.picture)return this.$toast("请上传印模"),!1;var t=function(e){return e.map((function(e){return{userId:e.id,userName:e.name,deptId:e.deptId,groupId:e.groupId}}))},n={parent:this.value.uuid,parentName:this.value.name,name:this.insertValue,kind:this.value.kind,elecSeal:this.switchvalue,defConfigUser:t(this.defConfigUser),supervisors:t(this.supervisors)};this.$axios.post("/dictionary/insert",n).then((function(t){var a=rt["cropper"].getBlob();if(0!=t.code)return e.$toast(t.msg),!1;e.defConfigUser=[],e.insertValue="",e.value.child.push({name:n.name,uuid:t.data});var i=new FormData;i.append("formId",t.data),i.append("fileContent",a,(new Date).getTime()+".png"),i.append("kind",3);var o={headers:{"Content-Type":"multipart/form-data"}};e.$axios.post("/jchcfile/upload",i,o).then((function(t){0===t.code?e.dialog._ok():e.$toast(t.msg)})).catch((function(t){return e.$toast(t.toString())})),e.dialog._ok(),e.$toast("配置成功")}))}}]),n}(d["b"].with(lt));ut=Object(E["a"])([Object(d["a"])({components:{VuePictureCropper:ct.a,ElSwitch:st["a"]}})],ut);var dt=ut;n("2db4");const pt=g()(dt,[["render",qe],["__scopeId","data-v-4e9f2c04"]]);var ht=pt,ft=function(e){return Object(a["pushScopeId"])("data-v-16a6fa9c"),e=e(),Object(a["popScopeId"])(),e},bt={class:"add-company"},mt={class:"input-con"},gt=ft((function(){return Object(a["createElementVNode"])("p",{class:"title"},"印章名称：",-1)})),vt={class:"input-con"},jt=ft((function(){return Object(a["createElementVNode"])("p",{class:"title"},"印鉴保管：",-1)})),yt=["value"],Ot={class:"input-con"},kt=ft((function(){return Object(a["createElementVNode"])("p",{class:"title"},"监印人：",-1)})),wt=["value"],xt={class:"input-con"},Nt=ft((function(){return Object(a["createElementVNode"])("p",{class:"title",style:{"margin-left":"14px"}},"电子章：",-1)})),Et={class:"input-con"},It=ft((function(){return Object(a["createElementVNode"])("p",{class:"title",style:{"margin-top":"4px","vertical-align":"top"}},"上传印模：",-1)})),Ct={class:"signetImg"},Dt=["src"];function St(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("el-switch");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",bt,[Object(a["createElementVNode"])("div",mt,[gt,Object(a["withDirectives"])(Object(a["createElementVNode"])("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=function(t){return e.value.name=t}),placeholder:"请输入印章名称",disabled:!0,maxlength:"20"},null,512),[[a["vModelText"],e.value.name]])]),Object(a["createElementVNode"])("div",vt,[jt,Object(a["createElementVNode"])("input",{class:"input-save",type:"text",onClick:t[1]||(t[1]=function(t){return e.addType("defConfigUser")}),value:e.userNameHanlder("defConfigUser"),placeholder:"请选择印鉴保管"},null,8,yt)]),Object(a["createElementVNode"])("div",Ot,[kt,Object(a["createElementVNode"])("input",{class:"input-save",type:"text",onClick:t[2]||(t[2]=function(t){return e.addType("supervisors")}),value:e.userNameHanlder("supervisors"),placeholder:"请选择监印人"},null,8,wt)]),Object(a["createElementVNode"])("div",xt,[Nt,Object(a["createVNode"])(c,{modelValue:e.value.electronicSeal,"onUpdate:modelValue":t[3]||(t[3]=function(t){return e.value.electronicSeal=t}),"active-color":"#13ce66","inactive-color":"#ff4949","active-text":"有","inactive-text":"无",class:"switch",onChange:e.changeswitch},null,8,["modelValue","onChange"])]),Object(a["createElementVNode"])("div",Et,[It,Object(a["createElementVNode"])("div",Ct,[Object(a["createElementVNode"])("img",{style:{width:"100%",height:"100%"},src:e.picture,alt:""},null,8,Dt)])])])}var Tt=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"outData",void 0),Object(u["a"])(this,"value",void 0),Object(u["a"])(this,"dialog",void 0)},At=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"defConfigUser",[]),Object(u["a"])(Object(c["a"])(e),"supervisors",[]),Object(u["a"])(Object(c["a"])(e),"submitModel",!1),Object(u["a"])(Object(c["a"])(e),"deleteModel",!0),Object(u["a"])(Object(c["a"])(e),"randomkey",1e3*Math.random()),Object(u["a"])(Object(c["a"])(e),"picture",void 0),e}return Object(r["a"])(n,[{key:"created",value:function(){var e=this;this.$axios.get("/activityuser/getSupervisorsAndDefBySignetId?signetId="+this.value.uuid).then((function(t){return e.$logo(!1),0!==t.code?(e.$toast(t.msg),!1):(e.defConfigUser=t.data.actUsersList,e.supervisors=t.data.supervisors,!1)})).catch((function(t){t&&e.$toast(t.toString())})),this.value.files.length?this.picture="http://**************:8086/seal/jchcfile/download/"+this.value.files[0].uuid:(this.picture="",this.submitModel=!0,this.deleteModel=!1)}},{key:"userNameHanlder",get:function(){var e=this;return function(t){return e[t].length?e[t].map((function(e){return e.userName})).join(","):""}}},{key:"handleUploadFile",value:function(e){this.$refs.rules.pushFile(e)}},{key:"addType",value:function(e){this.$dialog({title:"印章配置",max:!1,width:580,height:495,content:{component:xe,props:{fieldType:e,actId:this.value.uuid,addUsers:this[e]}}})}},{key:"changeswitch",value:function(){var e=this,t={uuid:this.value.uuid,electronicSeal:this.value.electronicSeal};this.$axios.post("/dictionary/update",t).then((function(t){if(0!=t.code)return e.$toast(t.msg),!1;e.$toast("修改电子章状态成功")}))}}]),n}(d["b"].with(Tt));At=Object(E["a"])([Object(d["a"])({components:{VuePictureCropper:ct.a,ElSwitch:st["a"]}})],At);var Vt=At;n("2b35");const Bt=g()(Vt,[["render",St],["__scopeId","data-v-16a6fa9c"]]);var $t=Bt,Lt=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"uuid",void 0),Object(u["a"])(this,"value",void 0)},Ut=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"insertValue",""),Object(u["a"])(Object(c["a"])(e),"addUsers",[]),Object(u["a"])(Object(c["a"])(e),"checkedUsers",[]),Object(u["a"])(Object(c["a"])(e),"list",[]),Object(u["a"])(Object(c["a"])(e),"type",0),e}return Object(r["a"])(n,[{key:"created",value:function(){this.value["userList"]=[]}},{key:"addType",value:function(){var e=this;this.$dialog({title:"印章配置",max:!1,width:400,height:510,content:{component:ht,handle:!0,props:{value:this.value,outData:[]}},ok:function(){return e.$emit("reflash",1),!0}})}},{key:"editType",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(t){var n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$dialog({title:"修改印章配置",max:!1,width:400,height:450,content:{component:$t,handle:!0,props:{value:t,outData:[]}},ok:function(){return n.$emit("reflash",1),!0},close:function(){return n.$emit("reflash",1),!0}});case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"toPerson",value:function(e,t){this.$dialog({title:"配置流程人员",max:!1,width:580,height:495,content:{component:xe,props:{addUsers:this.addUsers,type:0,actId:"",title:this.insertValue,outData:this.addUsers}}})}},{key:"deleteType",value:function(e,t){var n=this;this.$alert({content:"您确认要删除吗?",lock:!0,ok:function(){var a=Object(re["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return n.$axios.get("/dictionary/delete?id="+e.uuid).then((function(e){if(0!=e.code)return n.$toast(e.msg),!1;n.value.child.splice(t,1)})),a.abrupt("return",!0);case 2:case"end":return a.stop()}}),a)})));function i(){return a.apply(this,arguments)}return i}()})}}]),n}(d["b"].with(Lt));Ut=Object(E["a"])([Object(d["a"])({components:{UserSetTree:xe},watch:{addUsers:{deep:!0,handler:function(e){}}}})],Ut);var Ft=Ut;n("d19a");const _t=g()(Ft,[["render",oe],["__scopeId","data-v-46b5d9ad"]]);var Rt=_t,Mt=n("53ca"),Pt=n("5530"),qt=(n("4d63"),n("c607"),n("2c3e"),n("466d"),n("841c"),n("b64b"),n("498a"),n("bc3a")),zt=n.n(qt),Ht={key:0,class:"mask"},Wt=["id"],Qt={key:1,class:"btn"};function Yt(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"dialog-container",style:Object(a["normalizeStyle"])({top:e.pxTop+"px",left:e.pxLeft+"px",width:e.pxWidth+"px",height:e.pxHeight+"px"})},[e.lock?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Ht)):Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("div",{class:"title",onMousedown:t[2]||(t[2]=function(t){return e.mousedown(t)})},[Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(e.title),1),e.closeButton?(Object(a["openBlock"])(),Object(a["createElementBlock"])("i",{key:0,class:"close-dialog",onClick:t[0]||(t[0]=function(t){return e._close(null)})})):Object(a["createCommentVNode"])("",!0),e.max?(Object(a["openBlock"])(),Object(a["createElementBlock"])("i",{key:1,class:Object(a["normalizeClass"])(["size-icon",{full:e.isFull}]),onClick:t[1]||(t[1]=function(){return e.changeSize&&e.changeSize.apply(e,arguments)})},null,2)):Object(a["createCommentVNode"])("",!0)],32),Object(a["createElementVNode"])("div",{id:e.id,class:Object(a["normalizeClass"])(e.customClass)},null,10,Wt),e.shwoBottom?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Qt,[e.ok?(Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{key:0,class:"submit",onClick:t[3]||(t[3]=function(){return e._ok&&e._ok.apply(e,arguments)})},"确定")):Object(a["createCommentVNode"])("",!0),e.close?(Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{key:1,class:"cancel",onClick:t[4]||(t[4]=function(){return e._close&&e._close.apply(e,arguments)})},"关闭")):Object(a["createCommentVNode"])("",!0)])):Object(a["createCommentVNode"])("",!0)],4)}n("8a79");var Gt=n("f23d");function Kt(e){e.config.globalProperties.$axios={getCrossOrigin:function(e,t){return new Promise((function(n,a){zt.a.get(e,t).then((function(e){n(e.data)})).catch((function(e){a(e)}))}))},get:function(e,t){return new Promise((function(n,a){zt.a.get(ba.rewrite(e),t).then((function(e){n(ba.rewriteResponse(e.data))})).catch((function(e){a(e)}))}))},post:function(e,t,n){return new Promise((function(a,i){zt.a.post(ba.rewrite(e),t,n).then((function(e){a(ba.rewriteResponse(e.data))})).catch((function(e){i(e)}))}))}}}function Xt(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"toast",style:Object(a["normalizeStyle"])({top:e.top+"px",left:e.left+"px"})},Object(a["toDisplayString"])(e.msg),5)}var Jt=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"msg",Object(d["c"])({default:""})),Object(u["a"])(this,"seconds",Object(d["c"])({default:2}))},Zt=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"top",0),Object(u["a"])(Object(c["a"])(e),"left",0),e}return Object(r["a"])(n,[{key:"position",value:function(){var e=document.documentElement.clientWidth,t=document.documentElement.clientHeight,n=this.$el.offsetWidth,a=this.$el.offsetHeight;this.top=(t-a)/2,this.left=(e-n)/2}},{key:"mounted",value:function(){var e=this;document.body.appendChild(this.$el),this.$nextTick((function(){e.position()})),window.onresize=function(){return e.position()},setTimeout((function(){document.body.removeChild(e.$el)}),1e3*this.seconds)}}]),n}(d["b"].with(Jt));n("bacb");const en=g()(Zt,[["render",Xt],["__scopeId","data-v-0e231b0f"]]);var tn=en,nn={key:0,class:"mask"},an={class:"content"},on={class:"btn"};function rn(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"alert-container",style:Object(a["normalizeStyle"])({top:e.top+"px",left:e.left+"px"})},[e.lock?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",nn)):Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("div",{class:"title",onMousedown:t[1]||(t[1]=function(t){return e.mousedown(t)})},[Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(e.title),1),Object(a["createElementVNode"])("i",{class:"close-alert",onClick:t[0]||(t[0]=function(){return e._close&&e._close.apply(e,arguments)})})],32),Object(a["createElementVNode"])("div",an,Object(a["toDisplayString"])(e.content),1),Object(a["createElementVNode"])("div",on,[Object(a["createElementVNode"])("p",{class:"cancel",onClick:t[2]||(t[2]=function(){return e._close&&e._close.apply(e,arguments)})},"关闭"),e.ok?(Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{key:0,class:"submit",onClick:t[3]||(t[3]=function(){return e._ok&&e._ok.apply(e,arguments)})},"确定")):Object(a["createCommentVNode"])("",!0)])],4)}var cn=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"title",Object(d["c"])({default:"提示"})),Object(u["a"])(this,"content",Object(d["c"])({default:""})),Object(u["a"])(this,"ok",void 0),Object(u["a"])(this,"close",void 0)},sn=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"lock",void 0),Object(u["a"])(Object(c["a"])(e),"top",0),Object(u["a"])(Object(c["a"])(e),"left",0),Object(u["a"])(Object(c["a"])(e),"width",0),Object(u["a"])(Object(c["a"])(e),"height",0),Object(u["a"])(Object(c["a"])(e),"clientWidth",0),Object(u["a"])(Object(c["a"])(e),"clientHeight",0),Object(u["a"])(Object(c["a"])(e),"moveX",0),Object(u["a"])(Object(c["a"])(e),"moveY",0),e}return Object(r["a"])(n,[{key:"_ok",value:function(){this.ok&&!this.ok()||this.$el.remove()}},{key:"_close",value:function(){this.close&&!this.close()||this.$el.remove()}},{key:"mousedown",value:function(e){var t=this;this.moveX=e.pageX,this.moveY=e.pageY,document.onselectstart=function(){return!1},document.onmousemove=function(e){var n=t.left+e.pageX-t.moveX,a=t.top+e.pageY-t.moveY;n>0&&n+t.width<t.clientWidth&&(t.left=n),a>0&&a+t.height<t.clientHeight&&(t.top=a),t.moveX=e.pageX,t.moveY=e.pageY},document.onmouseup=function(e){document.onmousemove=document.onmouseup=document.onselectstart=null}}},{key:"position",value:function(){this.clientWidth=document.documentElement.clientWidth,this.clientHeight=document.documentElement.clientHeight,this.width=this.$el.offsetWidth,this.height=this.$el.offsetHeight,this.left=(this.clientWidth-this.width)/2,this.top=(this.clientHeight-this.height)/2}},{key:"mounted",value:function(){var e=this;document.body.appendChild(this.$el),this.$nextTick((function(){e.position()})),window.onresize=function(){return e.position()}}}]),n}(d["b"].with(cn));n("5a38");const ln=g()(sn,[["render",rn],["__scopeId","data-v-6532b5fa"]]);var un=ln,dn={class:"logo"},pn={class:"cover"};function hn(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",null,[Object(a["withDirectives"])(Object(a["createElementVNode"])("div",dn,null,512),[[a["vShow"],e.show]]),Object(a["withDirectives"])(Object(a["createElementVNode"])("div",pn,null,512),[[a["vShow"],e.show&&e.cover]])])}var fn=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"show",Object(d["c"])({default:!0})),Object(u["a"])(this,"cover",Object(d["c"])({default:!0}))},bn=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){return Object(o["a"])(this,n),t.apply(this,arguments)}return Object(r["a"])(n,[{key:"mounted",value:function(){document.body.appendChild(this.$el)}},{key:"unmounted",value:function(){document.body.removeChild(this.$el)}}]),n}(d["b"].with(fn));n("a36c");const mn=g()(bn,[["render",hn],["__scopeId","data-v-6cf30c86"]]);var gn=mn,vn=["id"];function jn(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"ztree",id:e.zTreeId},null,8,vn)}var yn=n("1157"),On=n.n(yn);window.$=On.a,window.jQuery=On.a;var kn=On.a,wn=(n("ee51"),n("2654"),function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"zTreeId","zTreeId_"+Math.round(1e10*Math.random())),Object(u["a"])(Object(c["a"])(e),"nodes",void 0),Object(u["a"])(Object(c["a"])(e),"settings",void 0),Object(u["a"])(Object(c["a"])(e),"zTreeSettings",void 0),Object(u["a"])(Object(c["a"])(e),"zTreeIns",void 0),e}return Object(r["a"])(n,[{key:"beforeCreate",value:function(){var e=this;this.zTreeSettings={callback:{onAsyncError:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onAsyncError"].concat(n))},onAsyncSuccess:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onAsyncSuccess"].concat(n))},onCheck:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onCheck"].concat(n))},onClick:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onClick"].concat(n))},onCollapse:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onCollapse"].concat(n))},onDblClick:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onDblClick"].concat(n))},onDrag:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onDrag"].concat(n))},onDragMove:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onDragMove"].concat(n))},onDrop:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onDrop"].concat(n))},onExpand:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onExpand"].concat(n))},onMouseDown:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onMouseDown"].concat(n))},onMouseUp:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onMouseUp"].concat(n))},onRemove:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onRemove"].concat(n))},onRename:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onRename"].concat(n))},onRightClick:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["onRightClick"].concat(n))},beforeCheck:function(){for(var t=arguments.length,n=new Array(t),a=0;a<t;a++)n[a]=arguments[a];e.$emit.apply(e,["beforeCheck"].concat(n))}}}}},{key:"mounted",value:function(){this.initTree()}},{key:"initTree",value:function(){var e=this;this.zTreeIns&&this.zTreeIns.destroy(),this.$nextTick((function(){e.zTreeIns=kn.fn.zTree.init(kn("#"+e.zTreeId),Object.assign({},e.zTreeSettings,e.settings),e.nodes),e.$emit("onCreated",e.zTreeIns)}))}}]),n}(d["b"]));wn=Object(E["a"])([Object(d["a"])({props:{nodes:{type:Array,default:[]},settings:{type:Object,default:{}}},emits:["onCreated","onAsyncError","onAsyncSuccess","onCheck","onClick","onCollapse","onDblClick","onDrag","onDragMove","onDrop","onExpand","onMouseDown","onMouseUp","onRemove","onRename","onRightClick","beforeCheck"],watch:{nodes:{handler:function(e,t){this.initTree()},deep:!0},settings:{handler:function(e,t){this.initTree()},deep:!0}}})],wn);var xn=wn;const Nn=g()(xn,[["render",jn]]);var En=Nn,In={class:"v-column-slot"},Cn=["width"];function Dn(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-table-body");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",null,[Object(a["createElementVNode"])("div",In,[Object(a["renderSlot"])(e.$slots,"default",{},void 0,!0)]),Object(a["createElementVNode"])("table",{class:Object(a["normalizeClass"])(["v-table",{"v-table__border":e.border,"v-table__stripe":e.stripe,"v-table__hover":e.hover}]),style:Object(a["normalizeStyle"])({width:e.width})},[Object(a["createElementVNode"])("colgroup",null,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.columns,(function(e,t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("col",{key:e.label+t,width:e.width},null,8,Cn)})),128))]),Object(a["createElementVNode"])("thead",null,[Object(a["createElementVNode"])("tr",null,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.columns,(function(e,t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("th",{key:e.label+t},Object(a["toDisplayString"])(e.label),1)})),128))])]),Object(a["createVNode"])(c,{columns:e.columns,dataList:e.dataList,rowClass:e.rowClass,rowStyle:e.rowStyle,pageNo:e.pageNo,pageSize:e.pageSize,onRowClick:e.rowClick,onRowDblclick:e.rowDblclick},null,8,["columns","dataList","rowClass","rowStyle","pageNo","pageSize","onRowClick","onRowDblclick"])],6)])}n("a9e3");var Sn=Object(a["defineComponent"])({name:"vue-table-body",props:{columns:{type:Array,default:[]},dataList:{type:Array,default:[]},rowClass:{type:[String,Function],default:""},rowStyle:{type:Object,default:{}},pageNo:Number,pageSize:{type:Number,default:10}},data:function(){return{}},methods:{_index:function(e){return"number"===typeof this.pageNo?(this.pageNo-1)*this.pageSize+e:e},_rowClass:function(e,t){var n="function"===typeof this.rowClass?this.rowClass({row:Object(Pt["a"])({},e),index:t}):this.rowClass;return["v-table-row",n]},_columnClass:function(e,t){return e.className?["v-table-column",e.className]:["v-table-column"]}},render:function(){var e=this;return Object(a["createVNode"])("tbody",null,[this.dataList.map((function(t,n){return Object(a["createVNode"])("tr",{key:n,class:e._rowClass(t,n),style:e.rowStyle,onClick:function(n){return e.$emit("row-click",{event:n,row:t})},onDblclick:function(n){return e.$emit("row-dblclick",{event:n,row:t})}},[e.columns.map((function(i,o){return Object(a["createVNode"])("td",{key:n+"-"+o,class:e._columnClass(i,o),style:{textAlign:i.align},title:i.prop?t[i.prop]:""},[i.render({row:t,column:i,index:e._index(n),columnIndex:o})])}))])}))])}});const Tn=Sn;var An=Tn,Vn=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"columns",[]),Object(u["a"])(Object(c["a"])(e),"dataList",void 0),Object(u["a"])(Object(c["a"])(e),"timer",void 0),e}return Object(r["a"])(n,[{key:"rowClick",value:function(e){var t=this;clearTimeout(this.timer),this.timer=setTimeout((function(){t.$emit("row-click",e)}),300)}},{key:"rowDblclick",value:function(e){clearTimeout(this.timer),this.$emit("row-dblclick",e)}}]),n}(d["b"]);Vn=Object(E["a"])([Object(d["a"])({name:"vue-table",props:{border:{type:Boolean,default:!0},stripe:{type:Boolean,default:!1},hover:{type:Boolean,default:!1},width:{type:String,default:"100%"},rowClass:{type:[String,Function],default:""},rowStyle:{type:Object,default:{}},dataList:{type:Array,default:[]},pageNo:Number,pageSize:{type:Number,default:10}},emits:["row-click","row-dblclick"],components:{VueTableBody:An}})],Vn);var Bn=Vn;n("f501");const $n=g()(Bn,[["render",Dn],["__scopeId","data-v-0da5d4f8"]]);var Ln,Un,Fn,_n=$n,Rn={class:"pagination"},Mn={class:"period"},Pn={class:"dataTime"},qn={class:"page"};function zn(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("a-range-picker");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Rn,[Object(a["createElementVNode"])("ul",Mn,[Object(a["createElementVNode"])("li",{onClick:t[0]||(t[0]=function(){return e.all&&e.all.apply(e,arguments)}),class:Object(a["normalizeClass"])({active:e._all})},"全部",2),e.showTodo?(Object(a["openBlock"])(),Object(a["createElementBlock"])("li",{key:0,onClick:t[1]||(t[1]=function(){return e.todo&&e.todo.apply(e,arguments)}),class:Object(a["normalizeClass"])({active:e._todo})},"待办",2)):Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("li",Pn,[Object(a["createVNode"])(c,{value:[e.query.stime,e.query.etime],onChange:e.dataTimeChange,valueFormat:"YYYY-MM-DD",format:"YYYY-MM-DD"},null,8,["value","onChange"])])]),Object(a["createElementVNode"])("ul",qn,[Object(a["createElementVNode"])("li",{onClick:t[2]||(t[2]=function(){return e.first&&e.first.apply(e,arguments)}),class:Object(a["normalizeClass"])({disabled:e._first})},"首页",2),Object(a["createElementVNode"])("li",{onClick:t[3]||(t[3]=function(){return e.prev&&e.prev.apply(e,arguments)}),class:Object(a["normalizeClass"])({disabled:e._prev})},"上一页",2),Object(a["createElementVNode"])("li",{onClick:t[4]||(t[4]=function(){return e.next&&e.next.apply(e,arguments)}),class:Object(a["normalizeClass"])({disabled:e._next})},"下一页",2),Object(a["createElementVNode"])("li",{onClick:t[5]||(t[5]=function(){return e.last&&e.last.apply(e,arguments)}),class:Object(a["normalizeClass"])({disabled:e._last})},"尾页",2)])])}var Hn=(Un=Ln=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"showTodo",void 0),Object(u["a"])(Object(c["a"])(e),"count",void 0),Object(u["a"])(Object(c["a"])(e),"curr",void 0),Object(u["a"])(Object(c["a"])(e),"size",void 0),Object(u["a"])(Object(c["a"])(e),"period",Fn.periods[1]),Object(u["a"])(Object(c["a"])(e),"query",Object(Pt["a"])({curr:1,nodeCode:""},e.times())),e}return Object(r["a"])(n,[{key:"total",get:function(){return Math.floor((this.count+this.size-1)/this.size)}},{key:"dataTimeChange",value:function(e){this.query.curr=1,this.query.stime=e[0].format("YYYY-MM-DD HH:mm:ss"),this.query.etime=e[1].format("YYYY-MM-DD HH:mm:ss"),this.$emit("paging",this.query)}},{key:"all",value:function(){this.query.curr=1,this.period=Fn.periods[0],this.query.nodeCode="",this.paging()}},{key:"todo",value:function(){this.query.curr=1,this.period=Fn.periods[1],this.query.nodeCode="running",this.paging()}},{key:"plast",value:function(){this.query.curr=1,this.period=Fn.periods[2],this.paging()}},{key:"thisy",value:function(){this.query.curr=1,this.period=Fn.periods[3],this.paging()}},{key:"month",value:function(){this.query.curr=1,this.period=Fn.periods[4],this.paging()}},{key:"_all",get:function(){return this.period===Fn.periods[0]}},{key:"_todo",get:function(){return this.period===Fn.periods[1]}},{key:"_plast",get:function(){return this.period===Fn.periods[2]}},{key:"_thisy",get:function(){return this.period===Fn.periods[3]}},{key:"_month",get:function(){return this.period===Fn.periods[4]}},{key:"first",value:function(){this.query.curr=1,this.paging()}},{key:"prev",value:function(){this.query.curr--,this.paging()}},{key:"next",value:function(){this.query.curr++,this.paging()}},{key:"last",value:function(){this.query.curr=this.total,this.paging()}},{key:"_first",get:function(){return this._prev}},{key:"_prev",get:function(){return this.total<2||1===this.query.curr}},{key:"_next",get:function(){return this._last}},{key:"_last",get:function(){return this.total<2||this.query.curr===this.total}},{key:"paging",value:function(){Object.assign(this.query,this.times()),this.$emit("paging",this.query)}},{key:"times",value:function(){var e=new Date,t=new Date;switch(t.setDate(t.getDate()+1),this.period){case"month":e.setMonth(e.getMonth()-1);break;case"this":e.setMonth(0),e.setDate(1);break;case"plast":e.setFullYear(e.getFullYear()-1),e.setMonth(0),e.setDate(1),t.setMonth(0),t.setDate(1),t.setHours(0),t.setMinutes(0),t.setSeconds(0);break;case"all":e.setFullYear(1979),e.setMonth(0),e.setDate(1);break;default:e.setMonth(e.getMonth()-1);break}return e.setHours(0),e.setMinutes(0),e.setSeconds(0),{stime:this.format(e),etime:this.format(t)}}},{key:"format",value:function(e){var t=e.getFullYear(),n=e.getMonth()+1,a=e.getDate(),i=e.getHours(),o=e.getMinutes(),r=e.getSeconds(),c=["00","01","02","03","04","05","06","07","08","09"],s=function(e){return c[e]||e};return t+"-"+s(n)+"-"+s(a)+" "+s(i)+":"+s(o)+":"+s(r)}}]),n}(d["b"]),Object(u["a"])(Ln,"periods",["all","todo","plast","this","month"]),Object(u["a"])(Ln,"layout",["first","prev","next","last"]),Fn=Un);Hn=Fn=Object(E["a"])([Object(d["a"])({props:{count:{type:Number,default:0},curr:{type:Number,default:1},size:{type:Number,default:10},showTodo:{type:Boolean,default:!1}},watch:{curr:function(e,t){this.query.curr=e}},emits:["paging"]})],Hn);var Wn=Hn;n("ecf5");const Qn=g()(Wn,[["render",zn],["__scopeId","data-v-39861e88"]]);var Yn,Gn,Kn,Xn=Qn,Jn=(Gn=Yn=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"column",void 0),Object(u["a"])(Object(c["a"])(e),"table",void 0),e}return Object(r["a"])(n,[{key:"_watchProp",value:function(e){this.column.prop=e}},{key:"_watchLabel",value:function(e){this.column.label=e}},{key:"_watchAlign",value:function(e){this.column.align=e}},{key:"_watchIndex",value:function(e){this.column.index=e}},{key:"_watchWidth",value:function(e){this.column.width=e}},{key:"_watchMinWidth",value:function(e){this.column.minWidth=e}},{key:"_watchClassName",value:function(e){this.column.className=e}},{key:"_watchLabelClassName",value:function(e){this.column.labelClassName=e}},{key:"_table",value:function(){this.table=this.$parent;while(null!=this.table&&"vue-table"!==this.table.$options.name)this.table=this.table.$parent}},{key:"beforeCreate",value:function(){this.column={id:"col-"+Kn.columnIndex++,label:"",render:function(e){}}}},{key:"render",value:function(){}},{key:"created",value:function(){var e=this;Object.assign(this.column,this.$props),this.column.render=function(t){return e.$slots.default?e.$slots.default(t):e.column.prop?t.row[e.column.prop]:e.column.index?t.index+1:""},this._table()}},{key:"mounted",value:function(){null!=this.table&&this.table.columns.push(this.column)}},{key:"unmounted",value:function(){if(null===this.table)return!1;var e=this.table.columns.map((function(e){return e.id})).indexOf(this.column.id);e>-1&&this.table.columns.splice(e,1)}}]),n}(d["b"]),Object(u["a"])(Yn,"columnIndex",0),Kn=Gn);Jn=Kn=Object(E["a"])([Object(d["a"])({name:"vue-table-column",props:{prop:String,label:String,align:{type:String,default:"center"},index:{type:Boolean,default:!1},width:[Number,String],minWidth:[Number,String],className:[String,Function],labelClassName:[String,Function]},watch:{prop:function(e){this._watchProp(e)},label:function(e){this._watchLabel(e)},align:function(e){this._watchAlign(e)},index:function(e){this._watchIndex(e)},width:function(e){this._watchWidth(e)},minWidth:function(e){this._watchMinWidth(e)},className:function(e){this._watchClassName(e)},labelClassName:function(e){this._watchLabelClassName(e)}}})],Jn);var Zn=Jn;const ea=Zn;var ta=ea;function na(e){e.component("vue-ztree",En),e.component("vue-table",_n),e.component("pagination",Xn),e.component("vue-table-column",ta);var t=document.createElement("div");e.config.globalProperties.$logo=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];Object(a["createApp"])(gn,{show:e,cover:n}).mount(t)},e.config.globalProperties.$toast=function(e){var t={msg:""};"string"===typeof e?t.msg=e:Object.assign(t,e);var n=Object(a["createApp"])(tn,t);n.mount(document.createElement("div"))},e.config.globalProperties.$alert=function(e){var t={content:""};"string"===typeof e?t.content=e:Object.assign(t,e);var n=Object(a["createApp"])(un,t);n.mount(document.createElement("div"))},e.config.globalProperties.$dialog=function(e){var t={};e.content?Object.assign(t,e):Object.assign(t,{content:{component:e}});var n=Object(a["createApp"])(ra,t).use(Kt).use(na);return n.mount(document.createElement("div"))}}var aa=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"title",Object(d["c"])({default:"标题"})),Object(u["a"])(this,"content",Object(d["c"])({default:{}})),Object(u["a"])(this,"lock",Object(d["c"])({default:!0})),Object(u["a"])(this,"max",Object(d["c"])({default:!0})),Object(u["a"])(this,"width",Object(d["c"])({default:200})),Object(u["a"])(this,"height",Object(d["c"])({default:150})),Object(u["a"])(this,"top",Object(d["c"])({default:"50%"})),Object(u["a"])(this,"left",Object(d["c"])({default:"50%"})),Object(u["a"])(this,"ok",Object(d["c"])({default:function(){}})),Object(u["a"])(this,"close",Object(d["c"])({default:function(){}})),Object(u["a"])(this,"closeButton",Object(d["c"])({default:!0})),Object(u["a"])(this,"shwoBottom",Object(d["c"])({default:!1})),Object(u["a"])(this,"customClass",Object(d["c"])({default:"custom-dialog"}))},ia=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"id","content-"+Math.round(1e5*Math.random())),Object(u["a"])(Object(c["a"])(e),"pxTop",0),Object(u["a"])(Object(c["a"])(e),"pxLeft",0),Object(u["a"])(Object(c["a"])(e),"pxWidth",0),Object(u["a"])(Object(c["a"])(e),"pxHeight",0),Object(u["a"])(Object(c["a"])(e),"clientWidth",0),Object(u["a"])(Object(c["a"])(e),"clientHeight",0),Object(u["a"])(Object(c["a"])(e),"moveX",0),Object(u["a"])(Object(c["a"])(e),"moveY",0),Object(u["a"])(Object(c["a"])(e),"isFull",!1),Object(u["a"])(Object(c["a"])(e),"isActive",!0),Object(u["a"])(Object(c["a"])(e),"contentApp",void 0),e}return Object(r["a"])(n,[{key:"mousedown",value:function(e){var t=this;this.moveX=e.pageX,this.moveY=e.pageY,document.onselectstart=function(){return!1},document.onmousemove=function(e){var n=t.pxLeft+e.pageX-t.moveX,a=t.pxTop+e.pageY-t.moveY;n>0&&n+t.pxWidth<t.clientWidth&&(t.pxLeft=n),a>0&&a+t.pxHeight<t.clientHeight&&(t.pxTop=a),t.moveX=e.pageX,t.moveY=e.pageY},document.onmouseup=function(){document.onmousemove=document.onmouseup=document.onselectstart=null}}},{key:"changeSize",value:function(){this.isFull?this.position():(this.pxWidth=this.clientWidth,this.pxHeight=this.clientHeight,this.initTop(),this.initLeft()),this.isFull=!this.isFull}},{key:"_ok",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.ok&&!this.ok(t)||this._destroy()}},{key:"_close",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this.close&&!this.close(t)||this._destroy()}},{key:"_destroy",value:function(){this.$el.remove(),this.isActive=!1}},{key:"initTop",value:function(){"number"===typeof this.top?this.pxTop=this.top:this.top.endsWith("%")?this.pxTop=parseFloat(this.top.replace("%",""))/100*(this.clientHeight-this.pxHeight):this.pxTop=parseInt(this.top),this.pxTop+this.pxHeight>this.clientHeight&&(this.pxTop=this.clientHeight-this.pxHeight)}},{key:"initLeft",value:function(){"number"===typeof this.left?this.pxLeft=this.left:this.left.endsWith("%")?this.pxLeft=parseFloat(this.left.replace("%",""))/100*(this.clientWidth-this.pxWidth):this.pxLeft=parseInt(this.left),this.pxLeft+this.pxWidth>this.clientWidth&&(this.pxLeft=this.clientWidth-this.pxWidth)}},{key:"initWidth",value:function(){"number"===typeof this.width?this.pxWidth=this.width:this.width.endsWith("%")?this.pxWidth=parseFloat(this.width.replace("%",""))/100*this.clientWidth:this.pxWidth=parseInt(this.width),this.pxWidth>this.clientWidth&&(this.pxWidth=this.clientWidth)}},{key:"initHeight",value:function(){"number"===typeof this.height?this.pxHeight=this.height:this.height.endsWith("%")?this.pxHeight=parseFloat(this.height.replace("%",""))/100*this.clientHeight:this.pxHeight=parseInt(this.height),this.pxHeight>this.clientHeight&&(this.pxHeight=this.clientHeight)}},{key:"position",value:function(){this.initWidth(),this.initHeight(),this.initTop(),this.initLeft()}},{key:"client",value:function(){this.clientWidth=document.documentElement.clientWidth,this.clientHeight=document.documentElement.clientHeight}},{key:"created",value:function(){this.client(),this.position()}},{key:"mounted",value:function(){var e=this;document.body.appendChild(this.$el),this.content.handle&&Object.assign(this.content.props,{dialog:this}),this.contentApp=Object(a["createApp"])(this.content.component,this.content.props),this.contentApp.use(Kt).use(na).use(Gt["a"]).mount("#"+this.id),window.onresize=function(){return e.client()}}},{key:"replace",value:function(e,t,n){this.isActive?(n&&Object.assign(t,{dialog:this}),this.contentApp.unmount(),this.contentApp=Object(a["createApp"])(e,t),this.contentApp.use(Kt).use(na).use(Gt["a"]).mount("#"+this.id)):this.$toast("this dialog has been destroyed")}}]),n}(d["b"].with(aa));n("970c");const oa=g()(ia,[["render",Yt],["__scopeId","data-v-5552e54e"]]);var ra=oa,ca=["src"];function sa(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("iframe",{src:e.url,frameborder:"0"},null,8,ca)}var la=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){return Object(o["a"])(this,n),t.apply(this,arguments)}return n}(d["b"]);la=Object(E["a"])([Object(d["a"])({props:{url:String}})],la);var ua=la;n("fe73");const da=g()(ua,[["render",sa],["__scopeId","data-v-2e3cdf56"]]);var pa=da,ha=n("c1df"),fa=n.n(ha),ba=function(){function e(){Object(o["a"])(this,e)}return Object(r["a"])(e,null,[{key:"rewriteResponse",value:function(e){if(200===e.code){var t={code:0,data:e.result||e.data,msg:e.message||e.msg};return t}return Object(Pt["a"])(Object(Pt["a"])({},e),{},{msg:e.message})}},{key:"getQueryUrl",value:function(e){var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)"),n=window.location.search.substr(1).match(t);return null!=n?decodeURIComponent(n[2]):""}},{key:"fillZero",value:function(e,t){return Array(t).join("0").concat(String(e)).substr(-t)}},{key:"rewrite",value:function(t){var n=e.getQueryUrl("wx_user_id"),a="/seal/";return"/"===a[a.length-1]&&(a=a.substring(0,a.length-1)),n?a+t+(-1===t.indexOf("?")?"?":"&")+"wx_user_id="+n:a+t}},{key:"rewriteUrl",value:function(t){var n=e.getQueryUrl("wx_user_id"),a="/seal/";return"/"===a[a.length-1]&&(a=a.substring(0,a.length-1)),n?a+t+(-1===t.indexOf("?")?"?":"&")+"wx_user_id="+n:a+t}},{key:"getUser",value:function(){var t=Object(re["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.userInfo){t.next=2;break}return t.abrupt("return",e.userInfo);case 2:return t.next=4,zt.a.get(e.rewrite("/user/getUserInfo")).then((function(t){200===t.data.code?e.userInfo=t.data.result:console.log("获取人员出错:"+t.data.message)}));case 4:return t.abrupt("return",e.userInfo);case 5:case"end":return t.stop()}}),t)})));function n(){return t.apply(this,arguments)}return n}()},{key:"date",value:function(){return fa()(new Date).format("YYYY-MM-DD")}},{key:"datetime",value:function(){return fa()(new Date).format("YYYY-MM-DD HH:mm:ss")}},{key:"datediff",value:function(e,t){var n=new Date;return"day"===t?n.setDate(n.getDate()+e):"month"===t&&n.setMonth(n.getMonth()+e),fa()(n).format("YYYY-MM-DD HH:mm:ss")}},{key:"preview",value:function(e){Object(a["createApp"])(ra,{title:"文件预览",left:0,width:"50%",height:"100%",content:{component:pa,props:{src:"http://192.168.8.54:8090/view/".concat(e)}}}).mount(document.createElement("div"))}},{key:"offset",value:function(e){var t={top:0,left:0},n=e&&e.ownerDocument,a=n.documentElement;"undefined"!==typeof e.getBoundingClientRect&&(t=e.getBoundingClientRect());var i=n.defaultView;return i?{top:t.top+i.pageYOffset-a.clientTop,left:t.left+i.pageXOffset-a.clientLeft}:t}},{key:"isEmptyObject",value:function(e){return null===e||"undefined"===typeof e||!(e instanceof Array||e instanceof Date)&&("object"===Object(Mt["a"])(e)&&0===Object.keys(e).length)}},{key:"initObject",value:function(t){for(var n in t){var a=t[n];null===a&&(t[n]={}),"object"===Object(Mt["a"])(a)&&e.initObject(a)}}},{key:"initReverseObject",value:function(t){for(var n in t){var a=t[n];e.isEmptyObject(a)&&(t[n]=null),"object"===Object(Mt["a"])(a)&&e.initReverseObject(a)}}},{key:"isVisible",value:function(e){if(null===e)return!1;var t=!0,n="none"!==getComputedStyle(e).display&&"hidden"!==getComputedStyle(e).visibility;while(t&&n)e=e&&e.parentElement,e&&e!==document.body?n="none"!==getComputedStyle(e).display&&"hidden"!==getComputedStyle(e).visibility:t=!1;return n}},{key:"validate",value:function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,n=!0,a=["input","select","textarea"],i=0;i<a.length;i++)for(var o=t.getElementsByTagName(a[i]),r=0;r<o.length;r++){var c=o[r];if(!1!==e.isVisible(c)){var s="",l=!1;(c instanceof HTMLInputElement||c instanceof HTMLSelectElement||c instanceof HTMLTextAreaElement)&&(s=c.value,l=c.required),!l||s&&s.trim()?c.style.backgroundColor="#FFFFFF":(n=!1,c.style.backgroundColor="#FFC1C1")}}return n}},{key:"dropdown",value:function(t){t.index=t.index||1e3,t.close=t.close||function(){};var n=t.el,a=document.getElementById(t.menu);if(null!==n&&null!==a){var i=function(e,t){var n=e.parentElement;while(n){if(n.id===t)return n;n=n.parentElement}return null},o=e.offset(n);a.style.display="block",a.style.left=o.left+"px",a.style.top=o.top+n.offsetHeight+"px",a.style.zIndex=String(t.index);var r=function e(n){var o=n.target;o===t.el||o.id===t.menu||i(o,t.menu)||(a.style.display="none",document.body.removeEventListener("mousedown",e))};document.body.addEventListener("mousedown",r)}}}]),e}();Object(u["a"])(ba,"userInfo",void 0);var ma={class:"files"},ga={key:1,class:"file-list"},va=["title","onClick"],ja=["onClick"],ya=["onClick"];function Oa(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("a-upload");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",ma,[e.upload?(Object(a["openBlock"])(),Object(a["createBlock"])(c,{key:0,class:"upload",name:"fileContent",multiple:e.maxCount>1,accept:e.accept,data:e.params,onChange:e.handleChange,action:e._action,withCredentials:!0,showUploadList:!1,"before-upload":e.beforeUpload,disabled:e.disabled,ref:"fileBtnList"},{default:Object(a["withCtx"])((function(){return[Object(a["renderSlot"])(e.$slots,"default",{},(function(){return[Object(a["createElementVNode"])("span",{title:"点击上传文件",class:Object(a["normalizeClass"])(["upload-button no-print",{fileBtn:e.disabled}])},Object(a["toDisplayString"])(e.button),3)]}),!0)]})),_:3},8,["multiple","accept","data","onChange","action","before-upload","disabled"])):Object(a["createCommentVNode"])("",!0),e.showUploadList?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",ga,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.fileList,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"file-item",key:t.uuid},[Object(a["createElementVNode"])("span",{title:5==e.codeTo?"点击预览"+t.fileName:"点击预览",onClick:function(n){return e.handlePreview(t)},class:Object(a["normalizeClass"])(5==e.codeTo?"text_slice":"")},[Object(a["createTextVNode"])(Object(a["toDisplayString"])(n+1)+"、 ",1),e.showGroup?(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:0},[Object(a["createTextVNode"])("【"+Object(a["toDisplayString"])(t.groupDeptName)+"】",1)],64)):Object(a["createCommentVNode"])("",!0),Object(a["createTextVNode"])(" "+Object(a["toDisplayString"])(t.fileName),1)],10,va),e.download?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:0,class:"icon-download",title:"点击下载",onClick:function(n){return e.handleDownload(t)}},null,8,ja)):Object(a["createCommentVNode"])("",!0),e.remove?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:1,class:"icon-delete",title:"点击删除",onClick:function(n){return e.handleRemove(t)}},null,8,ya)):Object(a["createCommentVNode"])("",!0)])})),128))])):Object(a["createCommentVNode"])("",!0)])}n("4de4");var ka=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"disabled",!1),Object(u["a"])(Object(c["a"])(e),"codeTo",void 0),Object(u["a"])(Object(c["a"])(e),"params",void 0),Object(u["a"])(Object(c["a"])(e),"action",void 0),Object(u["a"])(Object(c["a"])(e),"getAction",void 0),Object(u["a"])(Object(c["a"])(e),"listAction",void 0),Object(u["a"])(Object(c["a"])(e),"removeAction",void 0),Object(u["a"])(Object(c["a"])(e),"downloadAction",void 0),Object(u["a"])(Object(c["a"])(e),"jchcUser",void 0),Object(u["a"])(Object(c["a"])(e),"showGroup",void 0),Object(u["a"])(Object(c["a"])(e),"showRadio",void 0),Object(u["a"])(Object(c["a"])(e),"maxCount",void 0),Object(u["a"])(Object(c["a"])(e),"fileList",[]),e}return Object(r["a"])(n,[{key:"_action",get:function(){return ba.rewrite(this.action)}},{key:"pushFile",value:function(e){this.fileList.push(e)}},{key:"beforeCreate",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Promise.resolve(ba.getUser());case 2:t=e.sent,this.jchcUser=t;case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"beforeUpload",value:function(e,t){return this.$logo(!0,!0),this.params&&this.params.type&&1===this.params.type&&t.length>1?(this.$toast("用印文本仅支持本地上传一个pdf文件!"),this.$logo(!1),!1):"3"==this.codeTo&&"application/pdf"!==e.type?(this.$toast("仅支持上传pdf格式的文件"),this.$logo(!1),!1):void 0}},{key:"created",value:function(){var e=this;this.$axios.get(this.listAction,{params:this.params}).then((function(t){e.fileList=t.data,e.$parent.$refs["opinionsFile"]&&(e.$parent.$refs["opinionsFile"].files=e.fileList),0!==e.fileList.length&&e.$emit("noempty")})).catch((function(t){e.$toast(t.toString())}))}},{key:"handleChange",value:function(e){if("done"===e.file.status){this.fileList=[];var t={uuid:e.file.response.data,fileName:e.file.name,uploadTime:ba.datetime(),groupDeptName:this.jchcUser.shortGroupName+"/"+this.jchcUser.deptName};1===this.maxCount&&(this.disabled=!0),1===this.maxCount&&this.fileList.length>0&&this.handleRemove(this.fileList[0]),this.fileList.push(t),this.$emit("upload",t),this.$emit("success",this),this.$logo(!1)}}},{key:"handlePreview",value:function(e){var t=this;e.idocviewUuid?ba.preview(e.idocviewUuid):this.$axios.get("".concat(this.getAction,"?id=").concat(e.uuid)).then((function(n){n.data.idocviewUuid?(e.idocviewUuid=n.data.idocviewUuid,ba.preview(n.data.idocviewUuid)):t.$toast("文件正在转换中，请稍候……")})).catch((function(e){return t.$toast(e.toString())}))}},{key:"handleDownload",value:function(e){window.open(ba.rewrite(this.downloadAction+"/"+e.uuid))}},{key:"handleRemove",value:function(e){var t=this;this.$logo(!0,!0),this.$axios.get("".concat(this.removeAction,"?id=").concat(e.uuid)).then((function(n){0===n.code?(t.$logo(!1),t.fileList=t.fileList.filter((function(t){return t.uuid!==e.uuid})),t.$emit("remove",e),t.$emit("removeFile",e),"3"==t.codeTo&&(t.disabled=!1),t.$logo(!1)):(t.$toast(n.msg),t.$logo(!1))})).catch((function(e){t.$toast(e.toString())}))}}]),n}(d["b"]);ka=Object(E["a"])([Object(d["a"])({props:{params:{type:Object,required:!0},action:{type:String,default:"/jchcfile/upload"},getAction:{type:String,default:"/jchcfile/queryById"},listAction:{type:String,default:"/jchcfile/getFileByKind"},removeAction:{type:String,default:"/jchcfile/delete"},downloadAction:{type:String,default:"/jchcfile/download"},button:{type:String,default:"上传"},upload:{type:Boolean,default:!1},remove:{type:Boolean,default:!1},download:{type:Boolean,default:!0},accept:{type:String,default:"*"},showUploadList:{type:Boolean,default:!0},showGroup:{type:Boolean,default:!1},maxCount:{type:Number,default:1/0},codeTo:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["upload","remove","removeFile","noempty"],watch:{fileList:{handler:function(e,t){var n=this,a=e;a.forEach((function(e,t){1===n.maxCount&&a.length&&(n.disabled=!0)}))}}}})],ka);var wa=ka;n("8488");const xa=g()(wa,[["render",Oa],["__scopeId","data-v-06d44c31"]]);var Na=xa,Ea=function(e){return Object(a["pushScopeId"])("data-v-4c44b68d"),e=e(),Object(a["popScopeId"])(),e},Ia={class:"set-tree"},Ca=Ea((function(){return Object(a["createElementVNode"])("div",{class:"title",style:{}},"用印申请单权限配置",-1)})),Da={class:"activity"},Sa=Ea((function(){return Object(a["createElementVNode"])("p",{class:"activity-name beixuan"},"备选人员",-1)})),Ta={class:"tree-con"},Aa={class:"activity"},Va=Ea((function(){return Object(a["createElementVNode"])("p",{class:"activity-name yixuan"},"已选人员",-1)})),Ba={class:"tree-con",id:"userList"},$a=["uuid","userId"],La=["onClick"];function Ua(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-ztree");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Ia,[Ca,Object(a["createElementVNode"])("div",Da,[Sa,Object(a["createElementVNode"])("div",Ta,[Object(a["createVNode"])(c,{settings:e.settings,onOnCheck:e.onCheck,onOnCreated:e.onCreated},null,8,["settings","onOnCheck","onOnCreated"])])]),Object(a["createElementVNode"])("div",Aa,[Va,Object(a["createElementVNode"])("div",Ba,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.checkedUsers,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{class:"user-list",key:t.uuid,uuid:t.uuid,userId:t.user.id},[Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(t.user.name),1),Object(a["createElementVNode"])("img",{onClick:function(a){return e.deleteUser(t,n)},src:se.a,alt:""},null,8,La)],8,$a)})),128))])])])}var Fa=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"outData",void 0),Object(u["a"])(this,"actId",void 0),Object(u["a"])(this,"title",void 0)},_a=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"checkedUsers",[]),Object(u["a"])(Object(c["a"])(e),"zTree",{}),Object(u["a"])(Object(c["a"])(e),"settings",{async:{enable:!0,autoParam:["id"],type:"get",url:"",dataFilter:function(t,n,a){for(var i=a.data,o=0,r=i.length;o<r;o++){i[o].name=i[o].name.replace(/\.n/g,".");for(var c=0;c<e.checkedUsers.length;c++)i[o].id===e.checkedUsers[c].user.id&&(i[o].checked=!0)}return i}},check:{enable:!0,autoCheckTrigger:!0,chkStyle:"checkbox",radioType:"all",chkboxType:{Y:"s",N:"ps"}},data:{key:{},simpleData:{enable:!0,idKey:"id",pIdKey:"Pid",rootPId:"-1"}}}),e}return Object(r["a"])(n,[{key:"beforeCreate",value:function(){this.settings.async.url=ba.rewrite("/QSecurity/zTreeQSecurity")}},{key:"created",value:function(){this.initCheckedUser()}},{key:"onCreated",value:function(e){this.zTree=e}},{key:"initCheckedUser",value:function(){var e=this;this.$axios.get("/activityUser/listActUser?actId="+this.actId).then((function(t){return e.$logo(!1),0!==t.code?(e.$toast(t.msg),!1):(e.checkedUsers=t.data,!1)})).catch((function(t){t&&e.$toast(t.toString())}))}},{key:"deleteUser",value:function(e,t,n){var a=this;this.$logo(!0),this.$axios.get("/defConfigUser/delete/"+e.uuid).then((function(i){if(a.$logo(!1),0!==i.code)return a.$toast(i.msg),!1;a.checkedUsers.splice(t,1),a.outData.splice(t,1);var o=a.zTree.getNodeByParam("id",e.user.id);null==o||n||a.zTree.checkNode(o)})).catch((function(e){e&&a.$toast(e.toString())}))}},{key:"onCheck",value:function(e,t,n){var a=this;if(n.checked)this.$logo(!0),this.$axios.post("/activityuser/insert",{activityId:this.actId,userId:n.id,userName:n.name,deptId:n.extAttrs.deptId,deptName:n.extAttrs.deptName,groupId:n.extAttrs.orgId,groupName:n.extAttrs.orgName}).then((function(e){if(a.$logo(!1),0!==e.code)return a.$toast(e.msg),!1;a.checkedUsers.push({uuid:e.data,user:{id:n.id,name:n.name,deptId:n.extAttrs.deptId,groupId:n.extAttrs.orgId}}),a.outData.push({actUserUuid:"",activityId:a.actId,createGroupId:"",createTime:"",deptId:n.extAttrs.deptId,deptName:n.extAttrs.deptName,groupId:n.extAttrs.orgId,groupName:n.extAttrs.orgName,signetId:null,userId:n.id,userName:n.name,uuid:e.data})})).catch((function(e){e&&a.$toast(e.toString())}));else for(var i=0;i<this.checkedUsers.length;i++){var o=this.checkedUsers[i];o.user.id===n.id&&this.deleteUser(o,i,!0)}}}]),n}(d["b"].with(Fa));n("8c4a");const Ra=g()(_a,[["render",Ua],["__scopeId","data-v-4c44b68d"]]);var Ma=Ra,Pa=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"setData",{signetType:[],listActModelUsers:[],actUsers:[]}),Object(u["a"])(Object(c["a"])(e),"jchcUser",{}),Object(u["a"])(Object(c["a"])(e),"uploadParams",null),Object(u["a"])(Object(c["a"])(e),"codeTo","3"),e}return Object(r["a"])(n,[{key:"handleUploadFile",value:function(e){this.$refs.rules.pushFile(e)}},{key:"handleUploadTextFile",value:function(e){this.$refs.textFile.pushFile(e)}},{key:"beforeCreate",value:function(){var e=this;ba.getUser().then((function(t){e.jchcUser=t,e.uploadParams={formId:t.groupId}}))}},{key:"mounted",value:function(){this.searchList()}},{key:"reflash",value:function(){this.searchList()}},{key:"toPerson",value:function(e){this.setData.actUsers[e.uuid]||(this.setData.actUsers[e.uuid]=[]),this.$dialog({title:"配置流程人员",max:!1,width:580,height:495,content:{component:Ma,props:{actId:e.uuid,title:e.name,outData:this.setData.actUsers[e.uuid]}}})}},{key:"searchList",value:function(){var e=this;this.$axios.get("/signetConfig/getSignetConfigDate?flowCode=signet").then((function(t){if(0!==t.code)return e.$toast(t.msg),!1;e.setData=t.data})).catch((function(t){return e.$toast(t.toString())}))}}]),n}(d["b"]);Pa=Object(E["a"])([Object(d["a"])({components:{Tag:Rt,Files:Na}})],Pa);var qa=Pa;n("a74f");const za=g()(qa,[["render",G],["__scopeId","data-v-7621fc59"]]);var Ha=za,Wa={class:"app-con"},Qa=["onClick"],Ya={key:0,class:"btn-group"},Ga=["onClick"],Ka=["onClick"];function Xa(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-table-column"),s=Object(a["resolveComponent"])("vue-table");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Wa,[Object(a["createVNode"])(s,{hover:!0,dataList:e.list,class:"table"},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{label:"序号",index:!0,width:50}),Object(a["createVNode"])(c,{label:"用印申请单",prop:"sigentName"},{default:Object(a["withCtx"])((function(t){return[Object(a["createElementVNode"])("span",{class:"cell-title",onClick:function(n){return e._click(t)}},Object(a["toDisplayString"])(t.row.sigentName),9,Qa)]})),_:1}),Object(a["createVNode"])(c,{label:"延期申请内容",prop:"applyContent"}),Object(a["createVNode"])(c,{label:"申请人",width:120,prop:"userName"}),Object(a["createVNode"])(c,{label:"操作",width:120,prop:"state"},{default:Object(a["withCtx"])((function(t){return[e.computeShowOperation(t.row)?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Ya,[Object(a["createElementVNode"])("div",{class:"btn primary",onClick:function(n){return e.operation(t.row,2)}},"延期",8,Ga),Object(a["createElementVNode"])("div",{class:"btn normal",onClick:function(n){return e.operation(t.row,3)}},"不同意",8,Ka)])):(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:1},[],64))]})),_:1})]})),_:1},8,["dataList"])])}var Ja=n("2272"),Za=n.n(Ja),ei=n("c87d"),ti=n.n(ei),ni=function(e){return Object(a["pushScopeId"])("data-v-25aee7ab"),e=e(),Object(a["popScopeId"])(),e},ai={class:"right"},ii={class:"right-head"},oi={class:"seal-con"},ri=ni((function(){return Object(a["createElementVNode"])("img",{src:Za.a},null,-1)})),ci=ni((function(){return Object(a["createElementVNode"])("p",{style:{color:"#14a35f"}},"完 成",-1)})),si=[ri,ci],li={class:"seal-con",style:{}},ui=ni((function(){return Object(a["createElementVNode"])("img",{src:ti.a},null,-1)})),di=ni((function(){return Object(a["createElementVNode"])("p",{style:{color:"#ff0000"}},"不同意",-1)})),pi=[ui,di],hi={class:"right-title"},fi=ni((function(){return Object(a["createElementVNode"])("span",null,"用印申请单",-1)})),bi={class:"head-remark"},mi={class:"head-left"},gi=ni((function(){return Object(a["createElementVNode"])("span",{class:"blue-text"},"申请人：",-1)})),vi={class:"head-right head-right-num",style:{}},ji={class:"right-main"},yi={class:"table"},Oi=ni((function(){return Object(a["createElementVNode"])("colgroup",null,[Object(a["createElementVNode"])("col",{width:"100px"}),Object(a["createElementVNode"])("col")],-1)})),ki=ni((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"用印事由",-1)})),wi=ni((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"背景资料",-1)})),xi=ni((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"用印文本",-1)})),Ni=ni((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"印章类别",-1)})),Ei={style:{width:"90px",height:"90px",overflow:"hidden"}},Ii=["src","alt","title"],Ci={class:"table opinion table-margin"},Di=ni((function(){return Object(a["createElementVNode"])("colgroup",null,[Object(a["createElementVNode"])("col",{width:"100px"}),Object(a["createElementVNode"])("col")],-1)})),Si={class:"blue-text"},Ti={class:"file-list"},Ai=ni((function(){return Object(a["createElementVNode"])("p",null,"相关制度",-1)})),Vi=["onClick"];function Bi(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("link-file-pack"),s=Object(a["resolveComponent"])("files"),l=Object(a["resolveComponent"])("opinions");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",ai,[Object(a["createElementVNode"])("div",ii,[Object(a["withDirectives"])(Object(a["createElementVNode"])("div",oi,si,512),[[a["vShow"],3===e.ajaxData.status]]),Object(a["withDirectives"])(Object(a["createElementVNode"])("div",li,pi,512),[[a["vShow"],4===e.ajaxData.status]]),Object(a["createElementVNode"])("p",hi,[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.jchcUser.groupName)+" ",1),fi]),Object(a["createElementVNode"])("div",bi,[Object(a["createElementVNode"])("p",mi,[gi,Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(e.ajaxData.userName)+"("+Object(a["toDisplayString"])(e.ajaxData.deptName)+")",1)]),Object(a["createElementVNode"])("p",vi,[Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(e.ajaxData.createTime),1)])])]),Object(a["createElementVNode"])("div",ji,[Object(a["createElementVNode"])("table",yi,[Oi,Object(a["createElementVNode"])("tr",null,[ki,Object(a["createElementVNode"])("td",null,[Object(a["createElementVNode"])("span",null,Object(a["toDisplayString"])(e.ajaxData.title),1)])]),Object(a["createElementVNode"])("tr",null,[wi,Object(a["createElementVNode"])("td",null,[e.ajaxData.uuid?(Object(a["openBlock"])(),Object(a["createBlock"])(c,{key:0,ref:"sealFile",formId:e.ajaxData.uuid,upload:!1,remove:!1},null,8,["formId"])):Object(a["createCommentVNode"])("",!0)])]),Object(a["createElementVNode"])("tr",null,[xi,Object(a["createElementVNode"])("td",null,[e.ajaxData.uuid?(Object(a["openBlock"])(),Object(a["createBlock"])(s,{key:0,params:{formId:e.ajaxData.uuid,kind:4},upload:!1,remove:!1},null,8,["params"])):Object(a["createCommentVNode"])("",!0)])]),Object(a["createElementVNode"])("tr",null,[Ni,Object(a["createElementVNode"])("td",null,[Object(a["createElementVNode"])("ul",null,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.ajaxData.signetItems,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("li",{key:n,class:"link-signet"},[Object(a["createElementVNode"])("div",Ei,[Object(a["createElementVNode"])("img",{style:{width:"100%",height:"100%"},src:t.files.length?e.signetIcon+t.files[0].uuid:"",alt:t.signetName,title:t.signetName},null,8,Ii)])])})),128))])])])]),Object(a["createElementVNode"])("table",Ci,[Di,(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.AllNode,(function(t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("tr",{key:t.id},[Object(a["createElementVNode"])("td",Si,Object(a["toDisplayString"])(t.nodeName),1),Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(l,{opinions:e.opinionList,code:t.nodeId},null,8,["opinions","code"])])])})),128))]),Object(a["createElementVNode"])("div",Ti,[Ai,(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.fileList,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{class:"file",key:t.uuid},[Object(a["createElementVNode"])("span",{onClick:function(n){return e.previewFile(t.docViewUuid)}},Object(a["toDisplayString"])(n+1)+"、"+Object(a["toDisplayString"])(t.fileName),9,Vi)])})),128))])])])}n("1276");var $i={class:"link-files"},Li={class:"file-list"},Ui=["onClick"],Fi=["onClick"],_i=["onClick"],Ri=["src"];function Mi(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",$i,[e.upload?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:0,class:"link-button",onClick:t[0]||(t[0]=function(){return e.handleLink&&e.handleLink.apply(e,arguments)})},Object(a["toDisplayString"])(e.button),1)):Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("div",Li,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.fileList,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"file-item",key:t.uuid},[Object(a["createElementVNode"])("span",{title:"点击预览",onClick:function(n){return e.handlePreview(t)}},Object(a["toDisplayString"])(n+1)+"、【"+Object(a["toDisplayString"])(t.tag)+"】 "+Object(a["toDisplayString"])(t.title),9,Ui),t.localfile&&e.download?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:0,class:"icon-download",title:"点击下载",onClick:function(n){return e.handleDownload(t)}},null,8,Fi)):Object(a["createCommentVNode"])("",!0),e.remove?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:1,class:"icon-delete",title:"点击删除",onClick:function(n){return e.handleRemove(t)}},null,8,_i)):Object(a["createCommentVNode"])("",!0)])})),128))]),Object(a["createElementVNode"])("iframe",{id:"print-iframe",src:e.printUrl,style:{display:"none"}},null,8,Ri)])}var Pi=n("b32a"),qi=n.n(Pi),zi=function(e){return Object(a["pushScopeId"])("data-v-f5f07af0"),e=e(),Object(a["popScopeId"])(),e},Hi={class:"link-file"},Wi={class:"left"},Qi=["onClick"],Yi={class:"right"},Gi={class:"head"},Ki={class:"main"},Xi={class:"con"},Ji={key:0,class:"file-con"},Zi={class:"file-list"},eo={key:0},to=zi((function(){return Object(a["createElementVNode"])("p",null,"暂无数据！",-1)})),no=[to],ao=["value","checked","onClick"],io=["title","onClick"],oo={class:"page"},ro={key:1,class:"upload"},co=zi((function(){return Object(a["createElementVNode"])("p",null,"请上传.doc、.docx、.xls、.xlsx、.pdf、.png、.jpg、.jpeg格式文件",-1)})),so={class:"choosed"},lo={class:"upload-list"},uo=["title"],po=["onClick"];function ho(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("pagination"),s=Object(a["resolveComponent"])("files");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Hi,[Object(a["createElementVNode"])("div",Wi,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.tags,(function(t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{key:t.tag,class:Object(a["normalizeClass"])({active:e.currentTag.tag===t.tag}),onClick:function(n){return e.handleTag(t)}},Object(a["toDisplayString"])(t.tag),11,Qi)})),128))]),Object(a["createElementVNode"])("div",Yi,[Object(a["createElementVNode"])("div",Gi,[Object(a["withDirectives"])(Object(a["createElementVNode"])("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=function(t){return e.params.word=t}),placeholder:"输入关键字搜索"},null,512),[[a["vModelText"],e.params.word]]),Object(a["createElementVNode"])("button",{onClick:t[1]||(t[1]=function(){return e.handleQuery&&e.handleQuery.apply(e,arguments)})},"搜索")]),Object(a["createElementVNode"])("div",Ki,[Object(a["createElementVNode"])("div",Xi,[e.currentTag.url?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Ji,[Object(a["createElementVNode"])("ul",Zi,[0===e.page.data.length?(Object(a["openBlock"])(),Object(a["createElementBlock"])("li",eo,no)):Object(a["createCommentVNode"])("",!0),(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.page.data,(function(t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("li",{key:t.id},[Object(a["createElementVNode"])("input",{type:"checkbox",value:t.id,checked:e.isCheck(t),onClick:function(n){return e.handleCheck(n,t)}},null,8,ao),Object(a["createElementVNode"])("span",{title:t.title,onClick:function(n){return e.handlePreview(t)}},Object(a["toDisplayString"])(t.fullDocNO)+" "+Object(a["toDisplayString"])(t.title),9,io)])})),128))]),Object(a["createElementVNode"])("div",oo,[Object(a["createVNode"])(c,{size:e.page.size,count:e.page.count,onPaging:e.handlePage},null,8,["size","count","onPaging"])])])):(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",ro,[Object(a["createVNode"])(s,{upload:!0,showUploadList:!1,button:"点击上传",params:{formId:e.formId,kind:1},onUpload:e.handleUpload,codeTo:e.codeTo},null,8,["params","onUpload","codeTo"]),co]))]),Object(a["createElementVNode"])("div",so,[Object(a["createElementVNode"])("ul",lo,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.uploadList,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("li",{key:t.fileId},[Object(a["createElementVNode"])("span",{title:t.title},Object(a["toDisplayString"])(n+1)+"、【"+Object(a["toDisplayString"])(t.tag)+"】 "+Object(a["toDisplayString"])(t.title),9,uo),Object(a["createElementVNode"])("img",{src:qi.a,alt:"",title:"点击删除",onClick:function(n){return e.handleRemove(t)}},null,8,po)])})),128))]),Object(a["createElementVNode"])("p",{class:"submit",onClick:t[2]||(t[2]=function(){return e.handleOK&&e.handleOK.apply(e,arguments)})},"确认")])])])])}var fo=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"codeTo","2"),Object(u["a"])(Object(c["a"])(e),"formId",void 0),Object(u["a"])(Object(c["a"])(e),"dialog",void 0),Object(u["a"])(Object(c["a"])(e),"localFile",{tag:"上传文件",value:"附件"}),Object(u["a"])(Object(c["a"])(e),"tags",[{tag:"收文",url:"https://sjk.jchc.cn/incomFile/remote/page",preview:"https://sjk.jchc.cn/incomFile/print"},{tag:"发文",url:"https://sjk.jchc.cn/outFile/remote/page",preview:"https://sjk.jchc.cn/outFile/print"},{tag:"签报",url:"https://sjk.jchc.cn/qianBao/remote/page",preview:"https://sjk.jchc.cn/qianBao/print"},{tag:"通用会商",url:"https://sjk.jchc.cn/consultation/remote/page-list",preview:"https://sjk.jchc.cn/webpage/views/office/consultation/print.jsp"},{tag:"采购审议事项",url:"https://sjkhrxt.jchc.cn/jchc-hr/tender/remote/pageYBList",preview:"https://sjkhrxt.jchc.cn/jchc-hr/webpage/views/tender/form.jsp"},{tag:"合同",url:"https://sjkhtglxt.jchc.cn/jchc-contract/contract/remote/getListForRelated",preview2:"https://sjkhtglxt.jchc.cn/jchc-contract/webpage/views/rightPages/contractOld-back-view.jsp?isView=0&fileLook=1",preview:"https://sjkhtglxt.jchc.cn/jchc-contract/webpage/views/rightPages/contract-back-view.jsp?isView=0&fileLook=1"},e.localFile]),Object(u["a"])(Object(c["a"])(e),"currentTag",e.tags[6]),Object(u["a"])(Object(c["a"])(e),"params",{word:"",size:15,number:0,deptId:"",orgId:"",beginTime:ba.datediff(-1,"month"),endTime:ba.datetime()}),Object(u["a"])(Object(c["a"])(e),"page",{data:[],count:0,size:e.params.size}),Object(u["a"])(Object(c["a"])(e),"uploadList",[]),Object(u["a"])(Object(c["a"])(e),"dialogPreview",void 0),e}return Object(r["a"])(n,[{key:"isCheck",value:function(e){return this.uploadList.some((function(t){return t.fileId===e.fileId}))}},{key:"handleTag",value:function(e){this.currentTag=e,this.handleQuery()}},{key:"handleQuery",value:function(){var e=this;if(!this.currentTag.url)return!1;var t={};"通用会商"==this.currentTag.tag?(t.orgId="qs00001",t.flowStatus=2,t.beginTime=this.params.beginTime,t.endTime=this.params.endTime):"合同"==this.currentTag.tag?(t.orgId=this.params.orgId,t.word=this.params.word,t.size=this.params.size,t.number=1,t.deptId=this.params.deptId,t.beginTime=this.params.beginTime,t.endTime=this.params.endTime):t=this.params,this.$logo(!0),this.$axios.getCrossOrigin(this.currentTag.url,{params:t}).then((function(t){e.$logo(!1),e.page.count=t.value.totalElements;var n=t.value.dataList?t.value.dataList:t.value.data;e.page.data=n.map((function(t){return{id:"",fileId:t.uuid,title:t.title,tag:e.currentTag.tag,datetime:t.createTime,fullDocNO:t.fullDocNO,preview:e.currentTag.preview,processInstanceId:t.processInstanceId,localfile:!1,oldState:null!=t.oldState?t.oldState:null}}))}))}},{key:"handlePage",value:function(e){this.params.number=e.curr-1,this.params.beginTime=e.stime,this.params.endTime=e.etime,this.handleQuery()}},{key:"handleCheck",value:function(e,t){var n=e.target.checked;if(n){var a={id:"",tag:"",title:"",localfile:!1};Object.assign(a,t),a.title=t.title,this.uploadList.push(a)}else this.uploadList=this.uploadList.filter((function(e){return e.fileId!==t.fileId}))}},{key:"handlePreview",value:function(e){var t="";t="合同"==this.currentTag.tag?e.oldState?this.currentTag.preview2+"&id="+e.fileId+"&flowInsId="+e.processInstanceId:this.currentTag.preview+"&id="+e.fileId+"&flowInsId="+e.processInstanceId:this.currentTag.preview+"?uuid="+e.fileId+"&auto=0",this.dialogPreview&&this.dialogPreview.isActive?this.dialogPreview.replace(pa,{src:t}):this.dialogPreview=this.$dialog({title:"关联文件预览",width:"49%",height:"100%",left:"100%",lock:!1,content:{component:pa,props:{src:t}}})}},{key:"handleUpload",value:function(e){this.uploadList.push({id:e.uuid,title:ba.date()+" "+e.fileName,tag:this.currentTag.value||this.currentTag.tag,localfile:!0})}},{key:"handleRemove",value:function(e){var t,n=this;e.id?(t=e.localfile?"/jchcfile/delete?id=".concat(e.id):"/filepaper/delete/".concat(e.id),this.$logo(!0),this.$axios.get(t).then((function(t){n.$logo(!1),0===t.code?n.uploadList=n.uploadList.filter((function(t){return t.id!==e.id})):n.$toast(t.msg)})).catch((function(e){return n.$toast(e.toString())}))):this.uploadList=this.uploadList.filter((function(t){return t.fileId!==e.fileId}))}},{key:"loadUploadFiles",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/filepaper/list?formId=".concat(this.formId)).then((function(e){e.data.map((function(e){t.uploadList.push({id:e.uuid,tag:e.fileTag,fileId:e.fileId,title:e.fileName,preview:e.url,processInstanceId:e.processInstanceId,localfile:!1})}))})).catch((function(e){t.$toast(e.toString())}));case 2:return e.next=4,this.$axios.get("/jchcfile/getFileByKind",{params:{formId:this.formId,kind:1}}).then((function(e){e.data.map((function(e){t.uploadList.push({id:e.uuid,tag:t.localFile.value||t.localFile.tag,title:e.uploadTime.substring(0,10)+" "+e.fileName,idocview:e.idocviewUuid,localfile:!0})}))})).catch((function(e){t.$toast(e.toString())}));case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"handleOK",value:function(){var e=this,t=this.uploadList.filter((function(e){return!1===e.localfile})).map((function(e){return{fileId:e.fileId,fileTag:e.tag,fileName:e.title,url:e.preview,processInstanceId:e.processInstanceId}})),n={formId:this.formId,fileList:t};this.$axios.post("/filepaper/insert",n).then((function(t){0===t.code?e.dialog._ok():e.$toast(t.msg)})).catch((function(t){return e.$toast(t.toString())}))}},{key:"mounted",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,ba.getUser();case 2:return t=e.sent,this.params.deptId=t.deptId,this.params.orgId=t.groupId,e.next=7,this.loadUploadFiles();case 7:this.handleQuery();case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}]),n}(d["b"]);fo=Object(E["a"])([Object(d["a"])({components:{Files:Na},props:{formId:String,dialog:Object}})],fo);var bo=fo;n("02212");const mo=g()(bo,[["render",ho],["__scopeId","data-v-f5f07af0"]]);var go=mo,vo=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"formId",void 0),Object(u["a"])(Object(c["a"])(e),"printUrl",""),Object(u["a"])(Object(c["a"])(e),"fileList",[]),e}return Object(r["a"])(n,[{key:"handlePreview",value:function(e){if(e.localfile)e.idocview&&ba.preview(e.idocview);else{var t="";t="合同"==e.tag?"?auto=0&id="+e.fileId+"&flowInsId="+e.processInstanceId:"?auto=0&uuid="+e.fileId,this.$dialog({title:"关联文件预览",width:"50%",height:"100%",left:"0%",content:{component:pa,props:{src:e.preview+t}}})}}},{key:"handleRemove",value:function(e){var t,n=this;t=e.localfile?"/jchcfile/delete?id="+e.id:"/filepaper/delete?id="+e.id,this.$logo(!0),this.$axios.get(t).then((function(t){0===t.code?(n.fileList=n.fileList.filter((function(t){return t.id!==e.id})),n.$logo(!1)):(n.$toast(t.msg),n.$logo(!1))})).catch((function(e){return n.$toast(e.toString())}))}},{key:"handleDownload",value:function(e){window.open(ba.rewrite("/file/download/"+e.id))}},{key:"handlePrint",value:function(e){this.printUrl=e.preview+"?uuid="+e.fileId+"&random="+Math.floor(1e4*Math.random())}},{key:"loadFiles",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.fileList.length=0,e.next=3,this.$axios.get("/filepaper/list?formId=".concat(this.formId)).then((function(e){e.data.map((function(e){t.fileList.push({id:e.uuid,fileId:e.fileId,tag:e.fileTag,title:e.fileName,preview:e.url,processInstanceId:e.processInstanceId,localfile:!1})}))})).catch((function(e){t.$toast(e.toString())}));case 3:return e.next=5,this.$axios.get("/jchcfile/getFileByKind",{params:{formId:this.formId,kind:1}}).then((function(e){e.data.map((function(e){t.fileList.push({id:e.uuid,tag:"附件",title:e.uploadTime.substring(0,10)+" "+e.fileName,idocview:e.idocviewUuid,localfile:!0})}))})).catch((function(e){return t.$toast(e.toString())}));case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"handleLink",value:function(){var e=this;this.$dialog({title:"选择关联文件",width:"50%",height:"100%",left:0,content:{component:go,props:{formId:this.formId},handle:!0},ok:function(){return e.loadFiles(),!0}})}},{key:"mounted",value:function(){this.loadFiles()}}]),n}(d["b"]);vo=Object(E["a"])([Object(d["a"])({props:{formId:String,upload:{type:Boolean,default:!1},remove:{type:Boolean,default:!1},download:{type:Boolean,default:!0},button:{type:String,default:"关联文件"}}})],vo);var jo=vo;n("147b");const yo=g()(jo,[["render",Mi],["__scopeId","data-v-1cfbd4a6"]]);var Oo=yo,ko=function(e){return Object(a["pushScopeId"])("data-v-4df09f4b"),e=e(),Object(a["popScopeId"])(),e},wo={class:"line-time-box-con"},xo={key:0,class:"line-time-box"},No=Object(a["createStaticVNode"])('<div class="line-time-item line-item-active currentLink" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 申请 </div><div class="line-time-icon" data-v-4df09f4b></div><div class="line-time-icon-arrow" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 发起审核 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 批示 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 审核 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 承办部门批办 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 阅办 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 承办部门审核 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 备案 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 备案审核 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 归档 </div><div class="line-time-icon" data-v-4df09f4b></div></div>',10),Eo=[No],Io={key:1,class:"line-time-box"},Co=Object(a["createStaticVNode"])('<div class="line-time-item line-item-active currentLink" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 申请 </div><div class="line-time-icon-arrow" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 审核 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 接收单位 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 备案 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 备案审核 </div><div class="line-time-icon" data-v-4df09f4b></div></div><div class="line-time-item" data-v-4df09f4b><div class="time-item-text" data-v-4df09f4b> 归档 </div><div class="line-time-icon" data-v-4df09f4b></div></div>',6),Do=[Co],So={key:1,class:"line-time-box"},To={class:"time-item-text"},Ao=ko((function(){return Object(a["createElementVNode"])("div",{class:"line-time-icon"},null,-1)})),Vo={key:0,class:"nowUser"},Bo={key:0,class:"urge"};function $o(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",wo,[e.isApply?(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:0},[e.isAsignet?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",xo,Eo)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Io,Do))],64)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",So,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.flowChartList,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:Object(a["normalizeClass"])(["line-time-item",{currentLink:1==t.state,pastLink:2==t.state}]),key:n},[Object(a["createElementVNode"])("div",To,Object(a["toDisplayString"])(t.activityName),1),Ao,Object(a["createElementVNode"])("div",{class:Object(a["normalizeClass"])({"line-time-icon-arrow":1==t.state||2==t.state})},null,2),t.userNames&&t.userNames.length>0?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Vo,[1==t.state&&e.isHandled?(Object(a["openBlock"])(),Object(a["createElementBlock"])("i",Bo)):Object(a["createCommentVNode"])("",!0),(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(t.userNames,(function(e,t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:t},Object(a["toDisplayString"])(e),1)})),128))])):Object(a["createCommentVNode"])("",!0)],2)})),128))]))])}var Lo=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"flowChartList",void 0),Object(u["a"])(Object(c["a"])(e),"isApply",void 0),Object(u["a"])(Object(c["a"])(e),"isAsignet",void 0),Object(u["a"])(Object(c["a"])(e),"isHandled",void 0),e}return n}(d["b"]);Lo=Object(E["a"])([Object(d["a"])({props:{flowChartList:Array,isApply:Boolean,isHandled:Boolean,isAsignet:Boolean}})],Lo);var Uo=Lo;n("42a1");const Fo=g()(Uo,[["render",$o],["__scopeId","data-v-4df09f4b"]]);var _o=Fo,Ro={class:"auth-con"},Mo={class:"tree-con"},Po={class:"btn-con"};function qo(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-ztree");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Ro,[Object(a["createElementVNode"])("div",Mo,[Object(a["createVNode"])(c,{settings:e.settings,nodes:e.list,onOnCheck:e.onCheck,onOnCreated:e.onCreated},null,8,["settings","nodes","onOnCheck","onOnCreated"])]),Object(a["createElementVNode"])("div",Po,[Object(a["createElementVNode"])("p",{onClick:t[0]||(t[0]=function(){return e.cancel&&e.cancel.apply(e,arguments)})},"取消"),Object(a["createElementVNode"])("p",{class:Object(a["normalizeClass"])({disabled:e.disabled}),onClick:t[1]||(t[1]=function(){return e.submit&&e.submit.apply(e,arguments)})},"确定",2)])])}var zo=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"zTree",{}),Object(u["a"])(Object(c["a"])(e),"list",void 0),Object(u["a"])(Object(c["a"])(e),"personData",void 0),Object(u["a"])(Object(c["a"])(e),"dialog",void 0),Object(u["a"])(Object(c["a"])(e),"disabled",!1),Object(u["a"])(Object(c["a"])(e),"settings",{check:{enable:!0,autoCheckTrigger:!0,chkStyle:"checkbox",radioType:"all",chkboxType:{Y:"s",N:"ps"}},data:{simpleData:{enable:!0,idKey:"id",pIdKey:"Pid",rootPId:"-1"}},view:{showIcon:!1}}),e}return Object(r["a"])(n,[{key:"mounted",value:function(){this.personData=[],this.getDisablde()}},{key:"onCreated",value:function(e){this.zTree=e}},{key:"onCheck",value:function(e,t,n){var a=this.zTree.getCheckedNodes(!0);this.personData=[];for(var i=0;i<a.length;i++)this.personData.push({id:a[i].id,name:a[i].name,deptId:a[i].deptId,groupId:a[i].groupId});this.getDisablde()}},{key:"getDisablde",value:function(){this.disabled=!1,0==this.personData.length&&(this.disabled=!0)}},{key:"submit",value:function(){if(this.disabled)return!1;this.dialog._ok(this.personData)}},{key:"cancel",value:function(){this.dialog._close()}}]),n}(d["b"]);zo=Object(E["a"])([Object(d["a"])({components:{},props:{list:Array,dialog:Object}})],zo);var Ho=zo;n("9bc4");const Wo=g()(Ho,[["render",qo],["__scopeId","data-v-1e7b954e"]]);var Qo=Wo,Yo=function(e){return Object(a["pushScopeId"])("data-v-6fbb0094"),e=e(),Object(a["popScopeId"])(),e},Go={style:{display:"flex"}},Ko=Yo((function(){return Object(a["createElementVNode"])("div",{class:"number"},null,-1)})),Xo={class:"left"},Jo={key:0,class:"username"},Zo={key:1,class:"username"},er={key:0,class:"time"},tr={class:"right"},nr={key:0,class:"textarea-con"},ar={class:"textarea no-print"},ir=["onUpdate:modelValue"],or={class:"icon-edit"},rr=["onClick"],cr=["onClick"],sr=["onClick"],lr=["onClick"],ur={key:0,class:"content"};function dr(e,t,n,i,o,r){return Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.dataList,(function(t,n){var i,o;return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{key:t.procId+n,class:"op_content"},[Object(a["createElementVNode"])("div",Go,[Ko,Object(a["createElementVNode"])("div",Xo,["running"===(null===(i=e.task)||void 0===i?void 0:i.nodeCode)&&e.task.taskKey===t.taskId?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Jo,[Object(a["createElementVNode"])("p",null,Object(a["toDisplayString"])(e.user.name)+" :",1)])):(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Zo,[Object(a["createElementVNode"])("p",null,Object(a["toDisplayString"])(t.userName)+" :",1),t.endTime?(Object(a["openBlock"])(),Object(a["createElementBlock"])("p",er,Object(a["toDisplayString"])(t.endTime),1)):Object(a["createCommentVNode"])("",!0)]))]),Object(a["createElementVNode"])("div",tr,["running"===(null===(o=e.task)||void 0===o?void 0:o.nodeCode)&&e.task.taskKey===t.taskId?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",nr,[Object(a["createElementVNode"])("div",ar,[Object(a["withDirectives"])(Object(a["createElementVNode"])("textarea",{cols:"60",rows:"2",title:"请输入意见","onUpdate:modelValue":function(e){return t.opinion=e},class:Object(a["normalizeClass"])(e.validate?"":"validate")},null,10,ir),[[a["vModelText"],t.opinion]])]),Object(a["createElementVNode"])("div",or,[Object(a["createElementVNode"])("i",{class:"auth no-print",onClick:function(n){return e.onAuth(t)}},null,8,rr),Object(a["createElementVNode"])("p",{class:"agree no-print",onClick:function(n){return e.onAgree(t)}},Object(a["toDisplayString"])(e.agreeText),9,cr),Object(a["createElementVNode"])("p",{class:"goback no-print",onClick:function(n){return e.onGoback(t)}},"退回",8,sr),Object(a["createElementVNode"])("p",{class:"disagree no-print",onClick:function(n){return e.onDisagree(t)}},"不同意",8,lr)])])):(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:1},[t.opinion?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",ur,Object(a["toDisplayString"])(t.opinion),1)):Object(a["createCommentVNode"])("",!0)],64))])])])})),128)}var pr={class:"set-tree"};function hr(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-ztree");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",pr,[Object(a["createVNode"])(c,{settings:e.settings,onBeforeCheck:e.beforeCheck,onOnCreated:e.onCreated,onOnCheck:e.onCheck},null,8,["settings","onBeforeCheck","onOnCreated","onOnCheck"])])}var fr=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"zTree",{}),Object(u["a"])(Object(c["a"])(e),"checkedUnit",void 0),Object(u["a"])(Object(c["a"])(e),"ajaxData",void 0),Object(u["a"])(Object(c["a"])(e),"nodes",void 0),Object(u["a"])(Object(c["a"])(e),"settings",{async:{enable:!0,autoParam:["id"],dataType:"json",type:"get",url:"",dataFilter:function(t,n,a){var i=a.data;return e.treeCheck(i),i}},check:{chkboxType:{Y:"",N:""},chkStyle:"radio",radioType:"all",enable:!0},data:{simpleData:{enable:!0,idKey:"id",pIdKey:"parentId",rootPId:"0"}},view:{showIcon:!1}}),e}return Object(r["a"])(n,[{key:"beforeCreate",value:function(){this.settings.async.url=ba.rewrite("/QSecurity/zTreeQSecurity?id="+this.ajaxData.deptId)}},{key:"treeCheck",value:function(e){for(var t=0,n=e.length;t<n;t++)e[t].id===this.checkedUnit.sealUserId&&(e[t].checked=!0)}},{key:"beforeCheck",value:function(e,t){}},{key:"onCheck",value:function(e,t,n){var a=this.zTree.getCheckedNodes(!0);a.length?(this.checkedUnit.sealDeptId=a[0].extAttrs.deptId,this.checkedUnit.sealDeptName=a[0].extAttrs.deptName,this.checkedUnit.sealUserId=a[0].id,this.checkedUnit.sealUserName=a[0].name,this.$emit("check",this.checkedUnit,a)):(this.checkedUnit.sealDeptId=null,this.checkedUnit.sealDeptName=null,this.checkedUnit.sealUserId=null,this.checkedUnit.sealUserName=null,this.$emit("check",this.checkedUnit,a))}},{key:"onCreated",value:function(e){this.zTree=e}}]),n}(d["b"]);fr=Object(E["a"])([Object(d["a"])({components:{},props:{checkedUnit:Object,type:String,nodes:Array,ajaxData:Object}})],fr);var br=fr;n("6e08");const mr=g()(br,[["render",hr],["__scopeId","data-v-14f39468"]]);var gr=mr,vr=function(e){return Object(a["pushScopeId"])("data-v-69ecd45f"),e=e(),Object(a["popScopeId"])(),e},jr={class:"dimension-container"},yr={class:"erweima"},Or=["src"],kr=vr((function(){return Object(a["createElementVNode"])("p",null,"请使用手机扫码二维码进行用印确认",-1)}));function wr(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",jr,[Object(a["createElementVNode"])("div",yr,[Object(a["createElementVNode"])("img",{class:"head-image",src:e.qrcodeForSession(),alt:"二维码"},null,8,Or)]),kr])}var xr=n("f121"),Nr=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"dialog",void 0),Object(u["a"])(Object(c["a"])(e),"dialogPreview",void 0),Object(u["a"])(Object(c["a"])(e),"$route",void 0),Object(u["a"])(Object(c["a"])(e),"taskId",void 0),Object(u["a"])(Object(c["a"])(e),"uuid",void 0),Object(u["a"])(Object(c["a"])(e),"websocket",null),Object(u["a"])(Object(c["a"])(e),"id",e.getQueryURL("id")),Object(u["a"])(Object(c["a"])(e),"client","PC"),Object(u["a"])(Object(c["a"])(e),"queryData",{}),e}return Object(r["a"])(n,[{key:"mounted",value:function(){xr["IS_PROD"]||window.open("http://".concat(xr["BASE_UAT_URL"],"/jchc-signet/weixin/dialog?formId=").concat(this.uuid,"&taskId=").concat(this.taskId,"&wx_user_id=",18115888466)),this.qrcodeForSession(),"WebSocket"in window?(this.websocket=new WebSocket(("https:"===location.protocol?"wss://":"ws://")+xr["BASE_URL"]+"/chat?formId="+this.uuid+"&taskId="+this.taskId+"&remote_client="+this.client),this.websocket.addEventListener("open",this.handleOpen,!1),this.websocket.addEventListener("close",this.handleClose,!1),this.websocket.addEventListener("error",this.handleError,!1),this.websocket.addEventListener("message",this.handleMessage,!1)):console.log("您的浏览器不支持使用 WebSoket!")}},{key:"handleOpen",value:function(e){console.log("handleOpen",e,"e")}},{key:"handleClose",value:function(){console.log("handleClose")}},{key:"handleError",value:function(){console.log("handleError")}},{key:"handleMessage",value:function(e){var t=this;t.queryData=JSON.parse(e.data).sealUserSockets[0],JSON.parse(e.data).sealUserSockets.length&&(this.websocket.close(),t.dialog._ok(t.queryData))}},{key:"getQueryURL",value:function(e){var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)"),n=window.location.search.substr(1).match(t);return null!=n?decodeURIComponent(n[2]):null}},{key:"qrcodeForSession",value:function(){var e=this;return ba.rewrite("/signet/qrcodeForSession?formId="+e.uuid+"&taskId="+e.taskId)}}]),n}(d["b"]);Nr=Object(E["a"])([Object(d["a"])({props:{taskId:String,uuid:String,dialog:Object},components:{}})],Nr);var Er=Nr;n("a295");const Ir=g()(Er,[["render",wr],["__scopeId","data-v-69ecd45f"]]);var Cr=Ir,Dr=function(e){return Object(a["pushScopeId"])("data-v-9598ce8c"),e=e(),Object(a["popScopeId"])(),e},Sr={class:"Dimension"},Tr=Dr((function(){return Object(a["createElementVNode"])("option",{value:""},"请选择摄像头",-1)})),Ar=["value"],Vr=Dr((function(){return Object(a["createElementVNode"])("br",null,null,-1)})),Br=Dr((function(){return Object(a["createElementVNode"])("video",{id:"video",width:"480",height:"320",controls:""},null,-1)})),$r=Dr((function(){return Object(a["createElementVNode"])("canvas",{id:"canvas",width:"480",height:"320"},null,-1)})),Lr={class:"btm"};function Ur(e,t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Sr,[Object(a["withDirectives"])(Object(a["createElementVNode"])("select",{id:"videoSelector",onChange:t[0]||(t[0]=function(){return e.videoSelector&&e.videoSelector.apply(e,arguments)}),"onUpdate:modelValue":t[1]||(t[1]=function(t){return e.currentMachine=t})},[Tr,(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.videoArr,(function(e,t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("option",{value:e.id,key:t},Object(a["toDisplayString"])(e.label),9,Ar)})),128))],544),[[a["vModelSelect"],e.currentMachine]]),Vr,Br,$r,Object(a["createElementVNode"])("div",Lr,[Object(a["createElementVNode"])("div",{class:"btm-left",onClick:t[2]||(t[2]=function(){return e.takePhoto&&e.takePhoto.apply(e,arguments)})},"拍照"),Object(a["createElementVNode"])("div",{class:"btm-right",onClick:t[3]||(t[3]=function(){return e.save&&e.save.apply(e,arguments)})},"保存")])])}n("3ca3"),n("ddb0"),n("2b3d"),n("9861"),n("fb6a"),n("bf19"),n("5cc6"),n("907a"),n("9a8c"),n("a975"),n("735e"),n("c1ac"),n("d139"),n("3a7b"),n("d5d6"),n("82f8"),n("e91f"),n("60bd"),n("5f96"),n("3280"),n("3fcc"),n("ca91"),n("25a1"),n("cd26"),n("3c5d"),n("2954"),n("649e"),n("219c"),n("170b"),n("b39a"),n("72f7"),n("f12d"),n("e462");var Fr=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"dialog",void 0),Object(u["a"])(Object(c["a"])(e),"obj",void 0),Object(u["a"])(Object(c["a"])(e),"files",void 0),Object(u["a"])(Object(c["a"])(e),"currentMachine",""),Object(u["a"])(Object(c["a"])(e),"context",void 0),Object(u["a"])(Object(c["a"])(e),"canvas",void 0),Object(u["a"])(Object(c["a"])(e),"video",null),Object(u["a"])(Object(c["a"])(e),"videoArr",[]),Object(u["a"])(Object(c["a"])(e),"modelSel",""),Object(u["a"])(Object(c["a"])(e),"myInterval",null),Object(u["a"])(Object(c["a"])(e),"hasPhoto",!1),e}return Object(r["a"])(n,[{key:"mounted",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.video=document.getElementById("video"),this.videoArr=[],navigator.mediaDevices.enumerateDevices().then((function(e){e.forEach((function(e,n){"videoinput"==e.kind&&t.videoArr.push({label:e.label.length?e.label:"deli"+n,id:e.deviceId?e.deviceId:"sxt"+n})}))})).catch((function(e){console.log(e.name+": "+e.message)})),e.next=5,this.$nextTick((function(){t.initDraw()}));case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"initDraw",value:function(){this.canvas=document.getElementById("canvas"),this.context=this.canvas.getContext("2d")}},{key:"getUserMedia",value:function(e,t,n){navigator.mediaDevices.getUserMedia?navigator.mediaDevices.getUserMedia(e).then(t).catch(n):navigator.webkitGetUserMedia?navigator.webkitGetUserMedia(e,t,n):navigator.mozGetUserMedia?navigator.mozGetUserMedia(e,t,n):navigator.getUserMedia&&navigator.getUserMedia(e,t,n)}},{key:"success",value:function(e){window.URL||window.webkitURL;var t=this.video;t.srcObject=e,t.play()}},{key:"error",value:function(e){console.log("访问用户媒体设备失败".concat(e.name,", ").concat(e.message))}},{key:"videoSelector",value:function(e){if(!this.currentMachine){var t=this.video;return t.srcObject=null,!1}navigator.mediaDevices.getUserMedia||navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia?this.getUserMedia({video:{width:480,height:320,deviceId:this.currentMachine}},this.success,this.error):alert("不支持访问用户媒体")}},{key:"takePhoto",value:function(){if(!this.currentMachine)return this.$toast("请选择摄像头"),!1;this.context.drawImage(this.video,0,0,480,320),this.hasPhoto=!0}},{key:"save",value:function(){var e=this;if(!this.hasPhoto)return this.$toast("请拍照后保存！"),!1;this.$logo(!0);var t=document.getElementById("canvas"),n=t.toDataURL("image/png"),a=new FormData;a.append("kind","5"),a.append("formId",this.obj.uuid),a.append("fileContent",this.base64toFile(n)),this.$axios.post("/jchcfile/upload",a).then((function(t){0===t.code?(e.dialog._ok(t),e.$logo(!1)):(e.$logo(!1),e.$toast(t.msg.toString()))}))}},{key:"base64toFile",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Date((new Date).getTime()+288e5).toJSON().replace("T","").slice(0,19).replace(/-/g,"").replace(/:/g,"")+"S",n=e.split(","),a=n[0].match(/:(.*?);/)[1],i=a.split("/")[1],o=atob(n[1]),r=o.length,c=new Uint8Array(r);while(r--)c[r]=o.charCodeAt(r);return new File([c],"".concat(t).concat(i),{type:a})}}]),n}(d["b"]);Fr=Object(E["a"])([Object(d["a"])({props:{obj:Object,files:Array,dialog:Object},components:{}})],Fr);var _r=Fr;n("a599");const Rr=g()(_r,[["render",Ur],["__scopeId","data-v-9598ce8c"]]);var Mr=Rr,Pr=n("952e"),qr=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"code",void 0),Object(u["a"])(this,"task",void 0),Object(u["a"])(this,"opinions",void 0),Object(u["a"])(this,"agreeText",Object(d["c"])({default:"同意"}))},zr=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"files",[]),Object(u["a"])(Object(c["a"])(e),"dataList",[]),Object(u["a"])(Object(c["a"])(e),"validate",!0),e}return Object(r["a"])(n,[{key:"user",get:function(){return JSON.parse(localStorage.getItem("pyh-yy-userData"))||{}}},{key:"handleOpinions",value:function(e){var t=this;e.forEach((function(e){e.taskId===t.code&&t.dataList.push(e)}))}},{key:"onAuth",value:function(e){this.$emit("auth",e)}},{key:"onPress",value:function(e){this.$emit("press",e)}},{key:"onRevoke",value:function(e){this.$emit("revoke",e)}},{key:"onAgree",value:function(e){this.$emit("agree",e)}},{key:"onGoback",value:function(e){if(!e.opinion||!e.opinion.trim())return this.$toast("请填写意见"),this.validate=!1,!1;this.validate=!0,this.$emit("goback",e.opinion)}},{key:"onDisagree",value:function(e){if(!e.opinion||!e.opinion.trim())return this.$toast("请填写意见"),this.validate=!1,!1;this.validate=!0,this.$emit("disagree",e)}}]),n}(d["b"].with(qr));zr=Object(E["a"])([Object(d["a"])({name:"opinions",components:{UserSetTree:xe,Files:Na,UnitTree:gr,Dimension:Cr,Photograph:Mr,ElRadio:Pr["a"],ElRadioGroup:Pr["b"]},created:function(){this.handleOpinions(this.opinions)},computed:{},watch:{opinions:{handler:function(e){this.handleOpinions(e)}}},emits:["auth","press","revoke","agree","goback","disagree","queryData"]})],zr);var Hr=zr;n("b560");const Wr=g()(Hr,[["render",dr],["__scopeId","data-v-6fbb0094"]]);var Qr=Wr,Yr=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"itemData",Object(d["c"])({default:{}}))},Gr=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"AllNode",f.state.AllNode),Object(u["a"])(Object(c["a"])(e),"authorizeList",[]),Object(u["a"])(Object(c["a"])(e),"jchcUser",{}),Object(u["a"])(Object(c["a"])(e),"fileList",[]),Object(u["a"])(Object(c["a"])(e),"opinionList",[]),Object(u["a"])(Object(c["a"])(e),"ajaxData",{}),Object(u["a"])(Object(c["a"])(e),"delMessage",[]),Object(u["a"])(Object(c["a"])(e),"newSignetItems",[]),Object(u["a"])(Object(c["a"])(e),"signetIcon",ba.rewrite("/jchcfile/download/")),Object(u["a"])(Object(c["a"])(e),"params",void 0),e}return Object(r["a"])(n,[{key:"created",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$route&&this.$route.query.auto?this.params={uuid:this.$route.query.id,activityId:this.$route.query.activityId,activityCode:this.$route.query.activityCode,activityName:this.$route.query.activityName,flowInstanceId:this.$route.query.flowInstanceId,taskId:this.$route.query.taskId,taskState:this.$route.query.taskState,flowState:this.$route.query.flowState,status:this.$route.query.status,orgId:this.$route.query.orgId}:this.params={uuid:this.itemData.uuid,activityId:this.itemData.activityId,activityCode:this.itemData.activityCode,activityName:this.itemData.activityName,taskId:this.itemData.taskId,taskState:this.itemData.taskState,flowState:this.itemData.flowState,status:this.itemData.status,flowInstanceId:this.itemData.flowInstanceId,orgId:this.itemData.orgId},e.next=3,ba.getUser();case 3:t=e.sent,this.jchcUser=t,this.getFiles(this.jchcUser.groupId);case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"mounted",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getFormData();case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"previewFile",value:function(e){ba.preview(e)}},{key:"signetItems",get:function(){var e,t,n,a=this,i=null!==(e=null===(t=this.ajaxData)||void 0===t||null===(n=t.signetType)||void 0===n?void 0:n.map((function(e){return{formId:a.ajaxData.uuid,isSelfOrg:e[0].split("&")[0],parentId:e[1].split("&")[1],parentName:e[1].split("&")[2],signetId:e[2].split("&")[1],signetName:e[2].split("&")[2],kind:e[2].split("&")[0],imgUrl:e[2].split("&")[3]}})))&&void 0!==e?e:{};return i}},{key:"archivesSort",get:function(){return ba.fillZero(this.ajaxData.archivesSort,3)}},{key:"showExtraOpninions",get:function(){var e,t;return 1==(null===(e=this.signetItems)||void 0===e||null===(t=e[0])||void 0===t?void 0:t.isSelfOrg)}},{key:"getFiles",value:function(e){var t=this;this.$axios.get("/jchcfile/getFileByKind?formId="+e+"&kind=8").then((function(e){if(0!==e.code)return t.$toast(e.msg),!1;t.fileList=e.data})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}))}},{key:"press",value:function(e){var t=this;this.$alert({content:"您确定要督办吗",lock:!0,ok:function(){return t.$logo(!0),t.$axios.get("/signet/press/"+e.taskId).then((function(e){if(0!==e.code)return t.$logo(!1),t.$toast(e.msg),!1;t.$logo(!1),t.$toast(e.data)})).catch((function(e){t.$logo(!1),t.$toast(e.toString())})),!0}})}},{key:"revoke",value:function(e){}},{key:"getOpinion",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/bpmmanage/nodeOpinion?procId="+this.params.flowInstanceId).then((function(e){if(0!==e.code)return t.$toast(e.msg),!1;t.opinionList=e.data})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getCode",value:function(){return ba.rewrite("/signet/qrcodeForDetails?uuid="+this.params.uuid+"&flowInstanceId="+this.params.flowInstanceId+"&status="+this.params.status+"&taskId="+this.params.taskId+"&activityCode="+this.params.activityCode+"&orgId="+this.params.orgId)}},{key:"getFormData",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/signet/get?id="+this.params.uuid).then((function(e){if(0!==e.code)return t.$toast(e.msg),!1;var n=e.data.itemsJsonData&&JSON.parse(e.data.itemsJsonData);t.ajaxData=e.data,t.ajaxData.signetType=n,t.newSignetItems=e.data.signetItems}));case 2:return e.next=4,this.getOpinion();case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}]),n}(d["b"].with(Yr));Gr=Object(E["a"])([Object(d["a"])({name:"handled",props:{itemData:Object},components:{Files:Na,LinkFilePack:Oo,Opinions:Qr,FlowChart:_o,CirculateDialog:Qo}})],Gr);var Kr=Gr;n("e866");const Xr=g()(Kr,[["render",Bi],["__scopeId","data-v-25aee7ab"]]);var Jr=Xr,Zr={class:"msg-con"},ec={class:"title"};function tc(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Zr,[Object(a["createElementVNode"])("div",ec,Object(a["toDisplayString"])(e.message),1),Object(a["createElementVNode"])("div",{class:"btn primary",onClick:t[0]||(t[0]=function(){return e.handleOK&&e.handleOK.apply(e,arguments)})},"确定")])}var nc=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"message",Object(d["c"])({default:""})),Object(u["a"])(this,"dialog",Object(d["c"])({default:null}))},ac=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){return Object(o["a"])(this,n),t.apply(this,arguments)}return Object(r["a"])(n,[{key:"handleOK",value:function(){this.dialog._ok(),this.dialog._close()}}]),n}(d["b"].with(nc));n("fc10");const ic=g()(ac,[["render",tc],["__scopeId","data-v-3cc80ed4"]]);var oc=ic,rc=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"dialog",Object(d["c"])({default:null}))},cc=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"list",[]),Object(u["a"])(Object(c["a"])(e),"userInfo",f.state.userInfo),Object(u["a"])(Object(c["a"])(e),"applyContent",""),Object(u["a"])(Object(c["a"])(e),"ableDayNum",3),e}return Object(r["a"])(n,[{key:"computeShowOperation",get:function(){return function(e){return 1===e.state}}},{key:"mounted",value:function(){this.getApplicationList()}},{key:"_click",value:function(e){var t=Object.assign({},e.row);t.formId=t.sigentUuid,t.uuid=t.sigentUuid,this.$dialog({title:"用印申请单",width:"50%",left:"0%",height:"100%",top:300,max:!1,content:{component:Jr,props:{itemData:t}},ok:function(){}})}},{key:"operation",value:function(e,t){var n=this;2===t?this.$dialog({title:"延期申请",width:400,height:200,top:300,max:!1,content:{component:oc,props:{message:"确认将上传期限延期到".concat(fa()().add(this.ableDayNum,"days").format("LL"),"？")},handle:!0},ok:function(){n.submitOperation(e,t)}}):this.submitOperation(e,t)}},{key:"submitOperation",value:function(e,t){var n=this;this.$axios.post("/signetAble/singAbleManage",{state:t,ableDayNum:this.ableDayNum,uuid:e.uuid,sigentUuid:e.sigentUuid}).then((function(e){var t=e.code,a=(e.data,e.msg);if(0!==t)return n.$toast(a),!1;n.$toast("操作成功"),n.getApplicationList()})).catch((function(e){n.$logo(!1),n.$toast(e.toString())}))}},{key:"getApplicationList",value:function(){var e=this;this.$axios.post("/signetAble/getListApply",{state:1}).then((function(t){var n=t.code,a=t.data,i=t.msg;if(0!==n)return e.$toast(i),!1;e.list=a})).catch((function(t){e.$logo(!1),e.$toast(t.toString())}))}},{key:"getDay",value:function(e){var t=new Date,n=t.setDate(t.getDate()+e);return fa()(new Date(n)).format("LL")}}]),n}(d["b"].with(rc));n("f2fe");const sc=g()(cc,[["render",Xa],["__scopeId","data-v-4e919dc9"]]);var lc=sc,uc=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"showPrint",!1),Object(u["a"])(Object(c["a"])(e),"showSubmit",!1),Object(u["a"])(Object(c["a"])(e),"showSave",!1),Object(u["a"])(Object(c["a"])(e),"showSet",!0),Object(u["a"])(Object(c["a"])(e),"userInfo",f.state.userInfo),Object(u["a"])(Object(c["a"])(e),"aggregation",[]),Object(u["a"])(Object(c["a"])(e),"applicationList",[]),e}return Object(r["a"])(n,[{key:"openSetting",value:function(){this.$dialog({title:"权限配置",max:!1,content:{component:Ha},width:900,height:780})}},{key:"mounted",value:function(){var e=this;this.$axios.get("/signet/selectDoneNUmber").then((function(t){if(0!==t.code)return e.$toast(t.msg),!1;e.aggregation=t.data})).catch((function(t){e.$logo(!1),e.$toast(t.toString())}))}},{key:"queryTable",value:function(e){this.$parent.$refs["left"].handleQuery({taskKey:e,pageNo:1})}},{key:"applyCount",get:function(){return this.aggregation.length>0&&"8c55dbab17b34d109a4ae57d9eabfac5"===this.aggregation[0].id?this.aggregation[0].count:0}},{key:"showApplication",get:function(){var e;return null===(e=f.state.permission)||void 0===e?void 0:e.application}},{key:"getApplicationList",value:function(){var e=this;this.$axios.post("/signetAble/getListApply",{state:1}).then((function(t){var n=t.code,a=t.data,i=t.msg;if(0!==n)return e.$toast(i),!1;e.applicationList=a})).catch((function(t){e.$logo(!1),e.$toast(t.toString())}))}},{key:"applicationDelay",value:function(){this.$dialog({title:"延期申请",width:1e3,height:"auto",top:300,content:{component:lc,props:{},handle:!0},max:!1})}},{key:"submit",value:function(){var e=this.$parent.$refs["right"];"handle"===e.$options.name&&e.submit()}},{key:"save",value:function(){var e=this.$parent.$refs["right"];"handle"===e.$options.name&&e.save()}},{key:"toApply",value:function(){this.$parent.toApply(),this.showPrint=!1,this.showSubmit=!0,this.showSave=!0}},{key:"toHandle",value:function(){this.$parent.toHandle(),this.showPrint=!0,this.showSubmit=!1,this.showSave=!1}}]),n}(d["b"]);uc=Object(E["a"])([Object(d["a"])({emits:["print"]})],uc);var dc=uc;n("541b");const pc=g()(dc,[["render",V],["__scopeId","data-v-5026b5db"]]);var hc=pc,fc=n("1b61"),bc=n.n(fc),mc=function(e){return Object(a["pushScopeId"])("data-v-723d1916"),e=e(),Object(a["popScopeId"])(),e},gc={class:"left"},vc=mc((function(){return Object(a["createElementVNode"])("div",{class:"title"},"用印申请审批列表",-1)})),jc={class:"query"},yc=["onClick"];function Oc(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-table-column"),s=Object(a["resolveComponent"])("vue-table"),l=Object(a["resolveComponent"])("pagination");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",gc,[vc,Object(a["createElementVNode"])("div",jc,[Object(a["withDirectives"])(Object(a["createElementVNode"])("input",{type:"text",class:"input","onUpdate:modelValue":t[0]||(t[0]=function(t){return e.query.title=t})},null,512),[[a["vModelText"],e.query.title]]),Object(a["createElementVNode"])("img",{src:bc.a,class:"search",onClick:t[1]||(t[1]=function(t){return e.handleQuery({pageNo:1})})})]),Object(a["createVNode"])(s,{hover:!0,pageNo:e.query.pageNo,dataList:e.dataList,rowClass:e._class,onRowClick:e._click},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{label:"序号",index:!0,width:50}),Object(a["createVNode"])(c,{label:"标题",prop:"title",align:"center",className:"cell-title"},{default:Object(a["withCtx"])((function(e){return[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.row.title),1)]})),_:1}),Object(a["createVNode"])(c,{label:"用印事由",prop:"signetName",align:"center",className:"cell-title"},{default:Object(a["withCtx"])((function(t){return[Object(a["createTextVNode"])(Object(a["toDisplayString"])(t.row.signetName)+" ",1),e.userInfo.id==t.row.userId&&"Apply"==t.row.taskKey?(Object(a["openBlock"])(),Object(a["createElementBlock"])("i",{key:0,onClick:Object(a["withModifiers"])((function(n){return e._delete(t.row)}),["stop"]),class:"icon-delete"},null,8,yc)):Object(a["createCommentVNode"])("",!0)]})),_:1}),Object(a["createVNode"])(c,{label:"印章状态",prop:"nodeStatus",width:80}),Object(a["createVNode"])(c,{label:"部门",width:120,prop:"deptName"}),Object(a["createVNode"])(c,{label:"申请时间",width:160,prop:"createTime"})]})),_:1},8,["pageNo","dataList","rowClass","onRowClick"]),Object(a["createVNode"])(l,{ref:"pagination",showTodo:!0,curr:e.query.pageNo,count:e.count,onPaging:e.pageQuery},null,8,["curr","count","onPaging"])])}var kc=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"userInfo",{}),Object(u["a"])(Object(c["a"])(e),"query",{pageNo:1,title:"",taskKey:"",beginTime:new Date,endTime:new Date}),Object(u["a"])(Object(c["a"])(e),"count",0),Object(u["a"])(Object(c["a"])(e),"dataList",[]),e}return Object(r["a"])(n,[{key:"_class",value:function(e){return"Apply"==e.row.nodeCode||"running"==e.row.nodeCode?"__blue_row":"comping"==e.row.nodeCode?"__green_row":"__black_row"}},{key:"_click",value:function(e){"running"==e.row.nodeCode||"Apply"==e.row.taskKey?(this.$parent.toHandle(),"Apply"==e.row.taskKey||"UserTask_1d3gb3d"==e.row.taskKey?(this.$parent.$refs.head.showPrint=!1,this.$parent.$refs.head.showSubmit=!0,this.$parent.$refs.head.showSave=!0):(this.$parent.$refs.head.showPrint=!0,this.$parent.$refs.head.showSubmit=!1,this.$parent.$refs.head.showSave=!1)):(this.$parent.toHandled(),this.$parent.$refs.head.showPrint=!0,this.$parent.$refs.head.showSubmit=!1,this.$parent.$refs.head.showSave=!1),this.$parent.itemData=e.row}},{key:"_delete",value:function(e){var t=this;this.$alert({content:"您确定要删除吗",lock:!0,ok:function(){return t.$axios.get("/signet/delete?id="+e.uuid).then((function(e){0!=e.code&&t.$toast(e.msg),t.$toast("删除成功"),t.load(),location.reload()})),!0}})}},{key:"pageQuery",value:function(e){Object.assign(this.query,{pageNo:e.curr,beginTime:e.stime,endTime:e.etime,nodeCode:e.nodeCode}),this.load()}},{key:"format",value:function(e){var t=e.getFullYear(),n=e.getMonth()+1,a=e.getDate(),i=e.getHours(),o=e.getMinutes(),r=e.getSeconds(),c=["00","01","02","03","04","05","06","07","08","09"],s=function(e){return c[e]||e};return t+"-"+s(n)+"-"+s(a)+" "+s(i)+":"+s(o)+":"+s(r)}},{key:"created",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=new Date,n=new Date,n.setDate(n.getDate()+1),t.setMonth(t.getMonth()-1),t.setHours(0),t.setMinutes(0),t.setSeconds(0),this.query.beginTime=this.format(t),this.query.endTime=this.format(n),e.next=11,ba.getUser();case 11:this.userInfo=e.sent,this.load({taskKey:"Apply,UserTask_1d3gb3d",nodeCode:"running"});case 13:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"load",value:function(e){var t=this;this.$logo(!0),this.$axios.get("/signet/pageTask",{params:Object.assign(this.query,e)}).then((function(e){t.count=e.data.total,t.dataList=e.data.records,e.data.records.forEach((function(e){"4"==e.status&&(e.activityName="不同意")})),t.$logo(!1)}))}},{key:"handleQuery",value:function(e){Object.assign(this.query,e),this.$refs.pagination.$forceUpdate(),this.load()}}]),n}(d["b"]);kc=Object(E["a"])([Object(d["a"])({props:{}})],kc);var wc=kc;n("7f41");const xc=g()(wc,[["render",Oc],["__scopeId","data-v-723d1916"]]);var Nc=xc,Ec=function(e){return Object(a["pushScopeId"])("data-v-6290cb1c"),e=e(),Object(a["popScopeId"])(),e},Ic={class:"right",id:"validateForm",ref:"rightFile"},Cc={class:"right-head"},Dc={class:"right-title"},Sc=Ec((function(){return Object(a["createElementVNode"])("span",null,"用印申请单",-1)})),Tc={class:"head-remark"},Ac={class:"head-left"},Vc=Ec((function(){return Object(a["createElementVNode"])("span",{class:"blue-text"},"申请人：",-1)})),Bc={key:0},$c={key:1},Lc={key:2},Uc={class:"head-right"},Fc={style:{}},_c={class:"right-main"},Rc={class:"table"},Mc=Ec((function(){return Object(a["createElementVNode"])("colgroup",null,[Object(a["createElementVNode"])("col",{width:"100px"}),Object(a["createElementVNode"])("col")],-1)})),Pc=Ec((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"用印事由",-1)})),qc=Ec((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"背景资料",-1)})),zc=Ec((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"用印文本",-1)})),Hc=Ec((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"印章类别",-1)})),Wc={class:"relativeTd"},Qc={class:"link-signet"},Yc=["src","alt","title"],Gc=["onClick"],Kc={class:"cascader-wrap"},Xc={key:1,class:"parent-link"},Jc=["src","alt","title"],Zc={key:0,class:"table opinion table-margin"},es=Ec((function(){return Object(a["createElementVNode"])("colgroup",null,[Object(a["createElementVNode"])("col",{width:"100px"}),Object(a["createElementVNode"])("col")],-1)})),ts={class:"blue-text"},ns={class:"file-list"},as=Ec((function(){return Object(a["createElementVNode"])("p",null,"相关制度",-1)})),is=["onClick"];function os(e,t,n,i,o,r){var c,s=Object(a["resolveComponent"])("link-file-pack"),l=Object(a["resolveComponent"])("select-file"),u=Object(a["resolveComponent"])("draggable"),d=Object(a["resolveComponent"])("el-cascader-panel"),p=Object(a["resolveComponent"])("opinions");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Ic,[Object(a["createElementVNode"])("div",Cc,[Object(a["createElementVNode"])("p",Dc,[Object(a["createTextVNode"])(Object(a["toDisplayString"])(null!==(c=e.itemData.orgName)&&void 0!==c?c:e.jchcUser.groupName)+" ",1),Sc]),Object(a["createElementVNode"])("div",Tc,[Object(a["createElementVNode"])("p",Ac,[Vc,e.ajaxData.groupId?e.ajaxData.groupId==e.jchcUser.groupId?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",$c,Object(a["toDisplayString"])(e.ajaxData.userName)+"("+Object(a["toDisplayString"])(e.ajaxData.deptName)+")",1)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",Lc,Object(a["toDisplayString"])(e.ajaxData.shortGroupName)+"("+Object(a["toDisplayString"])(e.ajaxData.deptName)+")",1)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",Bc,Object(a["toDisplayString"])(e.ajaxData.userName)+"("+Object(a["toDisplayString"])(e.ajaxData.deptName)+")",1))]),Object(a["createElementVNode"])("p",Uc,[Object(a["createElementVNode"])("span",Fc,Object(a["toDisplayString"])(e.ajaxData.submitTime||e.ajaxData.createTime),1)])])]),Object(a["createElementVNode"])("div",_c,[Object(a["createElementVNode"])("table",Rc,[Mc,Object(a["createElementVNode"])("tr",null,[Pc,Object(a["createElementVNode"])("td",null,[e.canEditForm?Object(a["withDirectives"])((Object(a["openBlock"])(),Object(a["createElementBlock"])("input",{key:0,type:"text","onUpdate:modelValue":t[0]||(t[0]=function(t){return e.ajaxData.title=t}),required:"",maxlength:"200",placeholder:"请输入",id:"btnA",class:Object(a["normalizeClass"])(e.bgSave&&!e.ajaxData.title?"mandatoryPink":""),autocomplete:"off"},null,2)),[[a["vModelText"],e.ajaxData.title]]):(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:1},[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.ajaxData.title),1)],64))])]),Object(a["createElementVNode"])("tr",null,[qc,Object(a["createElementVNode"])("td",null,[e.ajaxData.uuid?(Object(a["openBlock"])(),Object(a["createBlock"])(s,{key:0,ref:"sealFile",formId:e.ajaxData.uuid,upload:e.canEditForm,remove:e.canEditForm},null,8,["formId","upload","remove"])):Object(a["createCommentVNode"])("",!0)])]),Object(a["createElementVNode"])("tr",null,[zc,Object(a["createElementVNode"])("td",null,[e.ajaxData.uuid?(Object(a["openBlock"])(),Object(a["createBlock"])(l,{key:0,formId:e.ajaxData.uuid,number:e.ajaxData.signetAmount,"onUpdate:number":t[1]||(t[1]=function(t){return e.ajaxData.signetAmount=t}),groupId:e.ajaxData.groupId,upload:e.canEditForm,remove:e.canEditForm,ref:"yywb",onQueryGetFiles:e.queryGetFiles},null,8,["formId","number","groupId","upload","remove","onQueryGetFiles"])):Object(a["createCommentVNode"])("",!0)])]),Object(a["createElementVNode"])("tr",null,[Hc,Object(a["createElementVNode"])("td",Wc,[e.canEditSignetType?(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:0},[e.ajaxData.signetType?(Object(a["openBlock"])(),Object(a["createBlock"])(u,{key:0,modelValue:e.ajaxData.signetType,"onUpdate:modelValue":t[2]||(t[2]=function(t){return e.ajaxData.signetType=t}),"item-key":"index",class:"parent-link"},{item:Object(a["withCtx"])((function(t){var n=t.element;return[Object(a["createElementVNode"])("div",Qc,[Object(a["createElementVNode"])("img",{style:{width:"100%",height:"100%"},src:e.getSignetItem(n,"imgUrl"),alt:e.getSignetItem(n,"signetName"),title:e.getSignetItem(n,"signetName")},null,8,Yc),Object(a["createElementVNode"])("img",{src:X.a,class:"deletes",onClick:function(t){return e.deleteType(n)}},null,8,Gc)])]})),_:1},8,["modelValue"])):Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("button",{class:"button-signet",onClick:t[3]||(t[3]=Object(a["withModifiers"])((function(t){return e.showElCascader=!e.showElCascader}),["stop"]))},"选择印章"),Object(a["withDirectives"])(Object(a["createElementVNode"])("div",Kc,[Object(a["createVNode"])(d,{ref:"cascader",props:e.cascaderConfig,modelValue:e.ajaxData.signetType,"onUpdate:modelValue":t[4]||(t[4]=function(t){return e.ajaxData.signetType=t}),options:e.options},null,8,["props","modelValue","options"])],512),[[a["vShow"],e.showElCascader]])],64)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Xc,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.signetItems,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"link-signet",key:n},[Object(a["createElementVNode"])("img",{style:{width:"100%",height:"100%"},src:e.splitImageUrl(t.imgUrl),alt:t.signetName,title:t.signetName},null,8,Jc)])})),128))]))])])]),e.AllNode.length?(Object(a["openBlock"])(),Object(a["createElementBlock"])("table",Zc,[es,(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.AllNode,(function(t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("tr",{key:t.id},[Object(a["createElementVNode"])("td",ts,Object(a["toDisplayString"])(t.nodeName),1),Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(p,{opinions:e.opinionList,task:e.currentTask,code:t.nodeId,onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth},null,8,["opinions","task","code","onAgree","onDisagree","onGoback","onAuth"])])])})),128))])):Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("div",ns,[as,(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.fileList,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{class:"file",key:t.uuid},[Object(a["createElementVNode"])("span",{onClick:function(n){return e.previewFile(t.docViewUuid)}},Object(a["toDisplayString"])(n+1)+"、"+Object(a["toDisplayString"])(t.fileName),9,is)])})),128))])])],512)}n("caad"),n("2532"),n("e9c4");var rs=function(e){return Object(a["pushScopeId"])("data-v-0db1b109"),e=e(),Object(a["popScopeId"])(),e},cs={class:"submit-con"},ss={class:"submit-dialog"},ls=rs((function(){return Object(a["createElementVNode"])("p",null,"环节",-1)})),us={class:"tree-con"},ds={class:"btn-con",style:{display:"flex","align-items":"center","justify-content":"right"}};function ps(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-ztree");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",cs,[Object(a["createElementVNode"])("ul",ss,[Object(a["createElementVNode"])("li",null,[ls,Object(a["createElementVNode"])("div",us,[Object(a["createVNode"])(c,{settings:e.settings,nodes:e.list,onOnCheck:e.onCheck,onOnCreated:e.onCreated},null,8,["settings","nodes","onOnCheck","onOnCreated"])])])]),Object(a["createElementVNode"])("div",ds,[Object(a["createElementVNode"])("p",{onClick:t[0]||(t[0]=function(){return e.cancel&&e.cancel.apply(e,arguments)})},"取消"),Object(a["createElementVNode"])("p",{class:Object(a["normalizeClass"])({disabled:e.disabled}),onClick:t[1]||(t[1]=function(t){return e.submit()})},"确定",2)])])}var hs=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"list",void 0),Object(u["a"])(Object(c["a"])(e),"personData",void 0),Object(u["a"])(Object(c["a"])(e),"dialog",void 0),Object(u["a"])(Object(c["a"])(e),"disabled",!1),Object(u["a"])(Object(c["a"])(e),"zTree",{}),Object(u["a"])(Object(c["a"])(e),"settings",{check:{enable:!0,autoCheckTrigger:!0,chkStyle:"radio",radioType:"all",chkboxType:{Y:"s",N:"ps"}},data:{key:{},simpleData:{enable:!0,idKey:"id",pIdKey:"Pid",rootPId:"-1"}},view:{showIcon:!1}}),e}return Object(r["a"])(n,[{key:"created",value:function(){this.personData=[]}},{key:"submit",value:function(){this.dialog.ok(this.personData)}},{key:"onCreated",value:function(e){this.zTree=e}},{key:"onCheck",value:function(e,t,n){this.personData=this.zTree.getCheckedNodes(!0).map((function(e){return e.id}))}},{key:"cancel",value:function(){this.dialog._close()}}]),n}(d["b"]);hs=Object(E["a"])([Object(d["a"])({components:{},props:{list:Array,dialog:Object}})],hs);var fs=hs;n("b421");const bs=g()(fs,[["render",ps],["__scopeId","data-v-0db1b109"]]);var ms=bs,gs={class:"submit-con"},vs={class:"submit-dialog"},js={class:"tree-con"},ys={class:"btn-con",style:{display:"flex","align-items":"center","justify-content":"right"}};function Os(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("SubmitTree");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",gs,[Object(a["createElementVNode"])("ul",vs,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.list,(function(t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("li",{key:t.uuid},[Object(a["createElementVNode"])("p",null,Object(a["toDisplayString"])(t.taskName),1),Object(a["createElementVNode"])("div",js,[Object(a["createVNode"])(c,{onCheck:e.changePerson,activityCode:t.code,nodes:t,ref:t.code,checkedPerson:[]},null,8,["onCheck","activityCode","nodes"])])])})),128))]),Object(a["createElementVNode"])("div",ys,[Object(a["createElementVNode"])("p",{onClick:t[0]||(t[0]=function(){return e.cancel&&e.cancel.apply(e,arguments)})},"取消"),Object(a["createElementVNode"])("p",{class:Object(a["normalizeClass"])({disabled:e.disabled}),onClick:t[1]||(t[1]=function(t){return e.submit()})},"确定",2)])])}var ks={class:"submit-tree"};function ws(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-ztree");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",ks,[Object(a["createVNode"])(c,{settings:e.settings,nodes:e.nodes.extObj,onOnCheck:e.onCheck,onBeforeCheck:e.beforeCheck,onOnCreated:e.onCreated},null,8,["settings","nodes","onOnCheck","onBeforeCheck","onOnCreated"])])}var xs=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"checkedPerson",void 0),Object(u["a"])(this,"nodes",void 0),Object(u["a"])(this,"activityCode",void 0)},Ns=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"zTree",{}),Object(u["a"])(Object(c["a"])(e),"settings",{check:{enable:!0,autoCheckTrigger:!0,chkStyle:"checkbox",radioType:"all",chkboxType:{Y:"s",N:"ps"}},data:{key:{name:"fullname"},simpleData:{enable:!0,idKey:"id",pIdKey:"Pid",rootPId:"-1"}},view:{showIcon:!1}}),e}return Object(r["a"])(n,[{key:"created",value:function(){}},{key:"onCreated",value:function(e){this.zTree=e}},{key:"onCheck",value:function(e,t,n){var a=this.zTree.getCheckedNodes(!0);this.$emit("check",a)}},{key:"beforeCheck",value:function(){}}]),n}(d["b"].with(xs));Ns=Object(E["a"])([Object(d["a"])({components:{},props:{checkedPerson:Array,nodes:Object,activityCode:String}})],Ns);var Es=Ns;n("3a57");const Is=g()(Es,[["render",ws],["__scopeId","data-v-ffd55b72"]]);var Cs=Is,Ds=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"list",void 0),Object(u["a"])(Object(c["a"])(e),"taskId",void 0),Object(u["a"])(Object(c["a"])(e),"personData",void 0),Object(u["a"])(Object(c["a"])(e),"dialog",void 0),Object(u["a"])(Object(c["a"])(e),"disabled",!1),e}return Object(r["a"])(n,[{key:"created",value:function(){this.personData=[]}},{key:"submit",value:function(){this.dialog.ok(this.personData)}},{key:"changePerson",value:function(e){this.personData=e.map((function(e){return e.id}))}},{key:"cancel",value:function(){this.dialog._close()}}]),n}(d["b"]);Ds=Object(E["a"])([Object(d["a"])({components:{SubmitTree:Cs},props:{list:Array,taskId:String,dialog:Object}})],Ds);var Ss=Ds;n("9ad4");const Ts=g()(Ss,[["render",Os],["__scopeId","data-v-35567ef4"]]);var As=Ts,Vs={class:"chart"},Bs=Object(a["createStaticVNode"])("<li data-v-0b3e792c><p data-v-0b3e792c>拟办</p><b data-v-0b3e792c></b></li><li data-v-0b3e792c><p data-v-0b3e792c>拟办</p><b data-v-0b3e792c></b></li><li data-v-0b3e792c><p data-v-0b3e792c>拟办</p><b data-v-0b3e792c></b></li><li data-v-0b3e792c><p data-v-0b3e792c>拟办</p><b data-v-0b3e792c></b></li><li data-v-0b3e792c><p data-v-0b3e792c>拟办</p><b data-v-0b3e792c></b></li><li data-v-0b3e792c><p data-v-0b3e792c>拟办</p><b data-v-0b3e792c></b></li>",6),$s=[Bs];function Ls(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("ul",Vs,$s)}var Us=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"flowChartList",void 0),Object(u["a"])(Object(c["a"])(e),"isApply",void 0),Object(u["a"])(Object(c["a"])(e),"isAsignet",void 0),Object(u["a"])(Object(c["a"])(e),"isHandled",void 0),e}return n}(d["b"]);Us=Object(E["a"])([Object(d["a"])({props:{flowChartList:Array,isApply:Boolean,isHandled:Boolean,isAsignet:Boolean}})],Us);var Fs=Us;n("3e72");const _s=g()(Fs,[["render",Ls],["__scopeId","data-v-0b3e792c"]]);var Rs=_s,Ms={class:"auth-con"},Ps={class:"tree-con"},qs={class:"btn-con"};function zs(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-ztree");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Ms,[Object(a["createElementVNode"])("div",Ps,[Object(a["createVNode"])(c,{settings:e.settings,nodes:e.list,onOnCheck:e.onCheck,onOnCreated:e.onCreated},null,8,["settings","nodes","onOnCheck","onOnCreated"])]),Object(a["createElementVNode"])("div",qs,[Object(a["createElementVNode"])("p",{onClick:t[0]||(t[0]=function(){return e.cancel&&e.cancel.apply(e,arguments)})},"取消"),Object(a["createElementVNode"])("p",{class:Object(a["normalizeClass"])({disabled:e.disabled}),onClick:t[1]||(t[1]=function(){return e.submit&&e.submit.apply(e,arguments)})},"确定",2)])])}var Hs=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"zTree",{}),Object(u["a"])(Object(c["a"])(e),"list",void 0),Object(u["a"])(Object(c["a"])(e),"personData",void 0),Object(u["a"])(Object(c["a"])(e),"dialog",void 0),Object(u["a"])(Object(c["a"])(e),"disabled",!1),Object(u["a"])(Object(c["a"])(e),"settings",{check:{enable:!0,autoCheckTrigger:!0,chkStyle:"radio",radioType:"all",chkboxType:{Y:"s",N:"ps"}},data:{simpleData:{enable:!0,idKey:"id",pIdKey:"Pid",rootPId:"-1"}},view:{showIcon:!1}}),e}return Object(r["a"])(n,[{key:"mounted",value:function(){this.personData=[],this.getDisablde()}},{key:"onCreated",value:function(e){this.zTree=e}},{key:"onCheck",value:function(e,t,n){n.checked?this.personData=[{id:n.extAttrs.account,fullname:n.name,userNo:n.extAttrs.account}]:this.personData=[],this.getDisablde()}},{key:"getDisablde",value:function(){this.disabled=!1,0==this.personData.length&&(this.disabled=!0)}},{key:"submit",value:function(){if(this.disabled)return!1;this.dialog._ok(this.personData[0])}},{key:"cancel",value:function(){this.dialog._close()}}]),n}(d["b"]);Hs=Object(E["a"])([Object(d["a"])({components:{SubmitTree:Cs},props:{list:Array,dialog:Object}})],Hs);var Ws=Hs;n("b9db");const Qs=g()(Ws,[["render",zs],["__scopeId","data-v-6468f70a"]]);var Ys=Qs,Gs={class:"files"},Ks={class:"checkout"},Xs=Object(a["createTextVNode"])("已通过保密审查"),Js={class:"btn-con"};function Zs(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("el-checkbox"),s=Object(a["resolveComponent"])("el-checkbox-group");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Gs,[Object(a["createElementVNode"])("div",Ks,[Object(a["createVNode"])(s,{modelValue:e.checkList,"onUpdate:modelValue":t[0]||(t[0]=function(t){return e.checkList=t}),onChange:t[1]||(t[1]=function(t){return e.checkChange(e.checkList)})},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{label:"1"},{default:Object(a["withCtx"])((function(){return[Xs]})),_:1})]})),_:1},8,["modelValue"])]),Object(a["createElementVNode"])("div",Js,[Object(a["createElementVNode"])("p",{onClick:t[2]||(t[2]=function(){return e.cancel&&e.cancel.apply(e,arguments)})},"取消"),Object(a["createElementVNode"])("p",{class:Object(a["normalizeClass"])({disabled:e.disabled}),onClick:t[3]||(t[3]=function(t){return e.submit()})},"确定",2)])])}var el=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"checkList",[]),Object(u["a"])(Object(c["a"])(e),"disabled",!0),Object(u["a"])(Object(c["a"])(e),"dialog",void 0),e}return Object(r["a"])(n,[{key:"created",value:function(){}},{key:"checkChange",value:function(e){}},{key:"cancel",value:function(){var e=this;e.dialog._close()}},{key:"submit",value:function(){var e=this;e.dialog.ok(this.disabled)}}]),n}(d["b"]);el=Object(E["a"])([Object(d["a"])({props:{dialog:Object},watch:{checkList:function(e,t){e.length?this.disabled=!1:this.disabled=!0}}})],el);var tl=el;n("5633");const nl=g()(tl,[["render",Zs],["__scopeId","data-v-1419ff24"]]);var al=nl,il={class:"link-files"},ol={class:"link-custom"},rl=Object(a["createTextVNode"])(" 用印份数： "),cl=["value"],sl={key:1},ll={class:"file-list"},ul=["onClick"],dl=["onClick"],pl=["onClick"],hl=["src"];function fl(e,t,n,i,o,r){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",il,[e.upload?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:0,class:"link-button",onClick:t[0]||(t[0]=function(){return e.handleLink&&e.handleLink.apply(e,arguments)})},Object(a["toDisplayString"])(e.button),1)):Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("div",ol,[rl,e.upload?(Object(a["openBlock"])(),Object(a["createElementBlock"])("input",{key:0,type:"number",value:e.number,onInput:t[1]||(t[1]=function(t){return e.$emit("update:number",t.target.value)}),required:"",min:0},null,40,cl)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",sl,Object(a["toDisplayString"])(e.number),1))]),Object(a["createElementVNode"])("div",ll,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.fileList,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"file-item",key:t.uuid},[Object(a["createElementVNode"])("span",{title:"点击预览",onClick:function(n){return e.handlePreview(t)}},Object(a["toDisplayString"])(n+1)+"、 "+Object(a["toDisplayString"])(t.fileName),9,ul),t.localfile&&e.download?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:0,class:"icon-download",title:"点击下载",onClick:function(n){return e.handleDownload(t)}},null,8,dl)):Object(a["createCommentVNode"])("",!0),e.remove?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",{key:1,class:"icon-delete",title:"点击删除",onClick:function(n){return e.handleRemove(t)}},null,8,pl)):Object(a["createCommentVNode"])("",!0)])})),128))]),Object(a["createElementVNode"])("iframe",{id:"print-iframe",src:e.printUrl,style:{display:"none"}},null,8,hl)])}var bl=function(e){return Object(a["pushScopeId"])("data-v-614f4625"),e=e(),Object(a["popScopeId"])(),e},ml={class:"link-file"},gl={class:"left"},vl=["onClick"],jl={class:"right"},yl={class:"head"},Ol={class:"main"},kl={class:"con"},wl={key:0,class:"file-con"},xl={class:"file-list"},Nl={key:0},El=bl((function(){return Object(a["createElementVNode"])("p",null,"暂无数据！",-1)})),Il=[El],Cl=["value","onChange"],Dl=["title","onClick"],Sl={key:1,class:"upload"},Tl={class:"disabledEle"},Al=bl((function(){return Object(a["createElementVNode"])("p",null,"仅支持上传pdf格式文件",-1)})),Vl={class:"choosed"},Bl={class:"upload-list"},$l=["title"],Ll=["onClick"];function Ul(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("files");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",ml,[Object(a["createElementVNode"])("div",gl,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.tags,(function(t){return Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{key:t.tag,class:Object(a["normalizeClass"])({active:e.currentTag.tag===t.tag}),onClick:function(n){return e.handleTag(t)}},Object(a["toDisplayString"])(t.tag),11,vl)})),128))]),Object(a["createElementVNode"])("div",jl,[Object(a["createElementVNode"])("div",yl,[Object(a["withDirectives"])(Object(a["createElementVNode"])("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=function(t){return e.params.word=t}),placeholder:"输入关键字搜索"},null,512),[[a["vModelText"],e.params.word]]),Object(a["createElementVNode"])("button",{onClick:t[1]||(t[1]=function(){return e.handleQuery&&e.handleQuery.apply(e,arguments)})},"搜索")]),Object(a["createElementVNode"])("div",Ol,[Object(a["createElementVNode"])("div",kl,[e.currentTag.url?(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",wl,[Object(a["createElementVNode"])("ul",xl,[0===e.page.data.length?(Object(a["openBlock"])(),Object(a["createElementBlock"])("li",Nl,Il)):Object(a["createCommentVNode"])("",!0),(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.page.data,(function(n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("li",{key:n.uuid},[Object(a["withDirectives"])(Object(a["createElementVNode"])("input",{type:"checkbox",value:n.uuid,onChange:function(t){return e.handleCheck(t,n)},"onUpdate:modelValue":t[2]||(t[2]=function(t){return e.defaultCheckboxValue=t})},null,40,Cl),[[a["vModelCheckbox"],e.defaultCheckboxValue]]),Object(a["createElementVNode"])("span",{title:n.fileName,onClick:function(t){return e.handlePreview(n)}},Object(a["toDisplayString"])(n.fileName),9,Dl)])})),128))])])):(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Sl,[Object(a["withDirectives"])(Object(a["createVNode"])(c,{upload:!0,showUploadList:!1,button:"点击上传",params:{formId:e.formId,kind:4,type:1},onUpload:e.handleUpload,codeTo:e.codeTo,accept:".pdf"},null,8,["params","onUpload","codeTo","accept"]),[[a["vShow"],!e.uploadIsShow]]),Object(a["withDirectives"])(Object(a["createElementVNode"])("span",Tl,"点击上传",512),[[a["vShow"],e.uploadIsShow]]),Al]))]),Object(a["createElementVNode"])("div",Vl,[Object(a["createElementVNode"])("ul",Bl,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.uploadList,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("li",{key:t.fileId},[Object(a["createElementVNode"])("span",{title:t.fileName},Object(a["toDisplayString"])(n+1)+" "+Object(a["toDisplayString"])(t.fileName),9,$l),Object(a["createElementVNode"])("img",{src:qi.a,alt:"",title:"点击删除",onClick:function(n){return e.handleRemove(t)}},null,8,Ll)])})),128))]),Object(a["createElementVNode"])("p",{class:"submit",onClick:t[3]||(t[3]=function(){return e.handleSave&&e.handleSave.apply(e,arguments)})},"确认")])])])])}var Fl=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"codeTo","3"),Object(u["a"])(Object(c["a"])(e),"formId",void 0),Object(u["a"])(Object(c["a"])(e),"groupId",void 0),Object(u["a"])(Object(c["a"])(e),"dialog",void 0),Object(u["a"])(Object(c["a"])(e),"localFile",{tag:"上传文件",value:"附件"}),Object(u["a"])(Object(c["a"])(e),"uploadIsShow",!1),Object(u["a"])(Object(c["a"])(e),"checkedList",[]),Object(u["a"])(Object(c["a"])(e),"tags",[{tag:"文本模板",url:"/jchcfile/getFileByKind",preview:"https://sjk.jchc.cn/incomFile/print"},e.localFile]),Object(u["a"])(Object(c["a"])(e),"defaultCheckboxValue",[]),Object(u["a"])(Object(c["a"])(e),"currentTag",e.tags[0]),Object(u["a"])(Object(c["a"])(e),"params",{word:"",size:15,number:0,deptId:"",orgId:"",beginTime:ba.datediff(-1,"month"),endTime:ba.datetime()}),Object(u["a"])(Object(c["a"])(e),"page",{data:[],count:0,size:e.params.size}),Object(u["a"])(Object(c["a"])(e),"uploadList",[]),Object(u["a"])(Object(c["a"])(e),"dialogPreview",void 0),e}return Object(r["a"])(n,[{key:"handleTag",value:function(e){this.currentTag=e,this.handleQuery()}},{key:"handleQuery",value:function(){var e=this,t=null;t="上传文件"===this.currentTag.tag?{kind:4,formId:this.formId}:{kind:7,formId:this.groupId},this.$logo(!0,!0),this.$axios.get("/jchcfile/getFileByKind",{params:t}).then((function(t){t.code||(e.$logo(!1),e.page.data=t.data.map((function(e){return e})))})).catch((function(t){e.$toast(t)}))}},{key:"handleCheck",value:function(e,t){var n=JSON.parse(JSON.stringify(t));if(n.docId=n.uuid,n.type=0,delete n.uuid,e.target.checked)this.uploadList.push(n);else for(var a=0;a<this.uploadList.length;a++)t.uuid===this.uploadList[a].docId&&this.uploadList[a].uuid!==t.uuid&&this.handleRemove(this.uploadList[a]),this.uploadList[a]&&this.uploadList[a].docId&&this.uploadList[a].docId===t.uuid&&this.uploadList.splice(a,1),this.uploadList[a]&&this.uploadList[a].uuid&&this.uploadList[a].uuid===t.uuid&&this.handleRemove(this.uploadList[a])}},{key:"handlePreview",value:function(e){var t="http://192.168.8.54:8090/view/".concat(e.idocviewUuid);this.dialogPreview&&this.dialogPreview.isActive?this.dialogPreview.replace(pa,{src:t}):this.dialogPreview=this.$dialog({title:"关联文件预览",width:"49%",height:"100%",left:"100%",lock:!1,content:{component:pa,props:{src:t}}})}},{key:"handleUpload",value:function(e){this.uploadList.push({uuid:e.uuid,fileName:e.fileName,tag:this.currentTag.value||this.currentTag.tag,type:1})}},{key:"handleRemove",value:function(e){var t=this;e.uuid?this.$axios.get("/jchcfile/delete?id=".concat(e.uuid)).then((function(n){t.$logo(!1),n.code||(t.uploadList=t.uploadList.filter((function(t){return t.docId!==e.uuid&&t.uuid!==e.uuid})))})):this.uploadList=this.uploadList.filter((function(t){return t.docId!==e.docId&&t.uuid!==e.docId})),this.defaultCheckboxValue=this.defaultCheckboxValue.filter((function(t){return t!==e.docId&&t!==e.uuid}))}},{key:"loadUploadFiles",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/jchcfile/getFileByKind",{params:{formId:this.formId,kind:4}}).then((function(e){e.data.map((function(e){t.uploadList.push({uuid:e.uuid,tag:t.localFile.value||t.localFile.tag,fileName:e.fileName,idocview:e.idocviewUuid,type:e.type,docId:e.docId}),e.docId&&t.defaultCheckboxValue.push(e.docId)}))})).catch((function(e){t.$toast(e.toString())}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"handleSave",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this.uploadList.some((function(e){return 1===e.type})),n=this.uploadList.filter((function(e){return 1!==e.type})),!t){e.next=11;break}if(1!==this.uploadList.length){e.next=7;break}this.dialog._ok(),e.next=9;break;case 7:return e.next=9,this.getFileData(n);case 9:e.next=13;break;case 11:return e.next=13,this.getFileData(n);case 13:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getFileData",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(t){var n,a,i,o=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(n=JSON.parse(JSON.stringify(t)),a=0;a<n.length;a++)n[a].docId&&(n[a].uuid=n[a].docId);i={formId:this.formId,files:n},this.$axios.post("/jchcfile/copyReUpload",i).then((function(e){if(0!==e.code)return o.$toast(e.msg),!1;o.dialog._ok()}));case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"mounted",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,ba.getUser();case 2:return t=e.sent,this.params.deptId=t.deptId,this.params.orgId=t.groupId,e.next=7,this.loadUploadFiles();case 7:this.handleQuery();case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}]),n}(d["b"]);Fl=Object(E["a"])([Object(d["a"])({components:{Files:Na},props:{formId:String,groupId:String,dialog:Object,fileList:Array},watch:{uploadList:{handler:function(e){var t=this;e&&e.length?e.some((function(e){if(e&&e.type)return t.uploadIsShow=!0,!0;t.uploadIsShow=!1})):this.uploadIsShow=!1},deep:!0}}})],Fl);var _l=Fl;n("f6e8");const Rl=g()(_l,[["render",Ul],["__scopeId","data-v-614f4625"]]);var Ml=Rl,Pl=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"formId",void 0),Object(u["a"])(Object(c["a"])(e),"groupId",void 0),Object(u["a"])(Object(c["a"])(e),"printUrl",""),Object(u["a"])(Object(c["a"])(e),"fileList",[]),e}return Object(r["a"])(n,[{key:"handlePreview",value:function(e){this.$dialog({title:"关联文件预览",width:"50%",height:"100%",left:"0%",content:{component:pa,props:{src:"http://192.168.8.54:8090/view/".concat(e.idocviewUuid)}}})}},{key:"handleRemove",value:function(e){var t=this,n="/jchcfile/delete?id="+e.uuid;this.$logo(!0),this.$axios.get(n).then((function(n){0===n.code?(t.fileList=t.fileList.filter((function(t){return t.uuid!==e.uuid})),t.$logo(!1)):(t.$toast(n.msg),t.$logo(!1))})).catch((function(e){return t.$toast(e.toString())}))}},{key:"handleDownload",value:function(e){window.open(ba.rewrite("/file/download/"+e.uuid))}},{key:"loadFiles",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.fileList=[],e.next=3,this.$axios.get("/jchcfile/getFileByKind",{params:{formId:this.formId,kind:4}}).then((function(e){t.fileList=e.data,console.log("关联附件",t.fileList),t.$emit("queryGetFiles",e.data)})).catch((function(e){t.$toast(e.toString())}));case 3:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"handleLink",value:function(){var e=this;this.$dialog({title:"选择关联文件",width:"50%",height:"100%",left:0,content:{component:Ml,props:{formId:this.formId,groupId:this.groupId,fileList:this.fileList},handle:!0},ok:function(){var t=Object(re["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.loadFiles();case 2:return t.abrupt("return",!0);case 3:case"end":return t.stop()}}),t)})));function n(){return t.apply(this,arguments)}return n}(),close:function(){var t=Object(re["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.loadFiles();case 2:return t.abrupt("return",!0);case 3:case"end":return t.stop()}}),t)})));function n(){return t.apply(this,arguments)}return n}()})}},{key:"mounted",value:function(){this.loadFiles()}}]),n}(d["b"]);Pl=Object(E["a"])([Object(d["a"])({props:{groupId:String,formId:String,number:{type:Number,default:0},upload:{type:Boolean,default:!1},remove:{type:Boolean,default:!1},download:{type:Boolean,default:!0},button:{type:String,default:"选择文件"}}})],Pl);var ql=Pl;n("3862");const zl=g()(ql,[["render",fl],["__scopeId","data-v-bf6c94a8"]]);var Hl=zl,Wl=n("b76a"),Ql=n.n(Wl);fa.a.locale("zh-cn");var Yl=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"itemData",Object(d["c"])({default:{}}))},Gl=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"AllNode",f.state.AllNode),Object(u["a"])(Object(c["a"])(e),"randomKey",1e10*Math.random()),Object(u["a"])(Object(c["a"])(e),"queryDataQuery",[]),Object(u["a"])(Object(c["a"])(e),"queryDataList",[]),Object(u["a"])(Object(c["a"])(e),"queryDataQueryList",[]),Object(u["a"])(Object(c["a"])(e),"checkList",[]),Object(u["a"])(Object(c["a"])(e),"cascaderConfig",{multiple:!0,expandTrigger:"hover",label:"name",value:"value",children:"child"}),Object(u["a"])(Object(c["a"])(e),"options",[]),Object(u["a"])(Object(c["a"])(e),"jchcUser",{}),Object(u["a"])(Object(c["a"])(e),"opinionList",[]),Object(u["a"])(Object(c["a"])(e),"fileList",[]),Object(u["a"])(Object(c["a"])(e),"canEditForm",!0),Object(u["a"])(Object(c["a"])(e),"canEditSignetType",!0),Object(u["a"])(Object(c["a"])(e),"ajaxData",{uuid:"",title:"",signetAmount:0,signetType:[],submitTime:"",groupId:"",groupName:"",activityId:"",deptId:"",taskId:"",toActUsers:[],opinionText:""}),Object(u["a"])(Object(c["a"])(e),"yyr",!1),Object(u["a"])(Object(c["a"])(e),"gd",!1),Object(u["a"])(Object(c["a"])(e),"oldJsonData",[]),Object(u["a"])(Object(c["a"])(e),"linkTypes",["UserTask_1mrim5x","UserTask_1fxf0ax","UserTask_0p5302q","UserTask_0b71pec","UserTask_1buywe6","UserTask_0cn5jib"]),Object(u["a"])(Object(c["a"])(e),"delMessage",[]),Object(u["a"])(Object(c["a"])(e),"yyjlSignetItems",[]),Object(u["a"])(Object(c["a"])(e),"newSignetItems",[]),Object(u["a"])(Object(c["a"])(e),"bgSave",!1),Object(u["a"])(Object(c["a"])(e),"showElCascader",!1),Object(u["a"])(Object(c["a"])(e),"showUploadOrStamp",!0),Object(u["a"])(Object(c["a"])(e),"currentPerson",[]),Object(u["a"])(Object(c["a"])(e),"disabledRidioFiles",[]),Object(u["a"])(Object(c["a"])(e),"websocket",null),Object(u["a"])(Object(c["a"])(e),"currentTask",{}),e}return Object(r["a"])(n,[{key:"mounted",value:function(){var e=this;this.linkTypes.includes(this.itemData.taskKey)?(this.canEditForm=!1,this.canEditSignetType=!1):(this.canEditForm=!0,this.canEditSignetType=!0),window.addEventListener("click",(function(t){var n=document.querySelector(".cascader-wrap");1!=e.showElCascader||n.contains(t.target)||(e.showElCascader=!1)}))}},{key:"handleOpen",value:function(e){console.log("handleOpen",e,"e")}},{key:"handleClose",value:function(){console.log("handleClose")}},{key:"handleError",value:function(){console.log("handleError")}},{key:"handleMessage",value:function(e){this.randomKey=1e10*Math.random()}},{key:"signetItems",get:function(){var e=this,t=this.ajaxData.signetType.map((function(t){return{formId:e.ajaxData.uuid,isSelfOrg:t[0].split("&")[0],parentId:t[1].split("&")[1],parentName:t[1].split("&")[2],signetId:t[2].split("&")[1],signetName:t[2].split("&")[2],kind:t[2].split("&")[0],imgUrl:t[2].split("&")[3]}}));return t}},{key:"signetIds",get:function(){return this.signetItems.map((function(e){return e.signetId})).join(",")}},{key:"signetNames",get:function(){return this.signetItems.map((function(e){return e.signetName})).join(",")}},{key:"submitData",get:function(){var e,t=JSON.stringify(this.ajaxData.signetType),n={uuid:this.ajaxData.uuid,title:this.ajaxData.title,signetAmount:this.ajaxData.signetAmount,signetId:this.signetIds,signetName:this.signetNames,signetKind:this.signetItems.every((function(e){return 0==e.kind}))?0:1,signetItems:this.signetItems,itemsJsonData:t,taskId:this.itemData.taskId||null,taskUuid:(null===(e=this.ajaxData.task)||void 0===e?void 0:e.uuid)||null,flowInstanceId:this.itemData.flowInstanceId||null};return n.opinionText=this.ajaxData.opinionText,n}},{key:"archivesSort",get:function(){return ba.fillZero(this.ajaxData.archivesSort,3)}},{key:"getSignetItem",get:function(){return function(e,t){switch(t){case"imgUrl":return ba.rewrite("/jchcfile/download/"+e[2].split("&")[3]||!1);case"signetName":return e[2].split("&")[2];case"signetId":return e[2].split("&")[1];default:return""}}}},{key:"splitImageUrl",get:function(){return function(e){return ba.rewrite("/jchcfile/download/"+e||!1)}}},{key:"queryGetFiles",value:function(e){this.disabledRidioFiles=e}},{key:"getCode",value:function(){return ba.rewrite("/signet/qrcodeForDetails?uuid="+this.itemData.uuid+"&flowInstanceId="+this.itemData.flowInstanceId+"&status="+this.itemData.status+"&taskId="+this.itemData.taskId+"&activityCode="+this.itemData.activityCode+"&orgId="+this.itemData.orgId)}},{key:"previewFile",value:function(e){ba.preview(e)}},{key:"deleteType",value:function(e){this.ajaxData.signetType=this.ajaxData.signetType.filter((function(t){return t[2].split("&")[1]!==e[2].split("&")[1]}))}},{key:"agree",value:function(e){this.ajaxData.opinionText=e.opinion,this.submit()}},{key:"auth",value:function(e){var t=this;if(!e.opinion||!e.opinion.trim())return this.$toast("请填写意见"),!1;this.$axios.get("/plyh/qsecurity/zTreeQSecurity?id=ff8081817de6123d017e7ab0a7c601d1").then((function(n){if(0!==n.code)return t.$logo(!1),t.$toast(n.msg),!1;t.$dialog({title:"交办",max:!1,content:{component:Ys,props:{list:n.data},handle:!0},height:300,width:200,top:"50%",left:"90%",ok:function(n){t.$logo(!0),t.$axios.post("/bpmmanage/transfer",{usersInfo:n[0],taskId:t.itemData.taskId,opinion:e.opinion}).then((function(e){if(0!==e.code)return t.$logo(!1),t.$toast(e.msg),!1;location.reload()})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}))}})}))}},{key:"disagree",value:function(e){var t=this;this.$alert({content:"您确定要不同意吗",lock:!0,ok:function(){t.$logo(!0),t.$axios.post("/bpmmanage/terminate",{taskId:t.itemData.taskId,option:e.opinion,taskUuid:t.itemData.taskUuid}).then((function(e){if(0!==e.code)return t.$logo(!1),t.$toast(e.msg),!1;location.reload()})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}))}})}},{key:"goback",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(t){var n,a,i,o=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=this,this.$logo(!0,!0),e.next=4,this.$axios.get("/bpmmanage/getBackNodes?taskId="+this.ajaxData.task.taskId);case 4:a=e.sent,i=a.data,this.$logo(!1),this.$dialog({title:"退回",max:!1,content:{component:ms,props:{list:i},handle:!0},height:300,width:220,top:"30%",left:"90%",ok:function(e){o.$axios.post("/bpmmanage/freeJump",{priority:"1",toTaskDefKey:e.join(),opinion:t,processInstanceId:n.ajaxData.flowInstanceId,taskId:n.ajaxData.task.taskId})}});case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"save",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.bgSave=!0,this.ajaxData.title.trim()){e.next=6;break}return this.$toast("请填写“用印事由”"),e.abrupt("return");case 6:if(0!=this.$refs.yywb.fileList.length){e.next=11;break}return this.$toast("请上传“用印文本”"),e.abrupt("return");case 11:if(this.ajaxData.signetType.length){e.next=16;break}return this.$toast("请选择“印章类别”"),e.abrupt("return");case 16:this.bgSave=!0;case 17:t=this.submitData,this.$logo(!0),this.$axios.post("/signet/add",t).then((function(e){if(0!==e.code)return n.$logo(!1),n.$toast(e.msg),!1;location.reload()})).catch((function(e){n.$logo(!1),n.$toast(e.toString())}));case 20:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getNextNode",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(t){var n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.post("/bpmmanage/getSubNode",{taskId:t});case 2:return n=e.sent,a=n.data,e.abrupt("return",a);case 5:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"submit",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.ajaxData.task&&"Apply"!==this.ajaxData.task.taskKey){e.next=12;break}return e.next=3,this.verifyForm();case 3:if(t=e.sent,t){e.next=6;break}return e.abrupt("return",!1);case 6:return e.next=8,this.getNextNode();case 8:n=e.sent,this.chooseNodeUser(null,n,!0),e.next=16;break;case 12:return e.next=14,this.getNextNode();case 14:a=e.sent,this.chooseNodeUser(null,a,!1);case 16:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"chooseNodeUser",value:function(e,t,n){var a=this,i=this;this.$dialog({title:"提交",max:!1,content:{component:As,props:{taskId:e,list:t},handle:!0},height:300,width:350,top:"30%",left:"90%",ok:function(e){var t,o,r;n?a.firstSbumit(e):a.linkSubmission({formData:JSON.stringify({userList:e.join()}),taskId:null===(t=i.ajaxData)||void 0===t?void 0:t.task.taskId,taskUuid:null===(o=i.ajaxData)||void 0===o?void 0:o.task.uuid,uuid:null===(r=i.ajaxData)||void 0===r?void 0:r.uuid,opinion:i.ajaxData.opinionText})}})}},{key:"firstSbumit",value:function(e){var t=this,n=this.submitData;this.$logo(!0,!0),this.$axios.post("/signet/sumbit",Object.assign(n,{formData:JSON.stringify({userList:e.join()})})).then((function(e){if(0!==e.code)return t.$logo(!1),t.$toast(e.msg),!1;window.location.reload(),t.$logo(!1)})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}))}},{key:"linkSubmission",value:function(e){this.$logo(!0,!0),this.$axios.post("/bpmmanage/agreeActivity",e).then((function(e){0===e.code&&window.location.reload()}))}},{key:"verifyForm",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=!0,n=this.ajaxData,n.title.trim()){e.next=8;break}return this.$toast("请填写“用印事由”"),t=!1,e.abrupt("return",t);case 8:if(0!=this.$refs.yywb.fileList.length){e.next=14;break}return this.$toast("请上传“用印文本”"),t=!1,e.abrupt("return",t);case 14:if(n.signetType.length){e.next=18;break}return this.$toast("请选择“印章类别”"),t=!1,e.abrupt("return",t);case 18:return e.abrupt("return",t);case 19:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"queryData",value:function(e,t){this.queryDataQueryList=t,this.queryDataQuery=e}},{key:"submitPassed",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(t){var n,a,i,o,r,c=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("yyjl"!==t.activityCode){e.next=7;break}if(this.queryDataQuery.length){e.next=4;break}return this.$toast("请确认用印部门承办人!"),e.abrupt("return",!1);case 4:for(n=this.yyjlSignetItems.filter((function(e){return e.signetNum&&1==e.status})),a=0;a<n.length;a++)t.newSignetItems.push({formId:n[a].formId,imgUrl:null===(i=n[a].files[0])||void 0===i?void 0:i.imgUrl,kind:"".concat(null===(o=n[a].files[0])||void 0===o?void 0:o.kind),parentId:n[a].parentId,parentName:n[a].parentName,signetId:n[a].signetId,signetName:n[a].signetName,uuid:n[a].uuid,sealDeptId:this.queryDataQuery[0].deptId,sealDeptName:this.queryDataQuery[0].deptName,sealUserId:this.queryDataQuery[0].userId,sealUserName:this.queryDataQuery[0].userName,sealType:"拍照"===this.queryDataQueryList[0].sealName?1:0,sealOrgId:this.queryDataQuery[0].orgId,sealOrgName:this.queryDataQuery[0].orgName,sealCheckTime:this.queryDataQuery[0].createTime,signetNum:n[a].signetNum,sealsNum:n[a].sealsNum});t.toActUsers=[];case 7:r=function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return c.$logo(!0),e.next=3,c.$axios.post("/signet/submit",t).then((function(e){if(0!==e.code)return c.$logo(!1),c.$toast(e.msg),!1;location.reload()})).catch((function(e){c.$logo(!1),c.$toast(e.toString())}));case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),"zyfz"===t.activityCode?this.$alert({content:"确认同意?",lock:!0,ok:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r(t),e.abrupt("return",!0);case 2:case"end":return e.stop()}}),e)})));function n(){return e.apply(this,arguments)}return n}()}):r(t);case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"getOpinion",value:function(){var e=this;this.$logo(!0,!0),this.$axios.get("/bpmmanage/nodeOpinion?procId="+this.itemData.flowInstanceId).then((function(t){if(e.$logo(!1),0!==t.code)return e.$toast(t.msg),!1;e.opinionList=t.data})).catch((function(t){e.$logo(!1),e.$toast(t.toString())}))}},{key:"getFiles",value:function(e){var t=this;this.$axios.get("/jchcfile/getFileByKind?formId="+e+"&kind=8").then((function(e){if(0!==e.code)return t.$toast(e.msg),!1;t.fileList=e.data})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}))}},{key:"created",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.init();case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"newSignetIntiItem",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=[],e.next=3,this.$axios.get("/signet/yyjlSignetItems?taskId=".concat(this.itemData.taskId,"&uuid=").concat(this.itemData.uuid,"&flowInsId=").concat(this.itemData.flowInstanceId)).then((function(e){t=e.data})).catch((function(e){n.$logo(!1),n.$toast(e.toString())}));case 3:return e.abrupt("return",t);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"newSignetInti",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/signet/yyjlSignetItems?taskId=".concat(this.itemData.taskId,"&uuid=").concat(this.itemData.uuid,"&flowInsId=").concat(this.itemData.flowInstanceId)).then((function(e){t.yyjlSignetItems=e.data})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"init",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/signet/initDefConfigData").then((function(e){if(0!==e.code)return t.$toast(e.msg),!1;t.jchcUser=e.data.user,t.getFiles(t.jchcUser.groupId),t.itemData.uuid||(t.ajaxData.groupId=t.jchcUser.groupId,t.ajaxData.userName=t.jchcUser.name,t.ajaxData.deptName=t.jchcUser.deptName,t.ajaxData.groupName=t.jchcUser.groupName,t.ajaxData.deptId=t.jchcUser.deptId,t.ajaxData.uuid=e.data.uuid),window.localStorage.setItem("pyh-yy-userData",JSON.stringify(t.jchcUser));var n=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t.map((function(t){var a,i,o,r,c=null!==(a=t.files)&&void 0!==a&&a.length?"&"+(null===(i=t.files)||void 0===i||null===(o=i[0])||void 0===o?void 0:o.uuid):"",s=0===n?(t.isSelfOrg?0:1)+"&"+t.name:t.kind+"&"+t.uuid+"&"+t.name+c;return null!==(r=t.child)&&void 0!==r&&r.length?t.child=e(t.child,n+1):1===n&&(t.disabled=!0),Object(Pt["a"])(Object(Pt["a"])({},t),{},{value:s})}))};t.options=n(e.data.signetList||[]),t.getData()})).catch((function(e){t.$toast(e.toString())}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getData",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.ajaxData.submitTime=ba.datetime(),!this.itemData.uuid){e.next=4;break}return e.next=4,this.getFormData();case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getFormData",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/signet/get?id="+this.itemData.uuid+"&taskUuid="+this.itemData.taskUuid).then(function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(n){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0===n.code){e.next=3;break}return t.$toast(n.msg),e.abrupt("return",!1);case 3:a=n.data.itemsJsonData&&JSON.parse(n.data.itemsJsonData),t.ajaxData=n.data,t.ajaxData.signetType=a,t.oldJsonData=a,t.currentTask=n.data.task||{},t.getOpinion(),t.newSignetItems=n.data.signetItems;case 10:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$logo(!1),t.$toast(e.toString())}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"searchUsers",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/sealUserSocket/listByFormIdAndTaskId?formId="+this.ajaxData.uuid+"&taskId="+this.itemData.taskId).then((function(e){t.queryDataList=e.data}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}]),n}(d["b"].with(Yl));Gl=Object(E["a"])([Object(d["a"])({name:"handle",components:{draggable:Ql.a,Files:Na,LinkFilePack:Oo,FlowCharts:Rs,Opinions:Qr,isSecrecy:al,SelectFile:Hl},watch:{signetItems:{handler:function(e){this.options=this.options.map((function(t){var n,a=t.isSelfOrg!==(0==(null===(n=e[0])||void 0===n?void 0:n.isSelfOrg));return Object(Pt["a"])(Object(Pt["a"])({},t),{},{disabled:!!e.length&&a})}))},deep:!0},ajaxData:{handler:function(e){this.yyjlSignetItems=this.yyjlSignetItems.map((function(t){var n;return Object(Pt["a"])(Object(Pt["a"])({},t),{},{signetNum:null!==(n=t.signetNum)&&void 0!==n?n:e.signetAmount})}))}}}})],Gl);var Kl=Gl;n("c32f");const Xl=g()(Kl,[["render",os],["__scopeId","data-v-6290cb1c"]]);var Jl=Xl,Zl=function(e){return Object(a["pushScopeId"])("data-v-61b374fe"),e=e(),Object(a["popScopeId"])(),e},eu={class:"pms-con"},tu={key:0,class:"title"},nu={key:1,class:"title"},au=["onClick"],iu=Zl((function(){return Object(a["createElementVNode"])("p",{class:"title"},"如有特殊情况，请提交延期申请。",-1)})),ou={class:"btn-con"};function ru(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("vue-table-column"),s=Object(a["resolveComponent"])("vue-table"),l=Object(a["resolveComponent"])("a-textarea");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",eu,[e.computeCanIApplication?(Object(a["openBlock"])(),Object(a["createElementBlock"])("p",tu," 以下的用印申请单已超过期限需要您上传扫描件，请先完成上传再使用用印模块。 ")):(Object(a["openBlock"])(),Object(a["createElementBlock"])("p",nu,"延期申请已提交，请等待处理。")),Object(a["createVNode"])(s,{hover:!0,dataList:e.list,class:"table"},{default:Object(a["withCtx"])((function(){return[Object(a["createVNode"])(c,{label:"序号",index:!0,width:50}),Object(a["createVNode"])(c,{label:"用印事由",prop:"sigentName"},{default:Object(a["withCtx"])((function(t){return[Object(a["createElementVNode"])("span",{class:"cell-title",onClick:function(n){return e._click(t)}},Object(a["toDisplayString"])(t.row.sigentName),9,au)]})),_:1}),Object(a["createVNode"])(c,{label:"申请人",width:120,prop:"userName"}),Object(a["createVNode"])(c,{label:"状态",width:120,prop:"state"},{default:Object(a["withCtx"])((function(t){return[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.computeState(t.row.state)),1)]})),_:1}),Object(a["createVNode"])(c,{label:"上传期限",width:160,prop:"endTime"})]})),_:1},8,["dataList"]),e.computeCanIApplication?(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:2},[iu,Object(a["createVNode"])(l,{class:"content",value:e.applyContent,"onUpdate:value":t[0]||(t[0]=function(t){return e.applyContent=t}),autoSize:{minRows:5,maxRows:5},placeholder:"请输入申请内容"},null,8,["value"]),Object(a["createElementVNode"])("div",ou,[Object(a["createElementVNode"])("p",{onClick:t[1]||(t[1]=function(){return e.submit&&e.submit.apply(e,arguments)})},"提交申请")])],64)):Object(a["createCommentVNode"])("",!0)])}var cu=function e(){Object(o["a"])(this,e),Object(u["a"])(this,"list",Object(d["c"])({default:[]})),Object(u["a"])(this,"dialog",Object(d["c"])({default:null}))},su=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"userInfo",f.state.userInfo),Object(u["a"])(Object(c["a"])(e),"applyContent",""),e}return Object(r["a"])(n,[{key:"computeState",get:function(){return function(e){switch(e){case 0:return"开始";case 1:return"延时申请";case 2:return"同意";case 3:return"不同意";default:return"添加"}}}},{key:"computeCanIApplication",get:function(){return this.list.filter((function(e){return 1!==e.state})).length}},{key:"_click",value:function(e){var t=Object.assign({},e.row);t.formId=t.sigentUuid,t.uuid=t.sigentUuid,this.$dialog({title:"用印申请单",width:"50%",left:"0%",height:"100%",top:300,max:!1,content:{component:Jr,props:{itemData:t}},ok:function(){}})}},{key:"submit",value:function(){var e=this;if(this.applyContent){var t=this.list.map((function(e){return e.sigentUuid})).join(","),n=this.list.map((function(e){return e.uuid})).join(",");console.log(this.list,t,n),this.$axios.post("/signetAble/singAbleApply",{uuid:n,sigentUuid:t,state:1,applyContent:this.applyContent}).then((function(t){if(0!==t.code)return e.$toast(t.msg),!1;e.$toast("提交成功"),e.dialog._close(),e.dialog._ok()})).catch((function(t){e.$logo(!1),e.$toast(t.toString())}))}else this.$toast("请填写申诉内容")}}]),n}(d["b"].with(cu));n("ac30");const lu=g()(su,[["render",ru],["__scopeId","data-v-61b374fe"]]);var uu=lu,du=function(){function e(t,n){Object(o["a"])(this,e),Object(u["a"])(this,"options",void 0),Object(u["a"])(this,"dom",void 0),this.options=this.extend({noPrint:".no-print",onStart:function(){},onEnd:function(){}},n),this.dom="string"===typeof t?document.querySelector(t):t,this.init()}return Object(r["a"])(e,[{key:"init",value:function(){var e=this.getStyle()+this.getHtml();this.writeIframe(e)}},{key:"extend",value:function(e,t){for(var n in t)e[n]=t[n];return e}},{key:"getStyle",value:function(){for(var e="",t=document.querySelectorAll("style,link"),n=0;n<t.length;n++)e+=t[n].outerHTML;return e+="<style>"+(this.options.noPrint?this.options.noPrint:".no-print")+"{display:none;}</style>",e}},{key:"getHtml",value:function(){var e=document.querySelectorAll("input"),t=document.querySelectorAll("textarea"),n=document.querySelectorAll("select");for(var a in e)"checkbox"==e[a].type||"radio"==e[a].type?1==e[a].checked?e[a].setAttribute("checked","checked"):e[a].removeAttribute("checked"):"text"==e[a].type&&e[a].setAttribute("value",e[a].value);for(var i in t)"textarea"==t[i].type&&(t[i].innerHTML=t[i].value);for(var o in n)if("select-one"==n[o].type){var r=n[o].children;for(var c in r)"OPTION"==r[c].tagName&&(1==r[c].selected?r[c].setAttribute("selected","selected"):r[c].removeAttribute("selected"))}return this.dom.outerHTML}},{key:"writeIframe",value:function(e){var t,n,a=document.createElement("iframe"),i=document.body.appendChild(a);a.id="myIframe",a.style="position:absolute;width:0;height:0;top:-10px;left:-10px;",t=i.contentWindow||i.contentDocument,n=i.contentDocument||i.contentWindow.document,n.open(),n.write(e),n.close(),this.toPrint(t,(function(){document.body.removeChild(a)}))}},{key:"toPrint",value:function(e,t){var n=this;e.onload=function(){try{setTimeout((function(){e.focus(),"function"===typeof n.options.onStart&&n.options.onStart(),e.document.execCommand("print",!1,null)||e.print(),"function"===typeof n.options.onEnd&&n.options.onEnd(),e.close(),t&&t()}))}catch(a){console.log("err",a)}}}}]),e}(),pu=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"currentTabComponent",null),Object(u["a"])(Object(c["a"])(e),"randomKey",0),Object(u["a"])(Object(c["a"])(e),"itemData",{uuid:""}),Object(u["a"])(Object(c["a"])(e),"printUrl",""),Object(u["a"])(Object(c["a"])(e),"styleFlag",!1),e}return Object(r["a"])(n,[{key:"mounted",value:function(){}},{key:"handlePrint",value:function(){new du(this.$refs.right.$el)}},{key:"getUserInfo",value:function(){this.$axios.post("/signetAble/getLogerInfo").then((function(e){var t=e.code,n=e.data;e.msg;0===t&&f.commit("setUser",{user:n})}))}},{key:"getSysPermission",value:function(){var e=this;this.$axios.post("/signetAble/hasAble").then((function(t){var n=t.code,a=t.data,i=t.msg;if(0!==n)return e.$toast(i),!1;a.length?(f.commit("setPermission",{permission:{application:!1}}),e.$dialog({title:"提示",width:825,height:"auto",top:300,content:{component:uu,props:{list:a},handle:!0},max:!1,ok:function(){e.getSysPermission()}})):f.commit("setPermission",{permission:{application:!0}})})).catch((function(t){e.$toast(t.toString())}))}},{key:"toApply",value:function(){this.itemData={},this.randomKey=Math.floor(1e4*Math.random()),this.currentTabComponent=Jl,this.styleFlag=!0}},{key:"toHandle",value:function(){this.randomKey=Math.floor(1e4*Math.random()),this.currentTabComponent=Jl,this.styleFlag=!0}},{key:"toHandled",value:function(){this.randomKey=Math.floor(1e4*Math.random()),this.currentTabComponent=Jr,this.styleFlag=!0}}]),n}(d["b"]);pu=Object(E["a"])([Object(d["a"])({components:{Head:hc,Left:Nc,Handle:Jl,Handled:Jr}})],pu);var hu=pu;n("2dfe");const fu=g()(hu,[["render",N],["__scopeId","data-v-20693817"]]);var bu=fu,mu=function(e){return Object(a["pushScopeId"])("data-v-4c14b65e"),e=e(),Object(a["popScopeId"])(),e},gu={class:"right",id:"validateForm",ref:"rightFile"},vu={class:"right-head"},ju={class:"right-title"},yu=mu((function(){return Object(a["createElementVNode"])("span",null,"用印申请单",-1)})),Ou={class:"head-remark"},ku={class:"head-left"},wu=mu((function(){return Object(a["createElementVNode"])("span",{class:"blue-text"},"申请人：",-1)})),xu={key:0},Nu={key:1},Eu={key:2},Iu={class:"head-right"},Cu={style:{}},Du=["src"],Su={class:"right-main"},Tu={class:"table"},Au=mu((function(){return Object(a["createElementVNode"])("colgroup",null,[Object(a["createElementVNode"])("col",{width:"100px"}),Object(a["createElementVNode"])("col")],-1)})),Vu=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"用印事由",-1)})),Bu=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"背景资料",-1)})),$u=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"用印文本",-1)})),Lu=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"印章类别",-1)})),Uu={class:"relativeTd"},Fu={class:"link-signet"},_u=["src","alt","title"],Ru=["onClick"],Mu={class:"cascader-wrap"},Pu={key:1,class:"parent-link"},qu=["src","alt","title"],zu={key:0,class:"table opinion table-margin"},Hu=mu((function(){return Object(a["createElementVNode"])("colgroup",null,[Object(a["createElementVNode"])("col",{width:"100px"}),Object(a["createElementVNode"])("col")],-1)})),Wu=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"经办部门审核",-1)})),Qu=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},[Object(a["createTextVNode"])(" 主要 "),Object(a["createElementVNode"])("br"),Object(a["createTextVNode"])(" 负责人审核 ")],-1)})),Yu={class:"table opinion table-margin"},Gu=mu((function(){return Object(a["createElementVNode"])("colgroup",null,[Object(a["createElementVNode"])("col",{width:"100px"}),Object(a["createElementVNode"])("col")],-1)})),Ku=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"部门审核",-1)})),Xu=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"职能部门",-1)})),Ju={key:0},Zu={key:1},ed=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"分管领导",-1)})),td=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"主要领导",-1)})),nd=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"用印记录",-1)})),ad=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"上传",-1)})),id=mu((function(){return Object(a["createElementVNode"])("td",{class:"blue-text"},"归档",-1)})),od={class:"file-list"},rd=mu((function(){return Object(a["createElementVNode"])("p",null,"相关制度",-1)})),cd=["onClick"];function sd(e,t,n,i,o,r){var c=Object(a["resolveComponent"])("link-file-pack"),s=Object(a["resolveComponent"])("files"),l=Object(a["resolveComponent"])("draggable"),u=Object(a["resolveComponent"])("el-cascader-panel"),d=Object(a["resolveComponent"])("opinions");return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",gu,[Object(a["createElementVNode"])("div",vu,[Object(a["createElementVNode"])("p",ju,[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.jchcUser.groupName)+" ",1),yu]),Object(a["createElementVNode"])("div",Ou,[Object(a["createElementVNode"])("p",ku,[wu,e.ajaxData.groupId?e.ajaxData.groupId==e.jchcUser.groupId?(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",Nu,Object(a["toDisplayString"])(e.ajaxData.userName)+"("+Object(a["toDisplayString"])(e.ajaxData.deptName)+")",1)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",Eu,Object(a["toDisplayString"])(e.ajaxData.shortGroupName)+"("+Object(a["toDisplayString"])(e.ajaxData.deptName)+")",1)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("span",xu,Object(a["toDisplayString"])(e.ajaxData.userName)+"("+Object(a["toDisplayString"])(e.ajaxData.deptName)+")",1))]),Object(a["createElementVNode"])("p",Iu,[Object(a["createElementVNode"])("span",Cu,[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.ajaxData.submitTime||e.ajaxData.createTime)+" ",1),Object(a["withDirectives"])(Object(a["createElementVNode"])("img",{class:"head-image",src:e.getCode(),alt:"二维码"},null,8,Du),[[a["vShow"],e.itemData.taskId]])])])])]),Object(a["createElementVNode"])("div",Su,[Object(a["createElementVNode"])("table",Tu,[Au,Object(a["createElementVNode"])("tr",null,[Vu,Object(a["createElementVNode"])("td",null,[e.canEditForm?Object(a["withDirectives"])((Object(a["openBlock"])(),Object(a["createElementBlock"])("input",{key:0,type:"text","onUpdate:modelValue":t[0]||(t[0]=function(t){return e.ajaxData.title=t}),required:"",maxlength:"200",placeholder:"请输入",id:"btnA",class:Object(a["normalizeClass"])(e.bgSave&&!e.ajaxData.title?"mandatoryPink":""),autocomplete:"off"},null,2)),[[a["vModelText"],e.ajaxData.title]]):(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:1},[Object(a["createTextVNode"])(Object(a["toDisplayString"])(e.ajaxData.title),1)],64))])]),Object(a["createElementVNode"])("tr",null,[Bu,Object(a["createElementVNode"])("td",null,[e.ajaxData.uuid?(Object(a["openBlock"])(),Object(a["createBlock"])(c,{key:0,ref:"sealFile",formId:e.ajaxData.uuid,upload:e.canEditForm,remove:e.canEditForm},null,8,["formId","upload","remove"])):Object(a["createCommentVNode"])("",!0)])]),Object(a["createElementVNode"])("tr",null,[$u,Object(a["createElementVNode"])("td",null,[e.ajaxData.uuid?(Object(a["openBlock"])(),Object(a["createBlock"])(s,{key:0,codeTo:"3",accept:"pdf",params:{formId:e.ajaxData.uuid,kind:4},upload:e.canEditForm,remove:e.canEditForm,maxCount:1,button:"上传文件",ref:"yywb"},null,8,["params","upload","remove"])):Object(a["createCommentVNode"])("",!0)])]),Object(a["createElementVNode"])("tr",null,[Lu,Object(a["createElementVNode"])("td",Uu,[e.canEditSignetType?(Object(a["openBlock"])(),Object(a["createElementBlock"])(a["Fragment"],{key:0},[e.ajaxData.signetType?(Object(a["openBlock"])(),Object(a["createBlock"])(l,{key:0,modelValue:e.ajaxData.signetType,"onUpdate:modelValue":t[1]||(t[1]=function(t){return e.ajaxData.signetType=t}),"item-key":"index",class:"parent-link"},{item:Object(a["withCtx"])((function(t){var n=t.element;return[Object(a["createElementVNode"])("div",Fu,[Object(a["createElementVNode"])("img",{style:{width:"100%",height:"100%"},src:e.getSignetItem(n,"imgUrl"),alt:e.getSignetItem(n,"signetName"),title:e.getSignetItem(n,"signetName")},null,8,_u),Object(a["createElementVNode"])("img",{src:X.a,class:"deletes",onClick:function(t){return e.deleteType(n)}},null,8,Ru)])]})),_:1},8,["modelValue"])):Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("button",{class:"button-signet",onClick:t[2]||(t[2]=Object(a["withModifiers"])((function(t){return e.showElCascader=!e.showElCascader}),["stop"]))},"选择印章"),Object(a["withDirectives"])(Object(a["createElementVNode"])("div",Mu,[Object(a["createVNode"])(u,{ref:"cascader",props:e.cascaderConfig,modelValue:e.ajaxData.signetType,"onUpdate:modelValue":t[3]||(t[3]=function(t){return e.ajaxData.signetType=t}),options:e.options},null,8,["props","modelValue","options"])],512),[[a["vShow"],e.showElCascader]])],64)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("div",Pu,[(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.signetItems,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"link-signet",key:n},[Object(a["createElementVNode"])("img",{style:{width:"100%",height:"100%"},src:e.splitImageUrl(t.imgUrl),alt:t.signetName,title:t.signetName},null,8,qu)])})),128))]))])])]),e.showExtraOpninions?(Object(a["openBlock"])(),Object(a["createElementBlock"])("table",zu,[Hu,Object(a["createElementVNode"])("tr",null,[Wu,Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(d,{opinions:e.opinionList,code:"jbbm",onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth},null,8,["opinions","onAgree","onDisagree","onGoback","onAuth"])])]),Object(a["createElementVNode"])("tr",null,[Qu,Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(d,{opinions:e.opinionList,code:"zyfzr",onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth},null,8,["opinions","onAgree","onDisagree","onGoback","onAuth"])])])])):Object(a["createCommentVNode"])("",!0),Object(a["createElementVNode"])("table",Yu,[Gu,Object(a["createElementVNode"])("tr",null,[Ku,Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(d,{opinions:e.opinionList,code:"bmsh",onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth},null,8,["opinions","onAgree","onDisagree","onGoback","onAuth"])])]),Object(a["createElementVNode"])("tr",null,[Xu,Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(d,{opinions:e.opinionList,code:"znbm",onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth},null,8,["opinions","onAgree","onDisagree","onGoback","onAuth"]),(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.delMessage,(function(e){return Object(a["openBlock"])(),Object(a["createElementBlock"])("div",{class:"change-messgae",key:e.uuid},[e.addMessage&&e.delMessage?(Object(a["openBlock"])(),Object(a["createElementBlock"])("p",Ju,Object(a["toDisplayString"])(e.userName)+" "+Object(a["toDisplayString"])(e.addMessage)+" ; "+Object(a["toDisplayString"])(e.delMessage),1)):(Object(a["openBlock"])(),Object(a["createElementBlock"])("p",Zu,Object(a["toDisplayString"])(e.userName)+" "+Object(a["toDisplayString"])(e.addMessage)+Object(a["toDisplayString"])(e.delMessage),1))])})),128))])]),Object(a["createElementVNode"])("tr",null,[ed,Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(d,{opinions:e.opinionList,code:"fgld",onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth},null,8,["opinions","onAgree","onDisagree","onGoback","onAuth"])])]),Object(a["createElementVNode"])("tr",null,[td,Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(d,{opinions:e.opinionList,code:"zyld",onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth},null,8,["opinions","onAgree","onDisagree","onGoback","onAuth"])])]),Object(a["createElementVNode"])("tr",null,[nd,Object(a["createElementVNode"])("td",null,[Object(a["createVNode"])(d,{code:"yyjl",agreeText:"确认用印",ref:"opinionsFile",key:e.randomKey,opinions:e.opinionList,codes:e.itemData.activityCode,ajaxData:e.ajaxData,actId:e.itemData.activityId,flowInsId:e.itemData.flowInstanceId,userInfo:e.jchcUser,itemData:e.itemData,yyjlSignetItem:e.yyjlSignetItems,newSignetItem:e.newSignetItems,taskId:e.itemData.taskId,currentPerson:e.currentPerson,queryDataList:e.queryDataList,onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth,onQueryData:e.queryData},null,8,["opinions","codes","ajaxData","actId","flowInsId","userInfo","itemData","yyjlSignetItem","newSignetItem","taskId","currentPerson","queryDataList","onAgree","onDisagree","onGoback","onAuth","onQueryData"])])]),Object(a["createElementVNode"])("tr",null,[ad,Object(a["createElementVNode"])("td",null,[e.showUploadOrStamp?(Object(a["openBlock"])(),Object(a["createBlock"])(d,{key:0,agreeText:"提交",opinions:e.opinionList,code:"yyr",codes:"yyr",itemData:e.itemData,onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth},null,8,["opinions","itemData","onAgree","onDisagree","onGoback","onAuth"])):Object(a["createCommentVNode"])("",!0)])]),Object(a["createElementVNode"])("tr",null,[id,Object(a["createElementVNode"])("td",null,[e.showUploadOrStamp?(Object(a["openBlock"])(),Object(a["createBlock"])(d,{key:0,agreeText:"归档",opinions:e.opinionList,code:"gd",codes:"gd",onAgree:e.agree,onDisagree:e.disagree,onGoback:e.goback,onAuth:e.auth},null,8,["opinions","onAgree","onDisagree","onGoback","onAuth"])):Object(a["createCommentVNode"])("",!0)])])]),Object(a["createElementVNode"])("div",od,[rd,(Object(a["openBlock"])(!0),Object(a["createElementBlock"])(a["Fragment"],null,Object(a["renderList"])(e.fileList,(function(t,n){return Object(a["openBlock"])(),Object(a["createElementBlock"])("p",{class:"file",key:t.uuid},[Object(a["createElementVNode"])("span",{onClick:function(n){return e.previewFile(t.docViewUuid)}},Object(a["toDisplayString"])(n+1)+"、"+Object(a["toDisplayString"])(t.fileName),9,cd)])})),128))])])],512)}var ld=n("b85c");fa.a.locale("zh-cn");var ud=function(e){Object(s["a"])(n,e);var t=Object(l["a"])(n);function n(){var e;Object(o["a"])(this,n);for(var a=arguments.length,i=new Array(a),r=0;r<a;r++)i[r]=arguments[r];return e=t.call.apply(t,[this].concat(i)),Object(u["a"])(Object(c["a"])(e),"itemData",{uuid:e.getUrlParam("uuid"),status:e.getUrlParam("status"),taskId:e.getUrlParam("taskId"),activityCode:e.getUrlParam("activityCode"),flowInstanceId:e.getUrlParam("flowInstanceId")}),Object(u["a"])(Object(c["a"])(e),"randomKey",1e10*Math.random()),Object(u["a"])(Object(c["a"])(e),"queryDataQuery",[]),Object(u["a"])(Object(c["a"])(e),"queryDataList",[]),Object(u["a"])(Object(c["a"])(e),"queryDataQueryList",[]),Object(u["a"])(Object(c["a"])(e),"checkList",[]),Object(u["a"])(Object(c["a"])(e),"cascaderConfig",{multiple:!0,expandTrigger:"hover",label:"name",value:"value",children:"child"}),Object(u["a"])(Object(c["a"])(e),"options",[]),Object(u["a"])(Object(c["a"])(e),"jchcUser",{}),Object(u["a"])(Object(c["a"])(e),"opinionList",[]),Object(u["a"])(Object(c["a"])(e),"fileList",[]),Object(u["a"])(Object(c["a"])(e),"canEditForm",!0),Object(u["a"])(Object(c["a"])(e),"canEditSignetType",!0),Object(u["a"])(Object(c["a"])(e),"ajaxData",{uuid:"",title:"",signetAmount:0,signetType:[],submitTime:"",groupId:"",groupName:"",activityId:"",deptId:"",taskId:"",toActUsers:[],opinionText:""}),Object(u["a"])(Object(c["a"])(e),"yyr",!1),Object(u["a"])(Object(c["a"])(e),"gd",!1),Object(u["a"])(Object(c["a"])(e),"oldJsonData",[]),Object(u["a"])(Object(c["a"])(e),"linkTypes",["jbbm","zyfz","bmsh","znbm","fgld","zyld","yyjl","yyr","gd"]),Object(u["a"])(Object(c["a"])(e),"delMessage",[]),Object(u["a"])(Object(c["a"])(e),"yyjlSignetItems",[]),Object(u["a"])(Object(c["a"])(e),"newSignetItems",[]),Object(u["a"])(Object(c["a"])(e),"bgSave",!1),Object(u["a"])(Object(c["a"])(e),"showElCascader",!1),Object(u["a"])(Object(c["a"])(e),"showUploadOrStamp",!0),Object(u["a"])(Object(c["a"])(e),"signetIcon",ba.rewrite("")),Object(u["a"])(Object(c["a"])(e),"currentPerson",[]),Object(u["a"])(Object(c["a"])(e),"websocket",null),e}return Object(r["a"])(n,[{key:"created",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.init();case 2:return e.next=4,this.newSignetInti();case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"mounted",value:function(){var e=this;this.linkTypes.includes(this.itemData.activityCode)&&(this.canEditForm=!1),"start"===this.itemData.activityCode||"znbm"===this.itemData.activityCode||"bmsh"==this.itemData.activityCode||"{}"===JSON.stringify(this.itemData)?this.canEditSignetType=!0:this.canEditSignetType=!1,window.addEventListener("click",(function(t){var n=document.querySelector(".cascader-wrap");1!=e.showElCascader||n.contains(t.target)||(e.showElCascader=!1)})),"用印记录"===this.itemData.activityName&&("WebSocket"in window?(this.websocket=new WebSocket(("https:"===location.protocol?"wss://":"ws://")+xr["BASE_URL"]+"/chat?formId="+this.itemData.uuid+"&taskId=authUpload&remote_client=PC"),this.websocket.addEventListener("open",this.handleOpen,!1),this.websocket.addEventListener("close",this.handleClose,!1),this.websocket.addEventListener("error",this.handleError,!1),this.websocket.addEventListener("message",this.handleMessage,!1)):console.log("您的浏览器不支持使用 WebSoket!"))}},{key:"getUrlParam",value:function(e){var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)"),n=window.location.search.substr(1).match(t),a="";return null!=n&&(a=unescape(n[2])),a}},{key:"handleOpen",value:function(e){console.log("handleOpen",e,"e")}},{key:"handleClose",value:function(){console.log("handleClose")}},{key:"handleError",value:function(){console.log("handleError")}},{key:"handleMessage",value:function(e){this.randomKey=1e10*Math.random()}},{key:"signetItems",get:function(){var e=this,t=this.ajaxData.signetType.map((function(t){return{formId:e.ajaxData.uuid,isSelfOrg:t[0].split("&")[0],parentId:t[1].split("&")[1],parentName:t[1].split("&")[2],signetId:t[2].split("&")[1],signetName:t[2].split("&")[2],kind:t[2].split("&")[0],imgUrl:t[2].split("&")[3]}}));return t}},{key:"signetIds",get:function(){return this.signetItems.map((function(e){return e.parentId})).join(",")}},{key:"signetNames",get:function(){return this.signetItems.map((function(e){return e.signetName})).join(",")}},{key:"submitData",get:function(){var e=JSON.stringify(this.ajaxData.signetType),t={uuid:this.ajaxData.uuid,title:this.ajaxData.title,signetAmount:this.ajaxData.signetAmount,toActUsers:[],signetId:this.signetIds,signetName:this.signetNames,signetKind:this.signetItems.every((function(e){return 0==e.kind}))?0:1,signetItems:this.signetItems,itemsJsonData:e==JSON.stringify(this.oldJsonData)?null:e,taskId:this.itemData.taskId||null,flowInstanceId:this.itemData.flowInstanceId||null,activityCode:this.itemData.activityCode||null,newSignetItems:this.yyjlSignetItems.filter((function(e){return e.sealUserName&&e.signetNum&&"1"==e.status}))||null};return t.opinionText=this.ajaxData.opinionText,t}},{key:"showExtraOpninions",get:function(){var e,t;return 1==(null===(e=this.signetItems)||void 0===e||null===(t=e[0])||void 0===t?void 0:t.isSelfOrg)}},{key:"archivesSort",get:function(){return ba.fillZero(this.ajaxData.archivesSort,3)}},{key:"getSignetItem",get:function(){return function(e,t){switch(t){case"imgUrl":return ba.rewrite(e[2].split("&")[3]||"");case"signetName":return e[2].split("&")[2];case"signetId":return e[2].split("&")[1];default:return""}}}},{key:"splitImageUrl",get:function(){return function(e){return ba.rewrite(e||"")}}},{key:"getCode",value:function(){return ba.rewrite("/signet/qrcodeForDetails?uuid="+this.itemData.uuid+"&flowInstanceId="+this.itemData.flowInstanceId+"&status="+this.itemData.status+"&taskId="+this.itemData.taskId+"&activityCode="+this.itemData.activityCode+"&orgId="+this.itemData.orgId)}},{key:"previewFile",value:function(e){ba.preview(e)}},{key:"deleteType",value:function(e){this.ajaxData.signetType=this.ajaxData.signetType.filter((function(t){return t[2].split("&")[1]!==e[2].split("&")[1]}))}},{key:"agree",value:function(e){this.ajaxData.opinionText=e.content,this.submit(e)}},{key:"auth",value:function(e){var t=this;if(!e.content||!e.content.trim())return this.$toast("请填写意见"),!1;this.$logo(!0),this.$axios.get("/QSecurity/getSelfDeptUsers").then((function(n){if(0!==n.code)return t.$logo(!1),t.$toast(n.msg),!1;t.$logo(!1,!0),t.$dialog({title:"交办",max:!1,content:{component:Ys,props:{list:n.data},handle:!0},height:300,width:200,top:"50%",left:"90%",ok:function(n){t.$logo(!0),t.$axios.post("/signet/auth",{authList:n[0],taskId:t.itemData.taskId,opinionText:e.content}).then((function(e){if(0!==e.code)return t.$logo(!1),t.$toast(e.msg),!1;location.reload()})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}))}})}))}},{key:"disagree",value:function(e){var t=this;if(!e.content||!e.content.trim())return this.$toast("请填写意见"),!1;this.$alert({content:"您确定要不同意吗",lock:!0,ok:function(){t.$logo(!0),t.$axios.post("/signet/disagree",{taskId:t.itemData.taskId,flowInstanceId:t.itemData.flowInstanceId,opinionText:e.content,uuid:t.ajaxData.uuid}).then((function(e){if(0!==e.code)return t.$logo(!1),t.$toast(e.msg),!1;location.reload()})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}))}})}},{key:"goback",value:function(e){var t=this;if(!e.content||!e.content.trim())return this.$toast("请填写意见"),!1;this.$alert({content:"您确定退回吗",lock:!0,ok:function(){t.$logo(!0);var n=t.$refs.opinionsFile.yyjlSignetItem.map((function(e){return e.uuid}));t.$axios.post("/signet/goback",{taskId:t.itemData.taskId,opinionText:e.content,uuid:t.itemData.uuid,selfSignetItemIds:n,activityCode:t.itemData.activityCode}).then((function(e){if(0!==e.code)return t.$logo(!1),t.$toast(e.msg),!1;location.reload()})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}))}})}},{key:"save",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n,a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.bgSave=!0,this.ajaxData.title.trim()){e.next=6;break}return this.$toast("请填写“用印事由”"),e.abrupt("return");case 6:if(0!=this.$refs.yywb.fileList.length){e.next=11;break}return this.$toast("请上传“用印文本”"),e.abrupt("return");case 11:if(this.ajaxData.signetType.length){e.next=16;break}return this.$toast("请选择“印章类别”"),e.abrupt("return");case 16:this.bgSave=!0;case 17:t=JSON.stringify(this.ajaxData.signetType),n={uuid:this.ajaxData.uuid,title:this.ajaxData.title,signetAmount:this.ajaxData.signetAmount,signetId:this.signetIds,signetName:this.signetNames,signetKind:this.signetItems.every((function(e){return 0==e.kind}))?0:1,signetItems:this.signetItems,itemsJsonData:t==JSON.stringify(this.oldJsonData)?null:t,taskId:this.itemData.taskId||null,flowInstanceId:this.itemData.flowInstanceId||null},this.$logo(!0),this.$axios.post("/signet/add",n).then((function(e){if(0!==e.code)return a.$logo(!1),a.$toast(e.msg),!1;location.reload()})).catch((function(e){a.$logo(!1),a.$toast(e.toString())}));case 21:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"verifyForm",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n,a,i,o,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=!0,n=this.ajaxData,n.title.trim()){e.next=8;break}return this.$toast("请填写“用印事由”"),t=!1,e.abrupt("return",t);case 8:if(0!=this.$refs.yywb.fileList.length){e.next=14;break}return this.$toast("请上传“用印文本”"),t=!1,e.abrupt("return",t);case 14:if(n.signetType.length){e.next=18;break}return this.$toast("请选择“印章类别”"),t=!1,e.abrupt("return",t);case 18:if("yyjl"!==this.itemData.activityCode){e.next=61;break}if(this.queryDataQuery.length){e.next=22;break}return this.$toast("请确认用印部门承办人!"),e.abrupt("return",!1);case 22:return e.next=24,this.newSignetIntiItem();case 24:if(a=e.sent,!a.length){e.next=61;break}i=0,this.yyjlSignetItems.forEach((function(e,t){e.sealFiles=a[t].sealFiles,e.sealType=a[t].sealType})),o=0;case 29:if(!(o<this.yyjlSignetItems.length)){e.next=58;break}if(r=this.yyjlSignetItems[o],!r.required){e.next=50;break}if(0!==r.sealType){e.next=42;break}if(r.sealFiles.length||r.signetNum){e.next=36;break}return this.$toast("加星的待办业务需要您处理"),e.abrupt("return",!1);case 36:if(!r.signetNum||r.sealFiles.length){e.next=39;break}return this.$toast("请上传".concat(r.signetName,"附件")),e.abrupt("return",!1);case 39:i+=1,e.next=48;break;case 42:if(r.signetNum){e.next=47;break}return this.$toast("加星的待办业务需要您处理"),e.abrupt("return",!1);case 47:i+=1;case 48:e.next=55;break;case 50:if(0!==r.sealType){e.next=54;break}if(!r.signetNum||r.sealFiles.length){e.next=54;break}return this.$toast("请上传".concat(r.signetName,"附件")),e.abrupt("return",!1);case 54:r.signetNum&&(i+=1);case 55:o++,e.next=29;break;case 58:if(!(i<1)){e.next=61;break}return this.$toast("请完善用印记录"),e.abrupt("return",!1);case 61:return e.abrupt("return",t);case 62:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"submit",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(t){var n,a,i,o,r=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.ajaxData.opinionText=null===t||void 0===t?void 0:t.content,e.next=3,this.verifyForm();case 3:if(o=e.sent,o){e.next=6;break}return e.abrupt("return",!1);case 6:this.$axios.get("/signet/checkActUsers?signetKind=1&groupId=".concat(null!==(n=this.itemData.orgId)&&void 0!==n?n:"","&biaoshi=").concat(null!==(a=null===(i=this.signetItems[0])||void 0===i?void 0:i.isSelfOrg)&&void 0!==a?a:"")).then((function(e){if(0!==e.code&&(r.$logo(!1),r.$toast(e.msg)),e.data.length){var t,n="",a=Object(ld["a"])(e.data);try{for(a.s();!(t=a.n()).done;){var i=t.value;n+=i}}catch(o){a.e(o)}finally{a.f()}r.$toast(n)}else r.successSubmit()}));case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"successSubmit",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(t){var n,a,i,o,r,c,s,l=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t&&(this.ajaxData.opinionText=t.content),n=["bmsh","znbm","start","jbbm"],a=["zyfz","fgld"],"yyjl"!==this.itemData.activityCode){e.next=16;break}if(i=this.yyjlSignetItems.some((function(e){return!e.signetNum&&!e.sealFiles.length})),!i){e.next=9;break}this.$alert({content:"您还有待办业务未处理，确认要提交吗?",lock:!0,ok:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.t0=l,e.next=3,l.submitInti();case 3:return e.t1=e.sent,e.t0.submitPassed.call(e.t0,e.t1),e.abrupt("return",!0);case 6:case"end":return e.stop()}}),e)})));function t(){return e.apply(this,arguments)}return t}()}),e.next=14;break;case 9:return e.t0=this,e.next=12,this.submitInti();case 12:e.t1=e.sent,e.t0.submitPassed.call(e.t0,e.t1);case 14:e.next=32;break;case 16:if(!n.includes(this.itemData.activityCode)&&this.itemData.activityCode){e.next=23;break}this.submitData.deptId=this.jchcUser.deptId,this.submitData.signetType=JSON.stringify(this.ajaxData.signetType),o=this.signetItems.every((function(e){return"0"===e.kind})),o&&"bmsh"===this.itemData.activityCode||o&&"znbm"===this.itemData.activityCode?this.$axios.get("/defConfigUser/getDefUsersBySignetIds?signetIds="+this.signetIds).then((function(e){if(0!==e.code)return l.$logo(!1),l.$toast(e.msg),!1;var t=e.data,n=l.submitData,a=[];t.forEach((function(e){a.push({deptId:e.deptId,groupId:e.groupId,id:e.userId,name:e.userName})})),n.toActUsers.push({activityCode:"yyjl",activityId:"fc5326c010444ba299109826af419406",type:"SUBMIT",users:a}),l.submitPassed(n)})):(r=320,"start"!==this.itemData.activityCode&&this.itemData.activityCode||"0"==(null===(c=this.signetItems[0])||void 0===c?void 0:c.isSelfOrg)&&(r=600),"jbbm"===this.itemData.activityCode&&null!==(s=this.signetItems[0])&&void 0!==s&&s.isSelfOrg&&(r=750),"bmsh"===this.itemData.activityCode&&(r=458),this.$dialog({title:"提交",max:!1,content:{component:As,props:{submitData:this.submitData,signetItems:this.signetItems,signetIds:this.signetIds,ajaxData:this.ajaxData},handle:!0},height:300,width:r,top:"30%",left:"90%",ok:function(e){var t=l.submitData;t.toActUsers=e,l.submitPassed(t)}})),e.next=32;break;case 23:if(!a.includes(this.itemData.activityCode)){e.next=31;break}return e.t2=this,e.next=27,this.submitInti();case 27:e.t3=e.sent,e.t2.submitPassed.call(e.t2,e.t3),e.next=32;break;case 31:this.submitPassed(this.submitData);case 32:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"queryData",value:function(e,t){this.queryDataQueryList=t,this.queryDataQuery=e}},{key:"submitPassed",value:function(e){var t=this;if("yyjl"===e.activityCode){if(!this.queryDataQuery.length)return this.$toast("请确认用印部门承办人!"),!1;for(var n=this.yyjlSignetItems.filter((function(e){return e.signetNum&&1==e.status})),a=0;a<n.length;a++){var i,o;e.newSignetItems.push({formId:n[a].formId,imgUrl:null===(i=n[a].files[0])||void 0===i?void 0:i.imgUrl,kind:"".concat(null===(o=n[a].files[0])||void 0===o?void 0:o.kind),parentId:n[a].parentId,parentName:n[a].parentName,signetId:n[a].signetId,signetName:n[a].signetName,uuid:n[a].uuid,sealDeptId:this.queryDataQuery[0].deptId,sealDeptName:this.queryDataQuery[0].deptName,sealUserId:this.queryDataQuery[0].userId,sealUserName:this.queryDataQuery[0].userName,sealType:"拍照"===this.queryDataQueryList[0].sealName?1:0,sealOrgId:this.queryDataQuery[0].orgId,sealOrgName:this.queryDataQuery[0].orgName,sealCheckTime:this.queryDataQuery[0].createTime,signetNum:n[a].signetNum,sealsNum:n[a].sealsNum})}e.toActUsers=[]}this.$alert({content:"您确定要提交吗",lock:!0,ok:function(){var n=Object(re["a"])(regeneratorRuntime.mark((function n(){return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.$logo(!0),n.next=3,t.$axios.post("/signet/submit",e).then((function(e){if(0!==e.code)return t.$logo(!1),t.$toast(e.msg),!1;location.reload()})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}));case 3:return n.abrupt("return",!0);case 4:case"end":return n.stop()}}),n)})));function a(){return n.apply(this,arguments)}return a}()})}},{key:"submitInti",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n,a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.submitData,n=this.itemData.taskId,e.next=4,this.$axios.get("/activityModel/def/list-task-next/".concat(n)).then((function(e){if(0!==e.code)return a.$logo(!1),a.$toast(e.msg),!1;var n=e.data;t.toActUsers=[],n.forEach((function(e){if("yyjl"===a.itemData.activityCode)t.toActUsers.push({activityId:e.uuid,activityCode:"yyr",type:e.type,users:[{deptId:a.ajaxData.deptId,groupId:a.ajaxData.orgId,id:a.ajaxData.userId,name:a.ajaxData.userName}]});else{var n=e.userList.filter((function(e){return e.checked}));n.length&&t.toActUsers.push({activityId:e.uuid,type:e.type,users:n.map((function(e){return{deptId:e.deptId,groupId:e.groupId,id:e.id,name:e.name}}))})}}))}));case 4:return e.abrupt("return",t);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getOpinion",value:function(){var e=this;this.$axios.get("/opinion/listOpinionsByFlowInsId?flowInsId="+this.itemData.flowInstanceId+"&taskInsId="+this.itemData.taskId).then((function(t){if(0!==t.code)return e.$toast(t.msg),!1;e.opinionList=t.data,console.log(t.data);var n=t.data.filter((function(e){return"yyjl"===e.code}));if(n.length){var a=n[0].opinions.filter((function(e){return e.todo}));e.currentPerson=a}})).catch((function(t){e.$logo(!1),e.$toast(t.toString())})),this.$axios.get("/signetItemHistory/getChangeHistory?formId="+this.itemData.uuid).then((function(t){if(0!==t.code)return e.$toast(t.msg),!1;e.delMessage=t.data})).catch((function(t){e.$toast(t.toString())}))}},{key:"getFiles",value:function(e){var t=this;this.$axios.get("/jchcfile/getFileByKind?formId="+e).then((function(e){if(0!==e.code)return t.$toast(e.msg),!1;t.fileList=e.data})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}))}},{key:"newSignetIntiItem",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t,n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=[],e.next=3,this.$axios.get("/signet/yyjlSignetItems?taskId=".concat(this.itemData.taskId,"&uuid=").concat(this.itemData.uuid,"&flowInsId=").concat(this.itemData.flowInstanceId)).then((function(e){t=e.data})).catch((function(e){n.$logo(!1),n.$toast(e.toString())}));case 3:return e.abrupt("return",t);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"newSignetInti",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/signet/yyjlSignetItems?taskId=".concat(this.itemData.taskId,"&uuid=").concat(this.itemData.uuid,"&flowInsId=").concat(this.itemData.flowInstanceId)).then((function(e){t.yyjlSignetItems=e.data})).catch((function(e){t.$logo(!1),t.$toast(e.toString())}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"init",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/signet/initDefConfigData").then((function(e){if(0!==e.code)return t.$toast(e.msg),!1;t.jchcUser=e.data.user,t.getFiles(t.jchcUser.groupId),t.itemData.taskId||(t.ajaxData.groupId=t.jchcUser.groupId,t.ajaxData.userName=t.jchcUser.name,t.ajaxData.deptName=t.jchcUser.deptName,t.ajaxData.groupName=t.jchcUser.groupName,t.ajaxData.deptId=t.jchcUser.deptId,t.ajaxData.uuid=e.data.uuid);var n=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t.map((function(t){var a,i,o,r,c=null!==(a=t.files)&&void 0!==a&&a.length?"&"+(null===(i=t.files)||void 0===i||null===(o=i[0])||void 0===o?void 0:o.imgUrl):"",s=0===n?(t.isSelfOrg?0:1)+"&"+t.name:t.kind+"&"+t.uuid+"&"+t.name+c;return null!==(r=t.child)&&void 0!==r&&r.length?t.child=e(t.child,n+1):1===n&&(t.disabled=!0),Object(Pt["a"])(Object(Pt["a"])({},t),{},{value:s})}))};t.options=n(e.data.signetList||[]),t.getData()})).catch((function(e){t.$toast(e.toString())}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getData",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.ajaxData.submitTime=ba.datetime(),!this.itemData.taskId){e.next=4;break}return e.next=4,this.getFormData();case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getFormData",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/signet/get?id="+this.itemData.uuid).then(function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(n){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0===n.code){e.next=3;break}return t.$toast(n.msg),e.abrupt("return",!1);case 3:return a=n.data.itemsJsonData&&JSON.parse(n.data.itemsJsonData),t.ajaxData=n.data,t.ajaxData.signetType=a,t.oldJsonData=a,t.getOpinion(),t.newSignetItems=n.data.signetItems,e.next=11,t.searchUsers();case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(e){t.$logo(!1),t.$toast(e.toString())}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"searchUsers",value:function(){var e=Object(re["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.$axios.get("/sealUserSocket/listByFormIdAndTaskId?formId="+this.ajaxData.uuid+"&taskId="+this.itemData.taskId).then((function(e){t.queryDataList=e.data}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}]),n}(d["b"]);ud=Object(E["a"])([Object(d["a"])({name:"handle",components:{draggable:Ql.a,Files:Na,LinkFilePack:Oo,FlowCharts:Rs,Opinions:Qr,isSecrecy:al},watch:{signetItems:{handler:function(e){this.options=this.options.map((function(t){var n,a=t.isSelfOrg!==(0==(null===(n=e[0])||void 0===n?void 0:n.isSelfOrg));return Object(Pt["a"])(Object(Pt["a"])({},t),{},{disabled:!!e.length&&a})}))},deep:!0},ajaxData:{handler:function(e){this.yyjlSignetItems=this.yyjlSignetItems.map((function(t){var n;return Object(Pt["a"])(Object(Pt["a"])({},t),{},{signetNum:null!==(n=t.signetNum)&&void 0!==n?n:e.signetAmount})}))}}}})],ud);var dd=ud;n("591e");const pd=g()(dd,[["render",sd],["__scopeId","data-v-4c14b65e"]]);var hd=pd,fd=[{path:"/",name:"Index",component:bu,children:[{path:"/handle",name:"Handle",component:Jl},{path:"/handled",name:"Handled",component:Jr}]},{path:"/pending",name:"PendingItems",component:hd},{path:"/print",name:"Print",component:Jr}],bd=Object(y["a"])({history:Object(y["b"])("/seal/"),routes:fd}),md=bd,gd=(n("7437"),n("317f")),vd=n("5f05"),jd=n("db9d"),yd=n("cf2e"),Od=n("8430"),kd=n("3ef4"),wd=function(e){e.component(gd["a"].name,gd["a"]),e.component(vd["a"].name,vd["a"]),e.component(jd["a"].name,jd["a"]),e.component(yd["a"].name,yd["a"]),e.component(Pr["a"].name,Pr["a"]),e.component(Pr["b"].name,Pr["b"]),e.component(Od["a"].name,Od["a"]),e.component(Od["b"].name,Od["b"]),e.component(st["a"].name,st["a"]),e.component(kd["a"].name,kd["a"])},xd=wd,Nd=(n("202f"),n("a2f0"),n("7e79")),Ed=n.n(Nd),Id=Object(a["createApp"])(j);xd(Id),Id.use(f),Id.use(Ed.a).use(md).use(Gt["a"]).use(Kt).use(na).mount("#app")},cd64:function(e,t,n){},d19a:function(e,t,n){"use strict";n("a50a")},d5fc:function(e,t,n){},e462:function(module,exports,__webpack_require__){__webpack_require__("b0c0"),__webpack_require__("4d63"),__webpack_require__("c607"),__webpack_require__("ac1f"),__webpack_require__("2c3e"),__webpack_require__("25f0"),jQuery.extend({createUploadIframe:function(e,t){var n="jUploadFrame"+e;if(window.ActiveXObject){var a=document.createElement('<iframe id="'+n+'" name="'+n+'" />');"boolean"==typeof t?a.src="javascript:false":"string"==typeof t&&(a.src=t)}else{a=document.createElement("iframe");a.id=n,a.name=n}return a.style.position="absolute",a.style.top="-1000px",a.style.left="-1000px",document.body.appendChild(a),a},createUploadForm:function(e,t){var n="jUploadForm"+e,a="jUploadFile"+e,i=$('<form  action="" method="POST" name="'+n+'" id="'+n+'" enctype="multipart/form-data"></form>'),o=$("#"+t),r=$(o).clone();return $(o).attr("id",a),$(o).before(r),$(o).appendTo(i),$(i).css("position","absolute"),$(i).css("top","-1200px"),$(i).css("left","-1200px"),$(i).appendTo("body"),i},addOtherRequestsToForm:function(e,t){var n=$('<input type="hidden" name="" value="">');for(var a in t){name=a,value=t[a];var i=n.clone();i.attr({name:name,value:value}),$(i).appendTo(e)}return e},ajaxFileUpload:function(e){e=jQuery.extend({},jQuery.ajaxSettings,e);var t=(new Date).getTime(),n=jQuery.createUploadForm(t,e.fileElementId);e.data&&(n=jQuery.addOtherRequestsToForm(n,e.data));jQuery.createUploadIframe(t,e.secureuri);var a="jUploadFrame"+t,i="jUploadForm"+t;e.global&&!jQuery.active++&&jQuery.event.trigger("ajaxStart");var o=!1,r={};e.global&&jQuery.event.trigger("ajaxSend",[r,e]);var c=function(t){var i=document.getElementById(a);try{i.contentWindow?(r.responseText=i.contentWindow.document.body?i.contentWindow.document.body.innerHTML:null,r.responseXML=i.contentWindow.document.XMLDocument?i.contentWindow.document.XMLDocument:i.contentWindow.document):i.contentDocument&&(r.responseText=i.contentDocument.document.body?i.contentDocument.document.body.innerHTML:null,r.responseXML=i.contentDocument.document.XMLDocument?i.contentDocument.document.XMLDocument:i.contentDocument.document)}catch(l){jQuery.handleError(e,r,null,l)}if(r||"timeout"==t){var c;o=!0;try{if(c="timeout"!=t?"success":"error","error"!=c){var s=jQuery.uploadHttpData(r,e.dataType);e.success&&e.success(s,c),e.global&&jQuery.event.trigger("ajaxSuccess",[r,e])}else jQuery.handleError(e,r,c)}catch(l){c="error",jQuery.handleError(e,r,c,l)}e.global&&jQuery.event.trigger("ajaxComplete",[r,e]),e.global&&!--jQuery.active&&jQuery.event.trigger("ajaxStop"),e.complete&&e.complete(r,c),jQuery(i).unbind(),setTimeout((function(){try{$(i).remove(),$(n).remove()}catch(l){jQuery.handleError(e,r,null,l)}}),100),r=null}};e.timeout>0&&setTimeout((function(){o||c("timeout")}),e.timeout);try{n=$("#"+i);$(n).attr("action",e.url),$(n).attr("method","POST"),$(n).attr("target",a),n.encoding?n.encoding="multipart/form-data":n.enctype="multipart/form-data",$(n).submit()}catch(s){jQuery.handleError(e,r,null,s)}return window.attachEvent?document.getElementById(a).attachEvent("onload",c):document.getElementById(a).addEventListener("load",c,!1),{abort:function(){}}},uploadHttpData:function uploadHttpData(r,type){var data=!type;if(data="xml"==type||data?r.responseXML:r.responseText,"script"==type&&jQuery.globalEval(data),"json"==type){var data=r.responseText,rx=new RegExp("<pre.*?>(.*?)</pre>","i"),am=rx.exec(data),data=am?am[1]:"";eval("data = "+data)}return"html"==type&&jQuery("<div>").html(data).evalScripts(),data}})},e6f6:function(e,t,n){},e866:function(e,t,n){"use strict";n("b860")},ecf5:function(e,t,n){"use strict";n("4151")},f121:function(e,t){var n=!0,a=!0,i="/seal",o="************:8088",r="http://************:8888",c="**************:8086",s="";s=a?n?o:r:c;var l=s+i;console.log(Object({NODE_ENV:"production",VUE_APP_BUILD_TYPE:"uat",BASE_URL:"/seal/"}),l);var u={BASE_URL:l,IS_PROD:a,BASE_UAT_URL:o,publicPath:i};e.exports=Object.assign({},u)},f12d:function(e,t,n){(function(e){var a,i,o=n("7037").default;n("fb6a"),n("99af"),n("d3b7"),n("d81d"),n("4e82"),n("a434"),n("ac1f"),n("5319"),n("25f0"),n("498a"),n("a4d3"),n("e01a"),n("d28b"),n("3ca3"),n("ddb0"),n("1276"),n("4d63"),n("c607"),n("2c3e"),n("00b4"),n("a15b"),n("7db0"),n("4de4"),n("466d"),n("b0c0"),function(t,n){"object"==o(e)&&"object"==o(e.exports)?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,(function(n,r){var c=[],s=n.document,l=c.slice,u=c.concat,d=c.push,p=c.indexOf,h={},f=h.toString,b=h.hasOwnProperty,m={},g="1.12.2",v=function e(t,n){return new e.fn.init(t,n)},j=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,y=/^-ms-/,O=/-([\da-z])/gi,k=function(e,t){return t.toUpperCase()};function w(e){var t=!!e&&"length"in e&&e.length,n=v.type(e);return"function"!==n&&!v.isWindow(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}v.fn=v.prototype={jquery:g,constructor:v,selector:"",length:0,toArray:function(){return l.call(this)},get:function(e){return null!=e?0>e?this[e+this.length]:this[e]:l.call(this)},pushStack:function(e){var t=v.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e){return v.each(this,e)},map:function(e){return this.pushStack(v.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(l.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:d,sort:c.sort,splice:c.splice},v.extend=v.fn.extend=function(){var e,t,n,a,i,r,c=arguments[0]||{},s=1,l=arguments.length,u=!1;for("boolean"==typeof c&&(u=c,c=arguments[s]||{},s++),"object"==o(c)||v.isFunction(c)||(c={}),s===l&&(c=this,s--);l>s;s++)if(null!=(i=arguments[s]))for(a in i)e=c[a],n=i[a],c!==n&&(u&&n&&(v.isPlainObject(n)||(t=v.isArray(n)))?(t?(t=!1,r=e&&v.isArray(e)?e:[]):r=e&&v.isPlainObject(e)?e:{},c[a]=v.extend(u,r,n)):void 0!==n&&(c[a]=n));return c},v.extend({expando:"jQuery"+(g+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===v.type(e)},isArray:Array.isArray||function(e){return"array"===v.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){var t=e&&e.toString();return!v.isArray(e)&&t-parseFloat(t)+1>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==v.type(e)||e.nodeType||v.isWindow(e))return!1;try{if(e.constructor&&!b.call(e,"constructor")&&!b.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}if(!m.ownFirst)for(t in e)return b.call(e,t);for(t in e);return void 0===t||b.call(e,t)},type:function(e){return null==e?e+"":"object"==o(e)||"function"==typeof e?h[f.call(e)]||"object":o(e)},globalEval:function(e){e&&v.trim(e)&&(n.execScript||function(e){n.eval.call(n,e)})(e)},camelCase:function(e){return e.replace(y,"ms-").replace(O,k)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var n,a=0;if(w(e)){for(n=e.length;n>a;a++)if(!1===t.call(e[a],a,e[a]))break}else for(a in e)if(!1===t.call(e[a],a,e[a]))break;return e},trim:function(e){return null==e?"":(e+"").replace(j,"")},makeArray:function(e,t){var n=t||[];return null!=e&&(w(Object(e))?v.merge(n,"string"==typeof e?[e]:e):d.call(n,e)),n},inArray:function(e,t,n){var a;if(t){if(p)return p.call(t,e,n);for(a=t.length,n=n?0>n?Math.max(0,a+n):n:0;a>n;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){var n=+t.length,a=0,i=e.length;while(n>a)e[i++]=t[a++];if(n!==n)while(void 0!==t[a])e[i++]=t[a++];return e.length=i,e},grep:function(e,t,n){for(var a,i=[],o=0,r=e.length,c=!n;r>o;o++)a=!t(e[o],o),a!==c&&i.push(e[o]);return i},map:function(e,t,n){var a,i,o=0,r=[];if(w(e))for(a=e.length;a>o;o++)i=t(e[o],o,n),null!=i&&r.push(i);else for(o in e)i=t(e[o],o,n),null!=i&&r.push(i);return u.apply([],r)},guid:1,proxy:function(e,t){var n,a,i;return"string"==typeof t&&(i=e[t],t=e,e=i),v.isFunction(e)?(n=l.call(arguments,2),a=function(){return e.apply(t||this,n.concat(l.call(arguments)))},a.guid=e.guid=e.guid||v.guid++,a):void 0},now:function(){return+new Date},support:m}),"function"==typeof Symbol&&(v.fn[Symbol.iterator]=c[Symbol.iterator]),v.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){h["[object "+t+"]"]=t.toLowerCase()}));var x=function(e){var t,n,a,i,o,r,c,s,l,u,d,p,h,f,b,m,g,v,j,y="sizzle"+1*new Date,O=e.document,k=0,w=0,x=oe(),N=oe(),E=oe(),I=function(e,t){return e===t&&(d=!0),0},C=1<<31,D={}.hasOwnProperty,S=[],T=S.pop,A=S.push,V=S.push,B=S.slice,$=function(e,t){for(var n=0,a=e.length;a>n;n++)if(e[n]===t)return n;return-1},L="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",U="[\\x20\\t\\r\\n\\f]",F="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",_="\\["+U+"*("+F+")(?:"+U+"*([*^$|!~]?=)"+U+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+F+"))|)"+U+"*\\]",R=":("+F+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+_+")*)|.*)\\)|)",M=new RegExp(U+"+","g"),P=new RegExp("^"+U+"+|((?:^|[^\\\\])(?:\\\\.)*)"+U+"+$","g"),q=new RegExp("^"+U+"*,"+U+"*"),z=new RegExp("^"+U+"*([>+~]|"+U+")"+U+"*"),H=new RegExp("="+U+"*([^\\]'\"]*?)"+U+"*\\]","g"),W=new RegExp(R),Q=new RegExp("^"+F+"$"),Y={ID:new RegExp("^#("+F+")"),CLASS:new RegExp("^\\.("+F+")"),TAG:new RegExp("^("+F+"|[*])"),ATTR:new RegExp("^"+_),PSEUDO:new RegExp("^"+R),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+U+"*(even|odd|(([+-]|)(\\d*)n|)"+U+"*(?:([+-]|)"+U+"*(\\d+)|))"+U+"*\\)|)","i"),bool:new RegExp("^(?:"+L+")$","i"),needsContext:new RegExp("^"+U+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+U+"*((?:-\\d)?\\d*)"+U+"*\\)|)(?=[^-]|$)","i")},G=/^(?:input|select|textarea|button)$/i,K=/^h\d$/i,X=/^[^{]+\{\s*\[native \w/,J=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Z=/[+~]/,ee=/'|\\/g,te=new RegExp("\\\\([\\da-f]{1,6}"+U+"?|("+U+")|.)","ig"),ne=function(e,t,n){var a="0x"+t-65536;return a!==a||n?t:0>a?String.fromCharCode(a+65536):String.fromCharCode(a>>10|55296,1023&a|56320)},ae=function(){p()};try{V.apply(S=B.call(O.childNodes),O.childNodes),S[O.childNodes.length].nodeType}catch(le){V={apply:S.length?function(e,t){A.apply(e,B.call(t))}:function(e,t){var n=e.length,a=0;while(e[n++]=t[a++]);e.length=n-1}}}function ie(e,t,a,i){var o,c,l,u,d,f,g,v,k=t&&t.ownerDocument,w=t?t.nodeType:9;if(a=a||[],"string"!=typeof e||!e||1!==w&&9!==w&&11!==w)return a;if(!i&&((t?t.ownerDocument||t:O)!==h&&p(t),t=t||h,b)){if(11!==w&&(f=J.exec(e)))if(o=f[1]){if(9===w){if(!(l=t.getElementById(o)))return a;if(l.id===o)return a.push(l),a}else if(k&&(l=k.getElementById(o))&&j(t,l)&&l.id===o)return a.push(l),a}else{if(f[2])return V.apply(a,t.getElementsByTagName(e)),a;if((o=f[3])&&n.getElementsByClassName&&t.getElementsByClassName)return V.apply(a,t.getElementsByClassName(o)),a}if(n.qsa&&!E[e+" "]&&(!m||!m.test(e))){if(1!==w)k=t,v=e;else if("object"!==t.nodeName.toLowerCase()){(u=t.getAttribute("id"))?u=u.replace(ee,"\\$&"):t.setAttribute("id",u=y),g=r(e),c=g.length,d=Q.test(u)?"#"+u:"[id='"+u+"']";while(c--)g[c]=d+" "+me(g[c]);v=g.join(","),k=Z.test(e)&&fe(t.parentNode)||t}if(v)try{return V.apply(a,k.querySelectorAll(v)),a}catch(x){}finally{u===y&&t.removeAttribute("id")}}}return s(e.replace(P,"$1"),t,a,i)}function oe(){var e=[];function t(n,i){return e.push(n+" ")>a.cacheLength&&delete t[e.shift()],t[n+" "]=i}return t}function re(e){return e[y]=!0,e}function ce(e){var t=h.createElement("div");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function se(e,t){var n=e.split("|"),i=n.length;while(i--)a.attrHandle[n[i]]=t}function ue(e,t){var n=t&&e,a=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||C)-(~e.sourceIndex||C);if(a)return a;if(n)while(n=n.nextSibling)if(n===t)return-1;return e?1:-1}function de(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function pe(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function he(e){return re((function(t){return t=+t,re((function(n,a){var i,o=e([],n.length,t),r=o.length;while(r--)n[i=o[r]]&&(n[i]=!(a[i]=n[i]))}))}))}function fe(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}for(t in n=ie.support={},o=ie.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},p=ie.setDocument=function(e){var t,i,r=e?e.ownerDocument||e:O;return r!==h&&9===r.nodeType&&r.documentElement?(h=r,f=h.documentElement,b=!o(h),(i=h.defaultView)&&i.top!==i&&(i.addEventListener?i.addEventListener("unload",ae,!1):i.attachEvent&&i.attachEvent("onunload",ae)),n.attributes=ce((function(e){return e.className="i",!e.getAttribute("className")})),n.getElementsByTagName=ce((function(e){return e.appendChild(h.createComment("")),!e.getElementsByTagName("*").length})),n.getElementsByClassName=X.test(h.getElementsByClassName),n.getById=ce((function(e){return f.appendChild(e).id=y,!h.getElementsByName||!h.getElementsByName(y).length})),n.getById?(a.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&b){var n=t.getElementById(e);return n?[n]:[]}},a.filter.ID=function(e){var t=e.replace(te,ne);return function(e){return e.getAttribute("id")===t}}):(delete a.find.ID,a.filter.ID=function(e){var t=e.replace(te,ne);return function(e){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}}),a.find.TAG=n.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):n.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,a=[],i=0,o=t.getElementsByTagName(e);if("*"===e){while(n=o[i++])1===n.nodeType&&a.push(n);return a}return o},a.find.CLASS=n.getElementsByClassName&&function(e,t){return"undefined"!=typeof t.getElementsByClassName&&b?t.getElementsByClassName(e):void 0},g=[],m=[],(n.qsa=X.test(h.querySelectorAll))&&(ce((function(e){f.appendChild(e).innerHTML="<a id='"+y+"'></a><select id='"+y+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+U+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+U+"*(?:value|"+L+")"),e.querySelectorAll("[id~="+y+"-]").length||m.push("~="),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+y+"+*").length||m.push(".#.+[+~]")})),ce((function(e){var t=h.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+U+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")}))),(n.matchesSelector=X.test(v=f.matches||f.webkitMatchesSelector||f.mozMatchesSelector||f.oMatchesSelector||f.msMatchesSelector))&&ce((function(e){n.disconnectedMatch=v.call(e,"div"),v.call(e,"[s!='']:x"),g.push("!=",R)})),m=m.length&&new RegExp(m.join("|")),g=g.length&&new RegExp(g.join("|")),t=X.test(f.compareDocumentPosition),j=t||X.test(f.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,a=t&&t.parentNode;return e===a||!(!a||1!==a.nodeType||!(n.contains?n.contains(a):e.compareDocumentPosition&&16&e.compareDocumentPosition(a)))}:function(e,t){if(t)while(t=t.parentNode)if(t===e)return!0;return!1},I=t?function(e,t){if(e===t)return d=!0,0;var a=!e.compareDocumentPosition-!t.compareDocumentPosition;return a||(a=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&a||!n.sortDetached&&t.compareDocumentPosition(e)===a?e===h||e.ownerDocument===O&&j(O,e)?-1:t===h||t.ownerDocument===O&&j(O,t)?1:u?$(u,e)-$(u,t):0:4&a?-1:1)}:function(e,t){if(e===t)return d=!0,0;var n,a=0,i=e.parentNode,o=t.parentNode,r=[e],c=[t];if(!i||!o)return e===h?-1:t===h?1:i?-1:o?1:u?$(u,e)-$(u,t):0;if(i===o)return ue(e,t);n=e;while(n=n.parentNode)r.unshift(n);n=t;while(n=n.parentNode)c.unshift(n);while(r[a]===c[a])a++;return a?ue(r[a],c[a]):r[a]===O?-1:c[a]===O?1:0},h):h},ie.matches=function(e,t){return ie(e,null,null,t)},ie.matchesSelector=function(e,t){if((e.ownerDocument||e)!==h&&p(e),t=t.replace(H,"='$1']"),n.matchesSelector&&b&&!E[t+" "]&&(!g||!g.test(t))&&(!m||!m.test(t)))try{var a=v.call(e,t);if(a||n.disconnectedMatch||e.document&&11!==e.document.nodeType)return a}catch(i){}return ie(t,h,null,[e]).length>0},ie.contains=function(e,t){return(e.ownerDocument||e)!==h&&p(e),j(e,t)},ie.attr=function(e,t){(e.ownerDocument||e)!==h&&p(e);var i=a.attrHandle[t.toLowerCase()],o=i&&D.call(a.attrHandle,t.toLowerCase())?i(e,t,!b):void 0;return void 0!==o?o:n.attributes||!b?e.getAttribute(t):(o=e.getAttributeNode(t))&&o.specified?o.value:null},ie.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ie.uniqueSort=function(e){var t,a=[],i=0,o=0;if(d=!n.detectDuplicates,u=!n.sortStable&&e.slice(0),e.sort(I),d){while(t=e[o++])t===e[o]&&(i=a.push(o));while(i--)e.splice(a[i],1)}return u=null,e},i=ie.getText=function(e){var t,n="",a=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=i(e)}else if(3===o||4===o)return e.nodeValue}else while(t=e[a++])n+=i(t);return n},a=ie.selectors={cacheLength:50,createPseudo:re,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ne),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ne),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ie.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ie.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Y.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&W.test(n)&&(t=r(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ne).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=x[e+" "];return t||(t=new RegExp("(^|"+U+")"+e+"("+U+"|$)"))&&x(e,(function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(a){var i=ie.attr(a,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace(M," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,a,i){var o="nth"!==e.slice(0,3),r="last"!==e.slice(-4),c="of-type"===t;return 1===a&&0===i?function(e){return!!e.parentNode}:function(t,n,s){var l,u,d,p,h,f,b=o!==r?"nextSibling":"previousSibling",m=t.parentNode,g=c&&t.nodeName.toLowerCase(),v=!s&&!c,j=!1;if(m){if(o){while(b){p=t;while(p=p[b])if(c?p.nodeName.toLowerCase()===g:1===p.nodeType)return!1;f=b="only"===e&&!f&&"nextSibling"}return!0}if(f=[r?m.firstChild:m.lastChild],r&&v){p=m,d=p[y]||(p[y]={}),u=d[p.uniqueID]||(d[p.uniqueID]={}),l=u[e]||[],h=l[0]===k&&l[1],j=h&&l[2],p=h&&m.childNodes[h];while(p=++h&&p&&p[b]||(j=h=0)||f.pop())if(1===p.nodeType&&++j&&p===t){u[e]=[k,h,j];break}}else if(v&&(p=t,d=p[y]||(p[y]={}),u=d[p.uniqueID]||(d[p.uniqueID]={}),l=u[e]||[],h=l[0]===k&&l[1],j=h),!1===j)while(p=++h&&p&&p[b]||(j=h=0)||f.pop())if((c?p.nodeName.toLowerCase()===g:1===p.nodeType)&&++j&&(v&&(d=p[y]||(p[y]={}),u=d[p.uniqueID]||(d[p.uniqueID]={}),u[e]=[k,j]),p===t))break;return j-=i,j===a||j%a===0&&j/a>=0}}},PSEUDO:function(e,t){var n,i=a.pseudos[e]||a.setFilters[e.toLowerCase()]||ie.error("unsupported pseudo: "+e);return i[y]?i(t):i.length>1?(n=[e,e,"",t],a.setFilters.hasOwnProperty(e.toLowerCase())?re((function(e,n){var a,o=i(e,t),r=o.length;while(r--)a=$(e,o[r]),e[a]=!(n[a]=o[r])})):function(e){return i(e,0,n)}):i}},pseudos:{not:re((function(e){var t=[],n=[],a=c(e.replace(P,"$1"));return a[y]?re((function(e,t,n,i){var o,r=a(e,null,i,[]),c=e.length;while(c--)(o=r[c])&&(e[c]=!(t[c]=o))})):function(e,i,o){return t[0]=e,a(t,null,o,n),t[0]=null,!n.pop()}})),has:re((function(e){return function(t){return ie(e,t).length>0}})),contains:re((function(e){return e=e.replace(te,ne),function(t){return(t.textContent||t.innerText||i(t)).indexOf(e)>-1}})),lang:re((function(e){return Q.test(e||"")||ie.error("unsupported lang: "+e),e=e.replace(te,ne).toLowerCase(),function(t){var n;do{if(n=b?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===f},focus:function(e){return e===h.activeElement&&(!h.hasFocus||h.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!a.pseudos.empty(e)},header:function(e){return K.test(e.nodeName)},input:function(e){return G.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:he((function(){return[0]})),last:he((function(e,t){return[t-1]})),eq:he((function(e,t,n){return[0>n?n+t:n]})),even:he((function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e})),odd:he((function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e})),lt:he((function(e,t,n){for(var a=0>n?n+t:n;--a>=0;)e.push(a);return e})),gt:he((function(e,t,n){for(var a=0>n?n+t:n;++a<t;)e.push(a);return e}))}},a.pseudos.nth=a.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})a.pseudos[t]=de(t);for(t in{submit:!0,reset:!0})a.pseudos[t]=pe(t);function be(){}function me(e){for(var t=0,n=e.length,a="";n>t;t++)a+=e[t].value;return a}function ge(e,t,n){var a=t.dir,i=n&&"parentNode"===a,o=w++;return t.first?function(t,n,o){while(t=t[a])if(1===t.nodeType||i)return e(t,n,o)}:function(t,n,r){var c,s,l,u=[k,o];if(r){while(t=t[a])if((1===t.nodeType||i)&&e(t,n,r))return!0}else while(t=t[a])if(1===t.nodeType||i){if(l=t[y]||(t[y]={}),s=l[t.uniqueID]||(l[t.uniqueID]={}),(c=s[a])&&c[0]===k&&c[1]===o)return u[2]=c[2];if(s[a]=u,u[2]=e(t,n,r))return!0}}}function ve(e){return e.length>1?function(t,n,a){var i=e.length;while(i--)if(!e[i](t,n,a))return!1;return!0}:e[0]}function je(e,t,n){for(var a=0,i=t.length;i>a;a++)ie(e,t[a],n);return n}function ye(e,t,n,a,i){for(var o,r=[],c=0,s=e.length,l=null!=t;s>c;c++)(o=e[c])&&(n&&!n(o,a,i)||(r.push(o),l&&t.push(c)));return r}function Oe(e,t,n,a,i,o){return a&&!a[y]&&(a=Oe(a)),i&&!i[y]&&(i=Oe(i,o)),re((function(o,r,c,s){var l,u,d,p=[],h=[],f=r.length,b=o||je(t||"*",c.nodeType?[c]:c,[]),m=!e||!o&&t?b:ye(b,p,e,c,s),g=n?i||(o?e:f||a)?[]:r:m;if(n&&n(m,g,c,s),a){l=ye(g,h),a(l,[],c,s),u=l.length;while(u--)(d=l[u])&&(g[h[u]]=!(m[h[u]]=d))}if(o){if(i||e){if(i){l=[],u=g.length;while(u--)(d=g[u])&&l.push(m[u]=d);i(null,g=[],l,s)}u=g.length;while(u--)(d=g[u])&&(l=i?$(o,d):p[u])>-1&&(o[l]=!(r[l]=d))}}else g=ye(g===r?g.splice(f,g.length):g),i?i(null,r,g,s):V.apply(r,g)}))}function ke(e){for(var t,n,i,o=e.length,r=a.relative[e[0].type],c=r||a.relative[" "],s=r?1:0,u=ge((function(e){return e===t}),c,!0),d=ge((function(e){return $(t,e)>-1}),c,!0),p=[function(e,n,a){var i=!r&&(a||n!==l)||((t=n).nodeType?u(e,n,a):d(e,n,a));return t=null,i}];o>s;s++)if(n=a.relative[e[s].type])p=[ge(ve(p),n)];else{if(n=a.filter[e[s].type].apply(null,e[s].matches),n[y]){for(i=++s;o>i;i++)if(a.relative[e[i].type])break;return Oe(s>1&&ve(p),s>1&&me(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(P,"$1"),n,i>s&&ke(e.slice(s,i)),o>i&&ke(e=e.slice(i)),o>i&&me(e))}p.push(n)}return ve(p)}function we(e,t){var n=t.length>0,i=e.length>0,o=function(o,r,c,s,u){var d,f,m,g=0,v="0",j=o&&[],y=[],O=l,w=o||i&&a.find.TAG("*",u),x=k+=null==O?1:Math.random()||.1,N=w.length;for(u&&(l=r===h||r||u);v!==N&&null!=(d=w[v]);v++){if(i&&d){f=0,r||d.ownerDocument===h||(p(d),c=!b);while(m=e[f++])if(m(d,r||h,c)){s.push(d);break}u&&(k=x)}n&&((d=!m&&d)&&g--,o&&j.push(d))}if(g+=v,n&&v!==g){f=0;while(m=t[f++])m(j,y,r,c);if(o){if(g>0)while(v--)j[v]||y[v]||(y[v]=T.call(s));y=ye(y)}V.apply(s,y),u&&!o&&y.length>0&&g+t.length>1&&ie.uniqueSort(s)}return u&&(k=x,l=O),j};return n?re(o):o}return be.prototype=a.filters=a.pseudos,a.setFilters=new be,r=ie.tokenize=function(e,t){var n,i,o,r,c,s,l,u=N[e+" "];if(u)return t?0:u.slice(0);c=e,s=[],l=a.preFilter;while(c){for(r in n&&!(i=q.exec(c))||(i&&(c=c.slice(i[0].length)||c),s.push(o=[])),n=!1,(i=z.exec(c))&&(n=i.shift(),o.push({value:n,type:i[0].replace(P," ")}),c=c.slice(n.length)),a.filter)!(i=Y[r].exec(c))||l[r]&&!(i=l[r](i))||(n=i.shift(),o.push({value:n,type:r,matches:i}),c=c.slice(n.length));if(!n)break}return t?c.length:c?ie.error(e):N(e,s).slice(0)},c=ie.compile=function(e,t){var n,a=[],i=[],o=E[e+" "];if(!o){t||(t=r(e)),n=t.length;while(n--)o=ke(t[n]),o[y]?a.push(o):i.push(o);o=E(e,we(i,a)),o.selector=e}return o},s=ie.select=function(e,t,i,o){var s,l,u,d,p,h="function"==typeof e&&e,f=!o&&r(e=h.selector||e);if(i=i||[],1===f.length){if(l=f[0]=f[0].slice(0),l.length>2&&"ID"===(u=l[0]).type&&n.getById&&9===t.nodeType&&b&&a.relative[l[1].type]){if(t=(a.find.ID(u.matches[0].replace(te,ne),t)||[])[0],!t)return i;h&&(t=t.parentNode),e=e.slice(l.shift().value.length)}s=Y.needsContext.test(e)?0:l.length;while(s--){if(u=l[s],a.relative[d=u.type])break;if((p=a.find[d])&&(o=p(u.matches[0].replace(te,ne),Z.test(l[0].type)&&fe(t.parentNode)||t))){if(l.splice(s,1),e=o.length&&me(l),!e)return V.apply(i,o),i;break}}}return(h||c(e,f))(o,t,!b,i,!t||Z.test(e)&&fe(t.parentNode)||t),i},n.sortStable=y.split("").sort(I).join("")===y,n.detectDuplicates=!!d,p(),n.sortDetached=ce((function(e){return 1&e.compareDocumentPosition(h.createElement("div"))})),ce((function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")}))||se("type|href|height|width",(function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)})),n.attributes&&ce((function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")}))||se("value",(function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue})),ce((function(e){return null==e.getAttribute("disabled")}))||se(L,(function(e,t,n){var a;return n?void 0:!0===e[t]?t.toLowerCase():(a=e.getAttributeNode(t))&&a.specified?a.value:null})),ie}(n);v.find=x,v.expr=x.selectors,v.expr[":"]=v.expr.pseudos,v.uniqueSort=v.unique=x.uniqueSort,v.text=x.getText,v.isXMLDoc=x.isXML,v.contains=x.contains;var N=function(e,t,n){var a=[],i=void 0!==n;while((e=e[t])&&9!==e.nodeType)if(1===e.nodeType){if(i&&v(e).is(n))break;a.push(e)}return a},E=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},I=v.expr.match.needsContext,C=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,D=/^.[^:#\[\.,]*$/;function S(e,t,n){if(v.isFunction(t))return v.grep(e,(function(e,a){return!!t.call(e,a,e)!==n}));if(t.nodeType)return v.grep(e,(function(e){return e===t!==n}));if("string"==typeof t){if(D.test(t))return v.filter(t,e,n);t=v.filter(t,e)}return v.grep(e,(function(e){return v.inArray(e,t)>-1!==n}))}v.filter=function(e,t,n){var a=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===a.nodeType?v.find.matchesSelector(a,e)?[a]:[]:v.find.matches(e,v.grep(t,(function(e){return 1===e.nodeType})))},v.fn.extend({find:function(e){var t,n=[],a=this,i=a.length;if("string"!=typeof e)return this.pushStack(v(e).filter((function(){for(t=0;i>t;t++)if(v.contains(a[t],this))return!0})));for(t=0;i>t;t++)v.find(e,a[t],n);return n=this.pushStack(i>1?v.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(S(this,e||[],!1))},not:function(e){return this.pushStack(S(this,e||[],!0))},is:function(e){return!!S(this,"string"==typeof e&&I.test(e)?v(e):e||[],!1).length}});var T,A=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,V=v.fn.init=function(e,t,n){var a,i;if(!e)return this;if(n=n||T,"string"==typeof e){if(a="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:A.exec(e),!a||!a[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(a[1]){if(t=t instanceof v?t[0]:t,v.merge(this,v.parseHTML(a[1],t&&t.nodeType?t.ownerDocument||t:s,!0)),C.test(a[1])&&v.isPlainObject(t))for(a in t)v.isFunction(this[a])?this[a](t[a]):this.attr(a,t[a]);return this}if(i=s.getElementById(a[2]),i&&i.parentNode){if(i.id!==a[2])return T.find(e);this.length=1,this[0]=i}return this.context=s,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):v.isFunction(e)?"undefined"!=typeof n.ready?n.ready(e):e(v):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),v.makeArray(e,this))};V.prototype=v.fn,T=v(s);var B=/^(?:parents|prev(?:Until|All))/,$={children:!0,contents:!0,next:!0,prev:!0};function L(e,t){do{e=e[t]}while(e&&1!==e.nodeType);return e}v.fn.extend({has:function(e){var t,n=v(e,this),a=n.length;return this.filter((function(){for(t=0;a>t;t++)if(v.contains(this,n[t]))return!0}))},closest:function(e,t){for(var n,a=0,i=this.length,o=[],r=I.test(e)||"string"!=typeof e?v(e,t||this.context):0;i>a;a++)for(n=this[a];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(r?r.index(n)>-1:1===n.nodeType&&v.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?v.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?v.inArray(this[0],v(e)):v.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(v.uniqueSort(v.merge(this.get(),v(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),v.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return N(e,"parentNode")},parentsUntil:function(e,t,n){return N(e,"parentNode",n)},next:function(e){return L(e,"nextSibling")},prev:function(e){return L(e,"previousSibling")},nextAll:function(e){return N(e,"nextSibling")},prevAll:function(e){return N(e,"previousSibling")},nextUntil:function(e,t,n){return N(e,"nextSibling",n)},prevUntil:function(e,t,n){return N(e,"previousSibling",n)},siblings:function(e){return E((e.parentNode||{}).firstChild,e)},children:function(e){return E(e.firstChild)},contents:function(e){return v.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:v.merge([],e.childNodes)}},(function(e,t){v.fn[e]=function(n,a){var i=v.map(this,t,n);return"Until"!==e.slice(-5)&&(a=n),a&&"string"==typeof a&&(i=v.filter(a,i)),this.length>1&&($[e]||(i=v.uniqueSort(i)),B.test(e)&&(i=i.reverse())),this.pushStack(i)}}));var U,F,_=/\S+/g;function R(e){var t={};return v.each(e.match(_)||[],(function(e,n){t[n]=!0})),t}function M(){s.addEventListener?(s.removeEventListener("DOMContentLoaded",P),n.removeEventListener("load",P)):(s.detachEvent("onreadystatechange",P),n.detachEvent("onload",P))}function P(){(s.addEventListener||"load"===n.event.type||"complete"===s.readyState)&&(M(),v.ready())}for(F in v.Callbacks=function(e){e="string"==typeof e?R(e):v.extend({},e);var t,n,a,i,o=[],r=[],c=-1,s=function(){for(i=e.once,a=t=!0;r.length;c=-1){n=r.shift();while(++c<o.length)!1===o[c].apply(n[0],n[1])&&e.stopOnFalse&&(c=o.length,n=!1)}e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},l={add:function(){return o&&(n&&!t&&(c=o.length-1,r.push(n)),function t(n){v.each(n,(function(n,a){v.isFunction(a)?e.unique&&l.has(a)||o.push(a):a&&a.length&&"string"!==v.type(a)&&t(a)}))}(arguments),n&&!t&&s()),this},remove:function(){return v.each(arguments,(function(e,t){var n;while((n=v.inArray(t,o,n))>-1)o.splice(n,1),c>=n&&c--})),this},has:function(e){return e?v.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=r=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=!0,n||l.disable(),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=n||[],n=[e,n.slice?n.slice():n],r.push(n),t||s()),this},fire:function(){return l.fireWith(this,arguments),this},fired:function(){return!!a}};return l},v.extend({Deferred:function(e){var t=[["resolve","done",v.Callbacks("once memory"),"resolved"],["reject","fail",v.Callbacks("once memory"),"rejected"],["notify","progress",v.Callbacks("memory")]],n="pending",a={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var e=arguments;return v.Deferred((function(n){v.each(t,(function(t,o){var r=v.isFunction(e[t])&&e[t];i[o[1]]((function(){var e=r&&r.apply(this,arguments);e&&v.isFunction(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[o[0]+"With"](this===a?n.promise():this,r?[e]:arguments)}))})),e=null})).promise()},promise:function(e){return null!=e?v.extend(e,a):a}},i={};return a.pipe=a.then,v.each(t,(function(e,o){var r=o[2],c=o[3];a[o[1]]=r.add,c&&r.add((function(){n=c}),t[1^e][2].disable,t[2][2].lock),i[o[0]]=function(){return i[o[0]+"With"](this===i?a:this,arguments),this},i[o[0]+"With"]=r.fireWith})),a.promise(i),e&&e.call(i,i),i},when:function(e){var t,n,a,i=0,o=l.call(arguments),r=o.length,c=1!==r||e&&v.isFunction(e.promise)?r:0,s=1===c?e:v.Deferred(),u=function(e,n,a){return function(i){n[e]=this,a[e]=arguments.length>1?l.call(arguments):i,a===t?s.notifyWith(n,a):--c||s.resolveWith(n,a)}};if(r>1)for(t=new Array(r),n=new Array(r),a=new Array(r);r>i;i++)o[i]&&v.isFunction(o[i].promise)?o[i].promise().progress(u(i,n,t)).done(u(i,a,o)).fail(s.reject):--c;return c||s.resolveWith(a,o),s.promise()}}),v.fn.ready=function(e){return v.ready.promise().done(e),this},v.extend({isReady:!1,readyWait:1,holdReady:function(e){e?v.readyWait++:v.ready(!0)},ready:function(e){(!0===e?--v.readyWait:v.isReady)||(v.isReady=!0,!0!==e&&--v.readyWait>0||(U.resolveWith(s,[v]),v.fn.triggerHandler&&(v(s).triggerHandler("ready"),v(s).off("ready"))))}}),v.ready.promise=function(e){if(!U)if(U=v.Deferred(),"complete"===s.readyState||"loading"!==s.readyState&&!s.documentElement.doScroll)n.setTimeout(v.ready);else if(s.addEventListener)s.addEventListener("DOMContentLoaded",P),n.addEventListener("load",P);else{s.attachEvent("onreadystatechange",P),n.attachEvent("onload",P);var t=!1;try{t=null==n.frameElement&&s.documentElement}catch(l){}t&&t.doScroll&&function a(){if(!v.isReady){try{t.doScroll("left")}catch(e){return n.setTimeout(a,50)}M(),v.ready()}}()}return U.promise(e)},v.ready.promise(),v(m))break;m.ownFirst="0"===F,m.inlineBlockNeedsLayout=!1,v((function(){var e,t,n,a;n=s.getElementsByTagName("body")[0],n&&n.style&&(t=s.createElement("div"),a=s.createElement("div"),a.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(a).appendChild(t),"undefined"!=typeof t.style.zoom&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",m.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(a))})),function(){var e=s.createElement("div");m.deleteExpando=!0;try{delete e.test}catch(r){m.deleteExpando=!1}e=null}();var q=function(e){var t=v.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return(1===n||9===n)&&(!t||!0!==t&&e.getAttribute("classid")===t)},z=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,H=/([A-Z])/g;function W(e,t,n){if(void 0===n&&1===e.nodeType){var a="data-"+t.replace(H,"-$1").toLowerCase();if(n=e.getAttribute(a),"string"==typeof n){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:z.test(n)?v.parseJSON(n):n)}catch(l){}v.data(e,t,n)}else n=void 0}return n}function Q(e){var t;for(t in e)if(("data"!==t||!v.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function Y(e,t,n,a){if(q(e)){var i,r,s=v.expando,l=e.nodeType,u=l?v.cache:e,d=l?e[s]:e[s]&&s;if(d&&u[d]&&(a||u[d].data)||void 0!==n||"string"!=typeof t)return d||(d=l?e[s]=c.pop()||v.guid++:s),u[d]||(u[d]=l?{}:{toJSON:v.noop}),"object"!=o(t)&&"function"!=typeof t||(a?u[d]=v.extend(u[d],t):u[d].data=v.extend(u[d].data,t)),r=u[d],a||(r.data||(r.data={}),r=r.data),void 0!==n&&(r[v.camelCase(t)]=n),"string"==typeof t?(i=r[t],null==i&&(i=r[v.camelCase(t)])):i=r,i}}function G(e,t,n){if(q(e)){var a,i,o=e.nodeType,r=o?v.cache:e,c=o?e[v.expando]:v.expando;if(r[c]){if(t&&(a=n?r[c]:r[c].data)){v.isArray(t)?t=t.concat(v.map(t,v.camelCase)):t in a?t=[t]:(t=v.camelCase(t),t=t in a?[t]:t.split(" ")),i=t.length;while(i--)delete a[t[i]];if(n?!Q(a):!v.isEmptyObject(a))return}(n||(delete r[c].data,Q(r[c])))&&(o?v.cleanData([e],!0):m.deleteExpando||r!=r.window?delete r[c]:r[c]=void 0)}}}v.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-************"},hasData:function(e){return e=e.nodeType?v.cache[e[v.expando]]:e[v.expando],!!e&&!Q(e)},data:function(e,t,n){return Y(e,t,n)},removeData:function(e,t){return G(e,t)},_data:function(e,t,n){return Y(e,t,n,!0)},_removeData:function(e,t){return G(e,t,!0)}}),v.fn.extend({data:function(e,t){var n,a,i,r=this[0],c=r&&r.attributes;if(void 0===e){if(this.length&&(i=v.data(r),1===r.nodeType&&!v._data(r,"parsedAttrs"))){n=c.length;while(n--)c[n]&&(a=c[n].name,0===a.indexOf("data-")&&(a=v.camelCase(a.slice(5)),W(r,a,i[a])));v._data(r,"parsedAttrs",!0)}return i}return"object"==o(e)?this.each((function(){v.data(this,e)})):arguments.length>1?this.each((function(){v.data(this,e,t)})):r?W(r,e,v.data(r,e)):void 0},removeData:function(e){return this.each((function(){v.removeData(this,e)}))}}),v.extend({queue:function(e,t,n){var a;return e?(t=(t||"fx")+"queue",a=v._data(e,t),n&&(!a||v.isArray(n)?a=v._data(e,t,v.makeArray(n)):a.push(n)),a||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=v.queue(e,t),a=n.length,i=n.shift(),o=v._queueHooks(e,t),r=function(){v.dequeue(e,t)};"inprogress"===i&&(i=n.shift(),a--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,r,o)),!a&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return v._data(e,n)||v._data(e,n,{empty:v.Callbacks("once memory").add((function(){v._removeData(e,t+"queue"),v._removeData(e,n)}))})}}),v.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?v.queue(this[0],e):void 0===t?this:this.each((function(){var n=v.queue(this,e,t);v._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&v.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){v.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,a=1,i=v.Deferred(),o=this,r=this.length,c=function(){--a||i.resolveWith(o,[o])};"string"!=typeof e&&(t=e,e=void 0),e=e||"fx";while(r--)n=v._data(o[r],e+"queueHooks"),n&&n.empty&&(a++,n.empty.add(c));return c(),i.promise(t)}}),function(){var e;m.shrinkWrapBlocks=function(){return null!=e?e:(e=!1,n=s.getElementsByTagName("body")[0],n&&n.style?(t=s.createElement("div"),a=s.createElement("div"),a.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(a).appendChild(t),"undefined"!=typeof t.style.zoom&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(s.createElement("div")).style.width="5px",e=3!==t.offsetWidth),n.removeChild(a),e):void 0);var t,n,a}}();var K=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,X=new RegExp("^(?:([+-])=|)("+K+")([a-z%]*)$","i"),J=["Top","Right","Bottom","Left"],Z=function(e,t){return e=t||e,"none"===v.css(e,"display")||!v.contains(e.ownerDocument,e)};function ee(e,t,n,a){var i,o=1,r=20,c=a?function(){return a.cur()}:function(){return v.css(e,t,"")},s=c(),l=n&&n[3]||(v.cssNumber[t]?"":"px"),u=(v.cssNumber[t]||"px"!==l&&+s)&&X.exec(v.css(e,t));if(u&&u[3]!==l){l=l||u[3],n=n||[],u=+s||1;do{o=o||".5",u/=o,v.style(e,t,u+l)}while(o!==(o=c()/s)&&1!==o&&--r)}return n&&(u=+u||+s||0,i=n[1]?u+(n[1]+1)*n[2]:+n[2],a&&(a.unit=l,a.start=u,a.end=i)),i}var te=function e(t,n,a,i,o,r,c){var s=0,l=t.length,u=null==a;if("object"===v.type(a))for(s in o=!0,a)e(t,n,s,a[s],!0,r,c);else if(void 0!==i&&(o=!0,v.isFunction(i)||(c=!0),u&&(c?(n.call(t,i),n=null):(u=n,n=function(e,t,n){return u.call(v(e),n)})),n))for(;l>s;s++)n(t[s],a,c?i:i.call(t[s],s,n(t[s],a)));return o?t:u?n.call(t):l?n(t[0],a):r},ne=/^(?:checkbox|radio)$/i,ae=/<([\w:-]+)/,ie=/^$|\/(?:java|ecma)script/i,oe=/^\s+/,re="abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video";function ce(e){var t=re.split("|"),n=e.createDocumentFragment();if(n.createElement)while(t.length)n.createElement(t.pop());return n}!function(){var e=s.createElement("div"),t=s.createDocumentFragment(),n=s.createElement("input");e.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",m.leadingWhitespace=3===e.firstChild.nodeType,m.tbody=!e.getElementsByTagName("tbody").length,m.htmlSerialize=!!e.getElementsByTagName("link").length,m.html5Clone="<:nav></:nav>"!==s.createElement("nav").cloneNode(!0).outerHTML,n.type="checkbox",n.checked=!0,t.appendChild(n),m.appendChecked=n.checked,e.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue,t.appendChild(e),n=s.createElement("input"),n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),e.appendChild(n),m.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,m.noCloneEvent=!!e.addEventListener,e[v.expando]=1,m.attributes=!e.getAttribute(v.expando)}();var se={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:m.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};function le(e,t){var n,a,i=0,o="undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t||"*"):"undefined"!=typeof e.querySelectorAll?e.querySelectorAll(t||"*"):void 0;if(!o)for(o=[],n=e.childNodes||e;null!=(a=n[i]);i++)!t||v.nodeName(a,t)?o.push(a):v.merge(o,le(a,t));return void 0===t||t&&v.nodeName(e,t)?v.merge([e],o):o}function ue(e,t){for(var n,a=0;null!=(n=e[a]);a++)v._data(n,"globalEval",!t||v._data(t[a],"globalEval"))}se.optgroup=se.option,se.tbody=se.tfoot=se.colgroup=se.caption=se.thead,se.th=se.td;var de=/<|&#?\w+;/,pe=/<tbody/i;function he(e){ne.test(e.type)&&(e.defaultChecked=e.checked)}function fe(e,t,n,a,i){for(var o,r,c,s,l,u,d,p=e.length,h=ce(t),f=[],b=0;p>b;b++)if(r=e[b],r||0===r)if("object"===v.type(r))v.merge(f,r.nodeType?[r]:r);else if(de.test(r)){s=s||h.appendChild(t.createElement("div")),l=(ae.exec(r)||["",""])[1].toLowerCase(),d=se[l]||se._default,s.innerHTML=d[1]+v.htmlPrefilter(r)+d[2],o=d[0];while(o--)s=s.lastChild;if(!m.leadingWhitespace&&oe.test(r)&&f.push(t.createTextNode(oe.exec(r)[0])),!m.tbody){r="table"!==l||pe.test(r)?"<table>"!==d[1]||pe.test(r)?0:s:s.firstChild,o=r&&r.childNodes.length;while(o--)v.nodeName(u=r.childNodes[o],"tbody")&&!u.childNodes.length&&r.removeChild(u)}v.merge(f,s.childNodes),s.textContent="";while(s.firstChild)s.removeChild(s.firstChild);s=h.lastChild}else f.push(t.createTextNode(r));s&&h.removeChild(s),m.appendChecked||v.grep(le(f,"input"),he),b=0;while(r=f[b++])if(a&&v.inArray(r,a)>-1)i&&i.push(r);else if(c=v.contains(r.ownerDocument,r),s=le(h.appendChild(r),"script"),c&&ue(s),n){o=0;while(r=s[o++])ie.test(r.type||"")&&n.push(r)}return s=null,h}!function(){var e,t,a=s.createElement("div");for(e in{submit:!0,change:!0,focusin:!0})t="on"+e,(m[e]=t in n)||(a.setAttribute(t,"t"),m[e]=!1===a.attributes[t].expando);a=null}();var be=/^(?:input|select|textarea)$/i,me=/^key/,ge=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,ve=/^(?:focusinfocus|focusoutblur)$/,je=/^([^.]*)(?:\.(.+)|)/;function ye(){return!0}function Oe(){return!1}function ke(){try{return s.activeElement}catch(n){}}function we(e,t,n,a,i,r){var c,s;if("object"==o(t)){for(s in"string"!=typeof n&&(a=a||n,n=void 0),t)we(e,s,n,a,t[s],r);return e}if(null==a&&null==i?(i=n,a=n=void 0):null==i&&("string"==typeof n?(i=a,a=void 0):(i=a,a=n,n=void 0)),!1===i)i=Oe;else if(!i)return e;return 1===r&&(c=i,i=function(e){return v().off(e),c.apply(this,arguments)},i.guid=c.guid||(c.guid=v.guid++)),e.each((function(){v.event.add(this,t,i,a,n)}))}v.event={global:{},add:function(e,t,n,a,i){var o,r,c,s,l,u,d,p,h,f,b,m=v._data(e);if(m){n.handler&&(s=n,n=s.handler,i=s.selector),n.guid||(n.guid=v.guid++),(r=m.events)||(r=m.events={}),(u=m.handle)||(u=m.handle=function(e){return"undefined"==typeof v||e&&v.event.triggered===e.type?void 0:v.event.dispatch.apply(u.elem,arguments)},u.elem=e),t=(t||"").match(_)||[""],c=t.length;while(c--)o=je.exec(t[c])||[],h=b=o[1],f=(o[2]||"").split(".").sort(),h&&(l=v.event.special[h]||{},h=(i?l.delegateType:l.bindType)||h,l=v.event.special[h]||{},d=v.extend({type:h,origType:b,data:a,handler:n,guid:n.guid,selector:i,needsContext:i&&v.expr.match.needsContext.test(i),namespace:f.join(".")},s),(p=r[h])||(p=r[h]=[],p.delegateCount=0,l.setup&&!1!==l.setup.call(e,a,f,u)||(e.addEventListener?e.addEventListener(h,u,!1):e.attachEvent&&e.attachEvent("on"+h,u))),l.add&&(l.add.call(e,d),d.handler.guid||(d.handler.guid=n.guid)),i?p.splice(p.delegateCount++,0,d):p.push(d),v.event.global[h]=!0);e=null}},remove:function(e,t,n,a,i){var o,r,c,s,l,u,d,p,h,f,b,m=v.hasData(e)&&v._data(e);if(m&&(u=m.events)){t=(t||"").match(_)||[""],l=t.length;while(l--)if(c=je.exec(t[l])||[],h=b=c[1],f=(c[2]||"").split(".").sort(),h){d=v.event.special[h]||{},h=(a?d.delegateType:d.bindType)||h,p=u[h]||[],c=c[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=p.length;while(o--)r=p[o],!i&&b!==r.origType||n&&n.guid!==r.guid||c&&!c.test(r.namespace)||a&&a!==r.selector&&("**"!==a||!r.selector)||(p.splice(o,1),r.selector&&p.delegateCount--,d.remove&&d.remove.call(e,r));s&&!p.length&&(d.teardown&&!1!==d.teardown.call(e,f,m.handle)||v.removeEvent(e,h,m.handle),delete u[h])}else for(h in u)v.event.remove(e,h+t[l],n,a,!0);v.isEmptyObject(u)&&(delete m.handle,v._removeData(e,"events"))}},trigger:function(e,t,a,i){var r,c,l,u,d,p,h,f=[a||s],m=b.call(e,"type")?e.type:e,g=b.call(e,"namespace")?e.namespace.split("."):[];if(l=p=a=a||s,3!==a.nodeType&&8!==a.nodeType&&!ve.test(m+v.event.triggered)&&(m.indexOf(".")>-1&&(g=m.split("."),m=g.shift(),g.sort()),c=m.indexOf(":")<0&&"on"+m,e=e[v.expando]?e:new v.Event(m,"object"==o(e)&&e),e.isTrigger=i?2:3,e.namespace=g.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+g.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=a),t=null==t?[e]:v.makeArray(t,[e]),d=v.event.special[m]||{},i||!d.trigger||!1!==d.trigger.apply(a,t))){if(!i&&!d.noBubble&&!v.isWindow(a)){for(u=d.delegateType||m,ve.test(u+m)||(l=l.parentNode);l;l=l.parentNode)f.push(l),p=l;p===(a.ownerDocument||s)&&f.push(p.defaultView||p.parentWindow||n)}h=0;while((l=f[h++])&&!e.isPropagationStopped())e.type=h>1?u:d.bindType||m,r=(v._data(l,"events")||{})[e.type]&&v._data(l,"handle"),r&&r.apply(l,t),r=c&&l[c],r&&r.apply&&q(l)&&(e.result=r.apply(l,t),!1===e.result&&e.preventDefault());if(e.type=m,!i&&!e.isDefaultPrevented()&&(!d._default||!1===d._default.apply(f.pop(),t))&&q(a)&&c&&a[m]&&!v.isWindow(a)){p=a[c],p&&(a[c]=null),v.event.triggered=m;try{a[m]()}catch(w){}v.event.triggered=void 0,p&&(a[c]=p)}return e.result}},dispatch:function(e){e=v.event.fix(e);var t,n,a,i,o,r=[],c=l.call(arguments),s=(v._data(this,"events")||{})[e.type]||[],u=v.event.special[e.type]||{};if(c[0]=e,e.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,e)){r=v.event.handlers.call(this,e,s),t=0;while((i=r[t++])&&!e.isPropagationStopped()){e.currentTarget=i.elem,n=0;while((o=i.handlers[n++])&&!e.isImmediatePropagationStopped())e.rnamespace&&!e.rnamespace.test(o.namespace)||(e.handleObj=o,e.data=o.data,a=((v.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,c),void 0!==a&&!1===(e.result=a)&&(e.preventDefault(),e.stopPropagation()))}return u.postDispatch&&u.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,a,i,o,r=[],c=t.delegateCount,s=e.target;if(c&&s.nodeType&&("click"!==e.type||isNaN(e.button)||e.button<1))for(;s!=this;s=s.parentNode||this)if(1===s.nodeType&&(!0!==s.disabled||"click"!==e.type)){for(a=[],n=0;c>n;n++)o=t[n],i=o.selector+" ",void 0===a[i]&&(a[i]=o.needsContext?v(i,this).index(s)>-1:v.find(i,this,null,[s]).length),a[i]&&a.push(o);a.length&&r.push({elem:s,handlers:a})}return c<t.length&&r.push({elem:this,handlers:t.slice(c)}),r},fix:function(e){if(e[v.expando])return e;var t,n,a,i=e.type,o=e,r=this.fixHooks[i];r||(this.fixHooks[i]=r=ge.test(i)?this.mouseHooks:me.test(i)?this.keyHooks:{}),a=r.props?this.props.concat(r.props):this.props,e=new v.Event(o),t=a.length;while(t--)n=a[t],e[n]=o[n];return e.target||(e.target=o.srcElement||s),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,r.filter?r.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,a,i,o=t.button,r=t.fromElement;return null==e.pageX&&null!=t.clientX&&(a=e.target.ownerDocument||s,i=a.documentElement,n=a.body,e.pageX=t.clientX+(i&&i.scrollLeft||n&&n.scrollLeft||0)-(i&&i.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(i&&i.scrollTop||n&&n.scrollTop||0)-(i&&i.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&r&&(e.relatedTarget=r===e.target?t.toElement:r),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==ke()&&this.focus)try{return this.focus(),!1}catch(n){}},delegateType:"focusin"},blur:{trigger:function(){return this===ke()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return v.nodeName(this,"input")&&"checkbox"===this.type&&this.click?(this.click(),!1):void 0},_default:function(e){return v.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n){var a=v.extend(new v.Event,n,{type:e,isSimulated:!0});v.event.trigger(a,null,t),a.isDefaultPrevented()&&n.preventDefault()}},v.removeEvent=s.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)}:function(e,t,n){var a="on"+t;e.detachEvent&&("undefined"==typeof e[a]&&(e[a]=null),e.detachEvent(a,n))},v.Event=function(e,t){return this instanceof v.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?ye:Oe):this.type=e,t&&v.extend(this,t),this.timeStamp=e&&e.timeStamp||v.now(),void(this[v.expando]=!0)):new v.Event(e,t)},v.Event.prototype={constructor:v.Event,isDefaultPrevented:Oe,isPropagationStopped:Oe,isImmediatePropagationStopped:Oe,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=ye,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=ye,e&&!this.isSimulated&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=ye,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},v.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){v.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,a=this,i=e.relatedTarget,o=e.handleObj;return i&&(i===a||v.contains(a,i))||(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}})),m.submit||(v.event.special.submit={setup:function(){return!v.nodeName(this,"form")&&void v.event.add(this,"click._submit keypress._submit",(function(e){var t=e.target,n=v.nodeName(t,"input")||v.nodeName(t,"button")?v.prop(t,"form"):void 0;n&&!v._data(n,"submit")&&(v.event.add(n,"submit._submit",(function(e){e._submitBubble=!0})),v._data(n,"submit",!0))}))},postDispatch:function(e){e._submitBubble&&(delete e._submitBubble,this.parentNode&&!e.isTrigger&&v.event.simulate("submit",this.parentNode,e))},teardown:function(){return!v.nodeName(this,"form")&&void v.event.remove(this,"._submit")}}),m.change||(v.event.special.change={setup:function(){return be.test(this.nodeName)?("checkbox"!==this.type&&"radio"!==this.type||(v.event.add(this,"propertychange._change",(function(e){"checked"===e.originalEvent.propertyName&&(this._justChanged=!0)})),v.event.add(this,"click._change",(function(e){this._justChanged&&!e.isTrigger&&(this._justChanged=!1),v.event.simulate("change",this,e)}))),!1):void v.event.add(this,"beforeactivate._change",(function(e){var t=e.target;be.test(t.nodeName)&&!v._data(t,"change")&&(v.event.add(t,"change._change",(function(e){!this.parentNode||e.isSimulated||e.isTrigger||v.event.simulate("change",this.parentNode,e)})),v._data(t,"change",!0))}))},handle:function(e){var t=e.target;return this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type?e.handleObj.handler.apply(this,arguments):void 0},teardown:function(){return v.event.remove(this,"._change"),!be.test(this.nodeName)}}),m.focusin||v.each({focus:"focusin",blur:"focusout"},(function(e,t){var n=function(e){v.event.simulate(t,e.target,v.event.fix(e))};v.event.special[t]={setup:function(){var a=this.ownerDocument||this,i=v._data(a,t);i||a.addEventListener(e,n,!0),v._data(a,t,(i||0)+1)},teardown:function(){var a=this.ownerDocument||this,i=v._data(a,t)-1;i?v._data(a,t,i):(a.removeEventListener(e,n,!0),v._removeData(a,t))}}})),v.fn.extend({on:function(e,t,n,a){return we(this,e,t,n,a)},one:function(e,t,n,a){return we(this,e,t,n,a,1)},off:function(e,t,n){var a,i;if(e&&e.preventDefault&&e.handleObj)return a=e.handleObj,v(e.delegateTarget).off(a.namespace?a.origType+"."+a.namespace:a.origType,a.selector,a.handler),this;if("object"==o(e)){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Oe),this.each((function(){v.event.remove(this,e,n,t)}))},trigger:function(e,t){return this.each((function(){v.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];return n?v.event.trigger(e,t,n,!0):void 0}});var xe=/ jQuery\d+="(?:null|\d+)"/g,Ne=new RegExp("<(?:"+re+")[\\s/>]","i"),Ee=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,Ie=/<script|<style|<link/i,Ce=/checked\s*(?:[^=]|=\s*.checked.)/i,De=/^true\/(.*)/,Se=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Te=ce(s),Ae=Te.appendChild(s.createElement("div"));function Ve(e,t){return v.nodeName(e,"table")&&v.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function Be(e){return e.type=(null!==v.find.attr(e,"type"))+"/"+e.type,e}function $e(e){var t=De.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function Le(e,t){if(1===t.nodeType&&v.hasData(e)){var n,a,i,o=v._data(e),r=v._data(t,o),c=o.events;if(c)for(n in delete r.handle,r.events={},c)for(a=0,i=c[n].length;i>a;a++)v.event.add(t,n,c[n][a]);r.data&&(r.data=v.extend({},r.data))}}function Ue(e,t){var n,a,i;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!m.noCloneEvent&&t[v.expando]){for(a in i=v._data(t),i.events)v.removeEvent(t,a,i.handle);t.removeAttribute(v.expando)}"script"===n&&t.text!==e.text?(Be(t).text=e.text,$e(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),m.html5Clone&&e.innerHTML&&!v.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&ne.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}}function Fe(e,t,n,a){t=u.apply([],t);var i,o,r,c,s,l,d=0,p=e.length,h=p-1,f=t[0],b=v.isFunction(f);if(b||p>1&&"string"==typeof f&&!m.checkClone&&Ce.test(f))return e.each((function(i){var o=e.eq(i);b&&(t[0]=f.call(this,i,o.html())),Fe(o,t,n,a)}));if(p&&(l=fe(t,e[0].ownerDocument,!1,e,a),i=l.firstChild,1===l.childNodes.length&&(l=i),i||a)){for(c=v.map(le(l,"script"),Be),r=c.length;p>d;d++)o=l,d!==h&&(o=v.clone(o,!0,!0),r&&v.merge(c,le(o,"script"))),n.call(e[d],o,d);if(r)for(s=c[c.length-1].ownerDocument,v.map(c,$e),d=0;r>d;d++)o=c[d],ie.test(o.type||"")&&!v._data(o,"globalEval")&&v.contains(s,o)&&(o.src?v._evalUrl&&v._evalUrl(o.src):v.globalEval((o.text||o.textContent||o.innerHTML||"").replace(Se,"")));l=i=null}return e}function _e(e,t,n){for(var a,i=t?v.filter(t,e):e,o=0;null!=(a=i[o]);o++)n||1!==a.nodeType||v.cleanData(le(a)),a.parentNode&&(n&&v.contains(a.ownerDocument,a)&&ue(le(a,"script")),a.parentNode.removeChild(a));return e}v.extend({htmlPrefilter:function(e){return e.replace(Ee,"<$1></$2>")},clone:function(e,t,n){var a,i,o,r,c,s=v.contains(e.ownerDocument,e);if(m.html5Clone||v.isXMLDoc(e)||!Ne.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(Ae.innerHTML=e.outerHTML,Ae.removeChild(o=Ae.firstChild)),!(m.noCloneEvent&&m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||v.isXMLDoc(e)))for(a=le(o),c=le(e),r=0;null!=(i=c[r]);++r)a[r]&&Ue(i,a[r]);if(t)if(n)for(c=c||le(e),a=a||le(o),r=0;null!=(i=c[r]);r++)Le(i,a[r]);else Le(e,o);return a=le(o,"script"),a.length>0&&ue(a,!s&&le(e,"script")),a=c=i=null,o},cleanData:function(e,t){for(var n,a,i,o,r=0,s=v.expando,l=v.cache,u=m.attributes,d=v.event.special;null!=(n=e[r]);r++)if((t||q(n))&&(i=n[s],o=i&&l[i])){if(o.events)for(a in o.events)d[a]?v.event.remove(n,a):v.removeEvent(n,a,o.handle);l[i]&&(delete l[i],u||"undefined"==typeof n.removeAttribute?n[s]=void 0:n.removeAttribute(s),c.push(i))}}}),v.fn.extend({domManip:Fe,detach:function(e){return _e(this,e,!0)},remove:function(e){return _e(this,e)},text:function(e){return te(this,(function(e){return void 0===e?v.text(this):this.empty().append((this[0]&&this[0].ownerDocument||s).createTextNode(e))}),null,e,arguments.length)},append:function(){return Fe(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Ve(this,e);t.appendChild(e)}}))},prepend:function(){return Fe(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Ve(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return Fe(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return Fe(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){1===e.nodeType&&v.cleanData(le(e,!1));while(e.firstChild)e.removeChild(e.firstChild);e.options&&v.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return v.clone(this,e,t)}))},html:function(e){return te(this,(function(e){var t=this[0]||{},n=0,a=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(xe,""):void 0;if("string"==typeof e&&!Ie.test(e)&&(m.htmlSerialize||!Ne.test(e))&&(m.leadingWhitespace||!oe.test(e))&&!se[(ae.exec(e)||["",""])[1].toLowerCase()]){e=v.htmlPrefilter(e);try{for(;a>n;n++)t=this[n]||{},1===t.nodeType&&(v.cleanData(le(t,!1)),t.innerHTML=e);t=0}catch(l){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return Fe(this,arguments,(function(t){var n=this.parentNode;v.inArray(this,e)<0&&(v.cleanData(le(this)),n&&n.replaceChild(t,this))}),e)}}),v.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){v.fn[e]=function(e){for(var n,a=0,i=[],o=v(e),r=o.length-1;r>=a;a++)n=a===r?this:this.clone(!0),v(o[a])[t](n),d.apply(i,n.get());return this.pushStack(i)}}));var Re,Me={HTML:"block",BODY:"block"};function Pe(e,t){var n=v(t.createElement(e)).appendTo(t.body),a=v.css(n[0],"display");return n.detach(),a}function qe(e){var t=s,n=Me[e];return n||(n=Pe(e,t),"none"!==n&&n||(Re=(Re||v("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement),t=(Re[0].contentWindow||Re[0].contentDocument).document,t.write(),t.close(),n=Pe(e,t),Re.detach()),Me[e]=n),n}var ze=/^margin/,He=new RegExp("^("+K+")(?!px)[a-z%]+$","i"),We=function(e,t,n,a){var i,o,r={};for(o in t)r[o]=e.style[o],e.style[o]=t[o];for(o in i=n.apply(e,a||[]),t)e.style[o]=r[o];return i},Qe=s.documentElement;!function(){var e,t,a,i,o,r,c=s.createElement("div"),l=s.createElement("div");if(l.style){function u(){var u,d,p=s.documentElement;p.appendChild(c),l.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",e=a=r=!1,t=o=!0,n.getComputedStyle&&(d=n.getComputedStyle(l),e="1%"!==(d||{}).top,r="2px"===(d||{}).marginLeft,a="4px"===(d||{width:"4px"}).width,l.style.marginRight="50%",t="4px"===(d||{marginRight:"4px"}).marginRight,u=l.appendChild(s.createElement("div")),u.style.cssText=l.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",u.style.marginRight=u.style.width="0",l.style.width="1px",o=!parseFloat((n.getComputedStyle(u)||{}).marginRight),l.removeChild(u)),l.style.display="none",i=0===l.getClientRects().length,i&&(l.style.display="",l.innerHTML="<table><tr><td></td><td>t</td></tr></table>",u=l.getElementsByTagName("td"),u[0].style.cssText="margin:0;border:0;padding:0;display:none",i=0===u[0].offsetHeight,i&&(u[0].style.display="",u[1].style.display="none",i=0===u[0].offsetHeight)),p.removeChild(c)}l.style.cssText="float:left;opacity:.5",m.opacity="0.5"===l.style.opacity,m.cssFloat=!!l.style.cssFloat,l.style.backgroundClip="content-box",l.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===l.style.backgroundClip,c=s.createElement("div"),c.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",l.innerHTML="",c.appendChild(l),m.boxSizing=""===l.style.boxSizing||""===l.style.MozBoxSizing||""===l.style.WebkitBoxSizing,v.extend(m,{reliableHiddenOffsets:function(){return null==e&&u(),i},boxSizingReliable:function(){return null==e&&u(),a},pixelMarginRight:function(){return null==e&&u(),t},pixelPosition:function(){return null==e&&u(),e},reliableMarginRight:function(){return null==e&&u(),o},reliableMarginLeft:function(){return null==e&&u(),r}})}}();var Ye,Ge,Ke=/^(top|right|bottom|left)$/;function Xe(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}n.getComputedStyle?(Ye=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=n),t.getComputedStyle(e)},Ge=function(e,t,n){var a,i,o,r,c=e.style;return n=n||Ye(e),r=n?n.getPropertyValue(t)||n[t]:void 0,""!==r&&void 0!==r||v.contains(e.ownerDocument,e)||(r=v.style(e,t)),n&&!m.pixelMarginRight()&&He.test(r)&&ze.test(t)&&(a=c.width,i=c.minWidth,o=c.maxWidth,c.minWidth=c.maxWidth=c.width=r,r=n.width,c.width=a,c.minWidth=i,c.maxWidth=o),void 0===r?r:r+""}):Qe.currentStyle&&(Ye=function(e){return e.currentStyle},Ge=function(e,t,n){var a,i,o,r,c=e.style;return n=n||Ye(e),r=n?n[t]:void 0,null==r&&c&&c[t]&&(r=c[t]),He.test(r)&&!Ke.test(t)&&(a=c.left,i=e.runtimeStyle,o=i&&i.left,o&&(i.left=e.currentStyle.left),c.left="fontSize"===t?"1em":r,r=c.pixelLeft+"px",c.left=a,o&&(i.left=o)),void 0===r?r:r+""||"auto"});var Je=/alpha\([^)]*\)/i,Ze=/opacity\s*=\s*([^)]*)/i,et=/^(none|table(?!-c[ea]).+)/,tt=new RegExp("^("+K+")(.*)$","i"),nt={position:"absolute",visibility:"hidden",display:"block"},at={letterSpacing:"0",fontWeight:"400"},it=["Webkit","O","Moz","ms"],ot=s.createElement("div").style;function rt(e){if(e in ot)return e;var t=e.charAt(0).toUpperCase()+e.slice(1),n=it.length;while(n--)if(e=it[n]+t,e in ot)return e}function ct(e,t){for(var n,a,i,o=[],r=0,c=e.length;c>r;r++)a=e[r],a.style&&(o[r]=v._data(a,"olddisplay"),n=a.style.display,t?(o[r]||"none"!==n||(a.style.display=""),""===a.style.display&&Z(a)&&(o[r]=v._data(a,"olddisplay",qe(a.nodeName)))):(i=Z(a),(n&&"none"!==n||!i)&&v._data(a,"olddisplay",i?n:v.css(a,"display"))));for(r=0;c>r;r++)a=e[r],a.style&&(t&&"none"!==a.style.display&&""!==a.style.display||(a.style.display=t?o[r]||"":"none"));return e}function st(e,t,n){var a=tt.exec(t);return a?Math.max(0,a[1]-(n||0))+(a[2]||"px"):t}function lt(e,t,n,a,i){for(var o=n===(a?"border":"content")?4:"width"===t?1:0,r=0;4>o;o+=2)"margin"===n&&(r+=v.css(e,n+J[o],!0,i)),a?("content"===n&&(r-=v.css(e,"padding"+J[o],!0,i)),"margin"!==n&&(r-=v.css(e,"border"+J[o]+"Width",!0,i))):(r+=v.css(e,"padding"+J[o],!0,i),"padding"!==n&&(r+=v.css(e,"border"+J[o]+"Width",!0,i)));return r}function ut(e,t,a){var i=!0,o="width"===t?e.offsetWidth:e.offsetHeight,r=Ye(e),c=m.boxSizing&&"border-box"===v.css(e,"boxSizing",!1,r);if(s.msFullscreenElement&&n.top!==n&&e.getClientRects().length&&(o=Math.round(100*e.getBoundingClientRect()[t])),0>=o||null==o){if(o=Ge(e,t,r),(0>o||null==o)&&(o=e.style[t]),He.test(o))return o;i=c&&(m.boxSizingReliable()||o===e.style[t]),o=parseFloat(o)||0}return o+lt(e,t,a||(c?"border":"content"),i,r)+"px"}function dt(e,t,n,a,i){return new dt.prototype.init(e,t,n,a,i)}v.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ge(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:m.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,a){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,r,c,s=v.camelCase(t),l=e.style;if(t=v.cssProps[s]||(v.cssProps[s]=rt(s)||s),c=v.cssHooks[t]||v.cssHooks[s],void 0===n)return c&&"get"in c&&void 0!==(i=c.get(e,!1,a))?i:l[t];if(r=o(n),"string"===r&&(i=X.exec(n))&&i[1]&&(n=ee(e,t,i),r="number"),null!=n&&n===n&&("number"===r&&(n+=i&&i[3]||(v.cssNumber[s]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),!c||!("set"in c)||void 0!==(n=c.set(e,n,a))))try{l[t]=n}catch(f){}}},css:function(e,t,n,a){var i,o,r,c=v.camelCase(t);return t=v.cssProps[c]||(v.cssProps[c]=rt(c)||c),r=v.cssHooks[t]||v.cssHooks[c],r&&"get"in r&&(o=r.get(e,!0,n)),void 0===o&&(o=Ge(e,t,a)),"normal"===o&&t in at&&(o=at[t]),""===n||n?(i=parseFloat(o),!0===n||isFinite(i)?i||0:o):o}}),v.each(["height","width"],(function(e,t){v.cssHooks[t]={get:function(e,n,a){return n?et.test(v.css(e,"display"))&&0===e.offsetWidth?We(e,nt,(function(){return ut(e,t,a)})):ut(e,t,a):void 0},set:function(e,n,a){var i=a&&Ye(e);return st(e,n,a?lt(e,t,a,m.boxSizing&&"border-box"===v.css(e,"boxSizing",!1,i),i):0)}}})),m.opacity||(v.cssHooks.opacity={get:function(e,t){return Ze.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,a=e.currentStyle,i=v.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=a&&a.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===v.trim(o.replace(Je,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||a&&!a.filter)||(n.filter=Je.test(o)?o.replace(Je,i):o+" "+i)}}),v.cssHooks.marginRight=Xe(m.reliableMarginRight,(function(e,t){return t?We(e,{display:"inline-block"},Ge,[e,"marginRight"]):void 0})),v.cssHooks.marginLeft=Xe(m.reliableMarginLeft,(function(e,t){return t?(parseFloat(Ge(e,"marginLeft"))||(v.contains(e.ownerDocument,e)?e.getBoundingClientRect().left-We(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})):0))+"px":void 0})),v.each({margin:"",padding:"",border:"Width"},(function(e,t){v.cssHooks[e+t]={expand:function(n){for(var a=0,i={},o="string"==typeof n?n.split(" "):[n];4>a;a++)i[e+J[a]+t]=o[a]||o[a-2]||o[0];return i}},ze.test(e)||(v.cssHooks[e+t].set=st)})),v.fn.extend({css:function(e,t){return te(this,(function(e,t,n){var a,i,o={},r=0;if(v.isArray(t)){for(a=Ye(e),i=t.length;i>r;r++)o[t[r]]=v.css(e,t[r],!1,a);return o}return void 0!==n?v.style(e,t,n):v.css(e,t)}),e,t,arguments.length>1)},show:function(){return ct(this,!0)},hide:function(){return ct(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each((function(){Z(this)?v(this).show():v(this).hide()}))}}),v.Tween=dt,dt.prototype={constructor:dt,init:function(e,t,n,a,i,o){this.elem=e,this.prop=n,this.easing=i||v.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=a,this.unit=o||(v.cssNumber[n]?"":"px")},cur:function(){var e=dt.propHooks[this.prop];return e&&e.get?e.get(this):dt.propHooks._default.get(this)},run:function(e){var t,n=dt.propHooks[this.prop];return this.options.duration?this.pos=t=v.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):dt.propHooks._default.set(this),this}},dt.prototype.init.prototype=dt.prototype,dt.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=v.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){v.fx.step[e.prop]?v.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[v.cssProps[e.prop]]&&!v.cssHooks[e.prop]?e.elem[e.prop]=e.now:v.style(e.elem,e.prop,e.now+e.unit)}}},dt.propHooks.scrollTop=dt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},v.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},v.fx=dt.prototype.init,v.fx.step={};var pt,ht,ft=/^(?:toggle|show|hide)$/,bt=/queueHooks$/;function mt(){return n.setTimeout((function(){pt=void 0})),pt=v.now()}function gt(e,t){var n,a={height:e},i=0;for(t=t?1:0;4>i;i+=2-t)n=J[i],a["margin"+n]=a["padding"+n]=e;return t&&(a.opacity=a.width=e),a}function vt(e,t,n){for(var a,i=(Ot.tweeners[t]||[]).concat(Ot.tweeners["*"]),o=0,r=i.length;r>o;o++)if(a=i[o].call(n,t,e))return a}function jt(e,t,n){var a,i,o,r,c,s,l,u,d=this,p={},h=e.style,f=e.nodeType&&Z(e),b=v._data(e,"fxshow");for(a in n.queue||(c=v._queueHooks(e,"fx"),null==c.unqueued&&(c.unqueued=0,s=c.empty.fire,c.empty.fire=function(){c.unqueued||s()}),c.unqueued++,d.always((function(){d.always((function(){c.unqueued--,v.queue(e,"fx").length||c.empty.fire()}))}))),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],l=v.css(e,"display"),u="none"===l?v._data(e,"olddisplay")||qe(e.nodeName):l,"inline"===u&&"none"===v.css(e,"float")&&(m.inlineBlockNeedsLayout&&"inline"!==qe(e.nodeName)?h.zoom=1:h.display="inline-block")),n.overflow&&(h.overflow="hidden",m.shrinkWrapBlocks()||d.always((function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]}))),t)if(i=t[a],ft.exec(i)){if(delete t[a],o=o||"toggle"===i,i===(f?"hide":"show")){if("show"!==i||!b||void 0===b[a])continue;f=!0}p[a]=b&&b[a]||v.style(e,a)}else l=void 0;if(v.isEmptyObject(p))"inline"===("none"===l?qe(e.nodeName):l)&&(h.display=l);else for(a in b?"hidden"in b&&(f=b.hidden):b=v._data(e,"fxshow",{}),o&&(b.hidden=!f),f?v(e).show():d.done((function(){v(e).hide()})),d.done((function(){var t;for(t in v._removeData(e,"fxshow"),p)v.style(e,t,p[t])})),p)r=vt(f?b[a]:0,a,d),a in b||(b[a]=r.start,f&&(r.end=r.start,r.start="width"===a||"height"===a?1:0))}function yt(e,t){var n,a,i,o,r;for(n in e)if(a=v.camelCase(n),i=t[a],o=e[n],v.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==a&&(e[a]=o,delete e[n]),r=v.cssHooks[a],r&&"expand"in r)for(n in o=r.expand(o),delete e[a],o)n in e||(e[n]=o[n],t[n]=i);else t[a]=i}function Ot(e,t,n){var a,i,o=0,r=Ot.prefilters.length,c=v.Deferred().always((function(){delete s.elem})),s=function(){if(i)return!1;for(var t=pt||mt(),n=Math.max(0,l.startTime+l.duration-t),a=n/l.duration||0,o=1-a,r=0,s=l.tweens.length;s>r;r++)l.tweens[r].run(o);return c.notifyWith(e,[l,o,n]),1>o&&s?n:(c.resolveWith(e,[l]),!1)},l=c.promise({elem:e,props:v.extend({},t),opts:v.extend(!0,{specialEasing:{},easing:v.easing._default},n),originalProperties:t,originalOptions:n,startTime:pt||mt(),duration:n.duration,tweens:[],createTween:function(t,n){var a=v.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(a),a},stop:function(t){var n=0,a=t?l.tweens.length:0;if(i)return this;for(i=!0;a>n;n++)l.tweens[n].run(1);return t?(c.notifyWith(e,[l,1,0]),c.resolveWith(e,[l,t])):c.rejectWith(e,[l,t]),this}}),u=l.props;for(yt(u,l.opts.specialEasing);r>o;o++)if(a=Ot.prefilters[o].call(l,e,u,l.opts))return v.isFunction(a.stop)&&(v._queueHooks(l.elem,l.opts.queue).stop=v.proxy(a.stop,a)),a;return v.map(u,vt,l),v.isFunction(l.opts.start)&&l.opts.start.call(e,l),v.fx.timer(v.extend(s,{elem:e,anim:l,queue:l.opts.queue})),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always)}v.Animation=v.extend(Ot,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ee(n.elem,e,X.exec(t),n),n}]},tweener:function(e,t){v.isFunction(e)?(t=e,e=["*"]):e=e.match(_);for(var n,a=0,i=e.length;i>a;a++)n=e[a],Ot.tweeners[n]=Ot.tweeners[n]||[],Ot.tweeners[n].unshift(t)},prefilters:[jt],prefilter:function(e,t){t?Ot.prefilters.unshift(e):Ot.prefilters.push(e)}}),v.speed=function(e,t,n){var a=e&&"object"==o(e)?v.extend({},e):{complete:n||!n&&t||v.isFunction(e)&&e,duration:e,easing:n&&t||t&&!v.isFunction(t)&&t};return a.duration=v.fx.off?0:"number"==typeof a.duration?a.duration:a.duration in v.fx.speeds?v.fx.speeds[a.duration]:v.fx.speeds._default,null!=a.queue&&!0!==a.queue||(a.queue="fx"),a.old=a.complete,a.complete=function(){v.isFunction(a.old)&&a.old.call(this),a.queue&&v.dequeue(this,a.queue)},a},v.fn.extend({fadeTo:function(e,t,n,a){return this.filter(Z).css("opacity",0).show().end().animate({opacity:t},e,n,a)},animate:function(e,t,n,a){var i=v.isEmptyObject(e),o=v.speed(t,n,a),r=function(){var t=Ot(this,v.extend({},e),o);(i||v._data(this,"finish"))&&t.stop(!0)};return r.finish=r,i||!1===o.queue?this.each(r):this.queue(o.queue,r)},stop:function(e,t,n){var a=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each((function(){var t=!0,i=null!=e&&e+"queueHooks",o=v.timers,r=v._data(this);if(i)r[i]&&r[i].stop&&a(r[i]);else for(i in r)r[i]&&r[i].stop&&bt.test(i)&&a(r[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||v.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=v._data(this),a=n[e+"queue"],i=n[e+"queueHooks"],o=v.timers,r=a?a.length:0;for(n.finish=!0,v.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;r>t;t++)a[t]&&a[t].finish&&a[t].finish.call(this);delete n.finish}))}}),v.each(["toggle","show","hide"],(function(e,t){var n=v.fn[t];v.fn[t]=function(e,a,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(gt(t,!0),e,a,i)}})),v.each({slideDown:gt("show"),slideUp:gt("hide"),slideToggle:gt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){v.fn[e]=function(e,n,a){return this.animate(t,e,n,a)}})),v.timers=[],v.fx.tick=function(){var e,t=v.timers,n=0;for(pt=v.now();n<t.length;n++)e=t[n],e()||t[n]!==e||t.splice(n--,1);t.length||v.fx.stop(),pt=void 0},v.fx.timer=function(e){v.timers.push(e),e()?v.fx.start():v.timers.pop()},v.fx.interval=13,v.fx.start=function(){ht||(ht=n.setInterval(v.fx.tick,v.fx.interval))},v.fx.stop=function(){n.clearInterval(ht),ht=null},v.fx.speeds={slow:600,fast:200,_default:400},v.fn.delay=function(e,t){return e=v.fx&&v.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,a){var i=n.setTimeout(t,e);a.stop=function(){n.clearTimeout(i)}}))},function(){var e,t=s.createElement("input"),n=s.createElement("div"),a=s.createElement("select"),i=a.appendChild(s.createElement("option"));n=s.createElement("div"),n.setAttribute("className","t"),n.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",e=n.getElementsByTagName("a")[0],t.setAttribute("type","checkbox"),n.appendChild(t),e=n.getElementsByTagName("a")[0],e.style.cssText="top:1px",m.getSetAttribute="t"!==n.className,m.style=/top/.test(e.getAttribute("style")),m.hrefNormalized="/a"===e.getAttribute("href"),m.checkOn=!!t.value,m.optSelected=i.selected,m.enctype=!!s.createElement("form").enctype,a.disabled=!0,m.optDisabled=!i.disabled,t=s.createElement("input"),t.setAttribute("value",""),m.input=""===t.getAttribute("value"),t.value="t",t.setAttribute("type","radio"),m.radioValue="t"===t.value}();var kt=/\r/g,wt=/[\x20\t\r\n\f]+/g;v.fn.extend({val:function(e){var t,n,a,i=this[0];return arguments.length?(a=v.isFunction(e),this.each((function(n){var i;1===this.nodeType&&(i=a?e.call(this,n,v(this).val()):e,null==i?i="":"number"==typeof i?i+="":v.isArray(i)&&(i=v.map(i,(function(e){return null==e?"":e+""}))),t=v.valHooks[this.type]||v.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))}))):i?(t=v.valHooks[i.type]||v.valHooks[i.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:(n=i.value,"string"==typeof n?n.replace(kt,""):null==n?"":n)):void 0}}),v.extend({valHooks:{option:{get:function(e){var t=v.find.attr(e,"value");return null!=t?t:v.trim(v.text(e)).replace(wt," ")}},select:{get:function(e){for(var t,n,a=e.options,i=e.selectedIndex,o="select-one"===e.type||0>i,r=o?null:[],c=o?i+1:a.length,s=0>i?c:o?i:0;c>s;s++)if(n=a[s],(n.selected||s===i)&&(m.optDisabled?!n.disabled:null===n.getAttribute("disabled"))&&(!n.parentNode.disabled||!v.nodeName(n.parentNode,"optgroup"))){if(t=v(n).val(),o)return t;r.push(t)}return r},set:function(e,t){var n,a,i=e.options,o=v.makeArray(t),r=i.length;while(r--)if(a=i[r],v.inArray(v.valHooks.option.get(a),o)>-1)try{a.selected=n=!0}catch(p){a.scrollHeight}else a.selected=!1;return n||(e.selectedIndex=-1),i}}}}),v.each(["radio","checkbox"],(function(){v.valHooks[this]={set:function(e,t){return v.isArray(t)?e.checked=v.inArray(v(e).val(),t)>-1:void 0}},m.checkOn||(v.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}));var xt,Nt,Et=v.expr.attrHandle,It=/^(?:checked|selected)$/i,Ct=m.getSetAttribute,Dt=m.input;v.fn.extend({attr:function(e,t){return te(this,v.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){v.removeAttr(this,e)}))}}),v.extend({attr:function(e,t,n){var a,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return"undefined"==typeof e.getAttribute?v.prop(e,t,n):(1===o&&v.isXMLDoc(e)||(t=t.toLowerCase(),i=v.attrHooks[t]||(v.expr.match.bool.test(t)?Nt:xt)),void 0!==n?null===n?void v.removeAttr(e,t):i&&"set"in i&&void 0!==(a=i.set(e,n,t))?a:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(a=i.get(e,t))?a:(a=v.find.attr(e,t),null==a?void 0:a))},attrHooks:{type:{set:function(e,t){if(!m.radioValue&&"radio"===t&&v.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,a,i=0,o=t&&t.match(_);if(o&&1===e.nodeType)while(n=o[i++])a=v.propFix[n]||n,v.expr.match.bool.test(n)?Dt&&Ct||!It.test(n)?e[a]=!1:e[v.camelCase("default-"+n)]=e[a]=!1:v.attr(e,n,""),e.removeAttribute(Ct?n:a)}}),Nt={set:function(e,t,n){return!1===t?v.removeAttr(e,n):Dt&&Ct||!It.test(n)?e.setAttribute(!Ct&&v.propFix[n]||n,n):e[v.camelCase("default-"+n)]=e[n]=!0,n}},v.each(v.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=Et[t]||v.find.attr;Dt&&Ct||!It.test(t)?Et[t]=function(e,t,a){var i,o;return a||(o=Et[t],Et[t]=i,i=null!=n(e,t,a)?t.toLowerCase():null,Et[t]=o),i}:Et[t]=function(e,t,n){return n?void 0:e[v.camelCase("default-"+t)]?t.toLowerCase():null}})),Dt&&Ct||(v.attrHooks.value={set:function(e,t,n){return v.nodeName(e,"input")?void(e.defaultValue=t):xt&&xt.set(e,t,n)}}),Ct||(xt={set:function(e,t,n){var a=e.getAttributeNode(n);return a||e.setAttributeNode(a=e.ownerDocument.createAttribute(n)),a.value=t+="","value"===n||t===e.getAttribute(n)?t:void 0}},Et.id=Et.name=Et.coords=function(e,t,n){var a;return n?void 0:(a=e.getAttributeNode(t))&&""!==a.value?a.value:null},v.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);return n&&n.specified?n.value:void 0},set:xt.set},v.attrHooks.contenteditable={set:function(e,t,n){xt.set(e,""!==t&&t,n)}},v.each(["width","height"],(function(e,t){v.attrHooks[t]={set:function(e,n){return""===n?(e.setAttribute(t,"auto"),n):void 0}}}))),m.style||(v.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}});var St=/^(?:input|select|textarea|button|object)$/i,Tt=/^(?:a|area)$/i;v.fn.extend({prop:function(e,t){return te(this,v.prop,e,t,arguments.length>1)},removeProp:function(e){return e=v.propFix[e]||e,this.each((function(){try{this[e]=void 0,delete this[e]}catch(r){}}))}}),v.extend({prop:function(e,t,n){var a,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&v.isXMLDoc(e)||(t=v.propFix[t]||t,i=v.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(a=i.set(e,n,t))?a:e[t]=n:i&&"get"in i&&null!==(a=i.get(e,t))?a:e[t]},propHooks:{tabIndex:{get:function(e){var t=v.find.attr(e,"tabindex");return t?parseInt(t,10):St.test(e.nodeName)||Tt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.hrefNormalized||v.each(["href","src"],(function(e,t){v.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}})),m.optSelected||(v.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),v.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){v.propFix[this.toLowerCase()]=this})),m.enctype||(v.propFix.enctype="encoding");var At=/[\t\r\n\f]/g;function Vt(e){return v.attr(e,"class")||""}v.fn.extend({addClass:function(e){var t,n,a,i,o,r,c,s=0;if(v.isFunction(e))return this.each((function(t){v(this).addClass(e.call(this,t,Vt(this)))}));if("string"==typeof e&&e){t=e.match(_)||[];while(n=this[s++])if(i=Vt(n),a=1===n.nodeType&&(" "+i+" ").replace(At," ")){r=0;while(o=t[r++])a.indexOf(" "+o+" ")<0&&(a+=o+" ");c=v.trim(a),i!==c&&v.attr(n,"class",c)}}return this},removeClass:function(e){var t,n,a,i,o,r,c,s=0;if(v.isFunction(e))return this.each((function(t){v(this).removeClass(e.call(this,t,Vt(this)))}));if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e){t=e.match(_)||[];while(n=this[s++])if(i=Vt(n),a=1===n.nodeType&&(" "+i+" ").replace(At," ")){r=0;while(o=t[r++])while(a.indexOf(" "+o+" ")>-1)a=a.replace(" "+o+" "," ");c=v.trim(a),i!==c&&v.attr(n,"class",c)}}return this},toggleClass:function(e,t){var n=o(e);return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):v.isFunction(e)?this.each((function(n){v(this).toggleClass(e.call(this,n,Vt(this),t),t)})):this.each((function(){var t,a,i,o;if("string"===n){a=0,i=v(this),o=e.match(_)||[];while(t=o[a++])i.hasClass(t)?i.removeClass(t):i.addClass(t)}else void 0!==e&&"boolean"!==n||(t=Vt(this),t&&v._data(this,"__className__",t),v.attr(this,"class",t||!1===e?"":v._data(this,"__className__")||""))}))},hasClass:function(e){var t,n,a=0;t=" "+e+" ";while(n=this[a++])if(1===n.nodeType&&(" "+Vt(n)+" ").replace(At," ").indexOf(t)>-1)return!0;return!1}}),v.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),(function(e,t){v.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}})),v.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});var Bt=n.location,$t=v.now(),Lt=/\?/,Ut=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;v.parseJSON=function(e){if(n.JSON&&n.JSON.parse)return n.JSON.parse(e+"");var t,a=null,i=v.trim(e+"");return i&&!v.trim(i.replace(Ut,(function(e,n,i,o){return t&&n&&(a=0),0===a?e:(t=i||n,a+=!o-!i,"")})))?Function("return "+i)():v.error("Invalid JSON: "+e)},v.parseXML=function(e){var t,a;if(!e||"string"!=typeof e)return null;try{n.DOMParser?(a=new n.DOMParser,t=a.parseFromString(e,"text/xml")):(t=new n.ActiveXObject("Microsoft.XMLDOM"),t.async="false",t.loadXML(e))}catch(l){t=void 0}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||v.error("Invalid XML: "+e),t};var Ft=/#.*$/,_t=/([?&])_=[^&]*/,Rt=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Mt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Pt=/^(?:GET|HEAD)$/,qt=/^\/\//,zt=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Ht={},Wt={},Qt="*/".concat("*"),Yt=Bt.href,Gt=zt.exec(Yt.toLowerCase())||[];function Kt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var a,i=0,o=t.toLowerCase().match(_)||[];if(v.isFunction(n))while(a=o[i++])"+"===a.charAt(0)?(a=a.slice(1)||"*",(e[a]=e[a]||[]).unshift(n)):(e[a]=e[a]||[]).push(n)}}function Xt(e,t,n,a){var i={},o=e===Wt;function r(c){var s;return i[c]=!0,v.each(e[c]||[],(function(e,c){var l=c(t,n,a);return"string"!=typeof l||o||i[l]?o?!(s=l):void 0:(t.dataTypes.unshift(l),r(l),!1)})),s}return r(t.dataTypes[0])||!i["*"]&&r("*")}function Jt(e,t){var n,a,i=v.ajaxSettings.flatOptions||{};for(a in t)void 0!==t[a]&&((i[a]?e:n||(n={}))[a]=t[a]);return n&&v.extend(!0,e,n),e}function Zt(e,t,n){var a,i,o,r,c=e.contents,s=e.dataTypes;while("*"===s[0])s.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in c)if(c[r]&&c[r].test(i)){s.unshift(r);break}if(s[0]in n)o=s[0];else{for(r in n){if(!s[0]||e.converters[r+" "+s[0]]){o=r;break}a||(a=r)}o=o||a}return o?(o!==s[0]&&s.unshift(o),n[o]):void 0}function en(e,t,n,a){var i,o,r,c,s,l={},u=e.dataTypes.slice();if(u[1])for(r in e.converters)l[r.toLowerCase()]=e.converters[r];o=u.shift();while(o)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!s&&a&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),s=o,o=u.shift())if("*"===o)o=s;else if("*"!==s&&s!==o){if(r=l[s+" "+o]||l["* "+o],!r)for(i in l)if(c=i.split(" "),c[1]===o&&(r=l[s+" "+c[0]]||l["* "+c[0]])){!0===r?r=l[i]:!0!==l[i]&&(o=c[0],u.unshift(c[1]));break}if(!0!==r)if(r&&e["throws"])t=r(t);else try{t=r(t)}catch(m){return{state:"parsererror",error:r?m:"No conversion from "+s+" to "+o}}}return{state:"success",data:t}}function tn(e){return e.style&&e.style.display||v.css(e,"display")}function nn(e){while(e&&1===e.nodeType){if("none"===tn(e)||"hidden"===e.type)return!0;e=e.parentNode}return!1}v.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Yt,type:"GET",isLocal:Mt.test(Gt[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Qt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":v.parseJSON,"text xml":v.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Jt(Jt(e,v.ajaxSettings),t):Jt(v.ajaxSettings,e)},ajaxPrefilter:Kt(Ht),ajaxTransport:Kt(Wt),ajax:function(e,t){"object"==o(e)&&(t=e,e=void 0),t=t||{};var a,i,r,c,s,l,u,d,p=v.ajaxSetup({},t),h=p.context||p,f=p.context&&(h.nodeType||h.jquery)?v(h):v.event,b=v.Deferred(),m=v.Callbacks("once memory"),g=p.statusCode||{},j={},y={},O=0,k="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(2===O){if(!d){d={};while(t=Rt.exec(c))d[t[1].toLowerCase()]=t[2]}t=d[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===O?c:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return O||(e=y[n]=y[n]||e,j[e]=t),this},overrideMimeType:function(e){return O||(p.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>O)for(t in e)g[t]=[g[t],e[t]];else w.always(e[w.status]);return this},abort:function(e){var t=e||k;return u&&u.abort(t),x(0,t),this}};if(b.promise(w).complete=m.add,w.success=w.done,w.error=w.fail,p.url=((e||p.url||Yt)+"").replace(Ft,"").replace(qt,Gt[1]+"//"),p.type=t.method||t.type||p.method||p.type,p.dataTypes=v.trim(p.dataType||"*").toLowerCase().match(_)||[""],null==p.crossDomain&&(a=zt.exec(p.url.toLowerCase()),p.crossDomain=!(!a||a[1]===Gt[1]&&a[2]===Gt[2]&&(a[3]||("http:"===a[1]?"80":"443"))===(Gt[3]||("http:"===Gt[1]?"80":"443")))),p.data&&p.processData&&"string"!=typeof p.data&&(p.data=v.param(p.data,p.traditional)),Xt(Ht,p,t,w),2===O)return w;for(i in l=v.event&&p.global,l&&0===v.active++&&v.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Pt.test(p.type),r=p.url,p.hasContent||(p.data&&(r=p.url+=(Lt.test(r)?"&":"?")+p.data,delete p.data),!1===p.cache&&(p.url=_t.test(r)?r.replace(_t,"$1_="+$t++):r+(Lt.test(r)?"&":"?")+"_="+$t++)),p.ifModified&&(v.lastModified[r]&&w.setRequestHeader("If-Modified-Since",v.lastModified[r]),v.etag[r]&&w.setRequestHeader("If-None-Match",v.etag[r])),(p.data&&p.hasContent&&!1!==p.contentType||t.contentType)&&w.setRequestHeader("Content-Type",p.contentType),w.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+Qt+"; q=0.01":""):p.accepts["*"]),p.headers)w.setRequestHeader(i,p.headers[i]);if(p.beforeSend&&(!1===p.beforeSend.call(h,w,p)||2===O))return w.abort();for(i in k="abort",{success:1,error:1,complete:1})w[i](p[i]);if(u=Xt(Wt,p,t,w)){if(w.readyState=1,l&&f.trigger("ajaxSend",[w,p]),2===O)return w;p.async&&p.timeout>0&&(s=n.setTimeout((function(){w.abort("timeout")}),p.timeout));try{O=1,u.send(j,x)}catch(C){if(!(2>O))throw C;x(-1,C)}}else x(-1,"No Transport");function x(e,t,a,i){var o,d,j,y,k,x=t;2!==O&&(O=2,s&&n.clearTimeout(s),u=void 0,c=i||"",w.readyState=e>0?4:0,o=e>=200&&300>e||304===e,a&&(y=Zt(p,w,a)),y=en(p,y,w,o),o?(p.ifModified&&(k=w.getResponseHeader("Last-Modified"),k&&(v.lastModified[r]=k),k=w.getResponseHeader("etag"),k&&(v.etag[r]=k)),204===e||"HEAD"===p.type?x="nocontent":304===e?x="notmodified":(x=y.state,d=y.data,j=y.error,o=!j)):(j=x,!e&&x||(x="error",0>e&&(e=0))),w.status=e,w.statusText=(t||x)+"",o?b.resolveWith(h,[d,x,w]):b.rejectWith(h,[w,x,j]),w.statusCode(g),g=void 0,l&&f.trigger(o?"ajaxSuccess":"ajaxError",[w,p,o?d:j]),m.fireWith(h,[w,x]),l&&(f.trigger("ajaxComplete",[w,p]),--v.active||v.event.trigger("ajaxStop")))}return w},getJSON:function(e,t,n){return v.get(e,t,n,"json")},getScript:function(e,t){return v.get(e,void 0,t,"script")}}),v.each(["get","post"],(function(e,t){v[t]=function(e,n,a,i){return v.isFunction(n)&&(i=i||a,a=n,n=void 0),v.ajax(v.extend({url:e,type:t,dataType:i,data:n,success:a},v.isPlainObject(e)&&e))}})),v._evalUrl=function(e){return v.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},v.fn.extend({wrapAll:function(e){if(v.isFunction(e))return this.each((function(t){v(this).wrapAll(e.call(this,t))}));if(this[0]){var t=v(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){var e=this;while(e.firstChild&&1===e.firstChild.nodeType)e=e.firstChild;return e})).append(this)}return this},wrapInner:function(e){return v.isFunction(e)?this.each((function(t){v(this).wrapInner(e.call(this,t))})):this.each((function(){var t=v(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=v.isFunction(e);return this.each((function(n){v(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(){return this.parent().each((function(){v.nodeName(this,"body")||v(this).replaceWith(this.childNodes)})).end()}}),v.expr.filters.hidden=function(e){return m.reliableHiddenOffsets()?e.offsetWidth<=0&&e.offsetHeight<=0&&!e.getClientRects().length:nn(e)},v.expr.filters.visible=function(e){return!v.expr.filters.hidden(e)};var an=/%20/g,on=/\[\]$/,rn=/\r?\n/g,cn=/^(?:submit|button|image|reset|file)$/i,sn=/^(?:input|select|textarea|keygen)/i;function ln(e,t,n,a){var i;if(v.isArray(t))v.each(t,(function(t,i){n||on.test(e)?a(e,i):ln(e+"["+("object"==o(i)&&null!=i?t:"")+"]",i,n,a)}));else if(n||"object"!==v.type(t))a(e,t);else for(i in t)ln(e+"["+i+"]",t[i],n,a)}v.param=function(e,t){var n,a=[],i=function(e,t){t=v.isFunction(t)?t():null==t?"":t,a[a.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=v.ajaxSettings&&v.ajaxSettings.traditional),v.isArray(e)||e.jquery&&!v.isPlainObject(e))v.each(e,(function(){i(this.name,this.value)}));else for(n in e)ln(n,e[n],t,i);return a.join("&").replace(an,"+")},v.fn.extend({serialize:function(){return v.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=v.prop(this,"elements");return e?v.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!v(this).is(":disabled")&&sn.test(this.nodeName)&&!cn.test(e)&&(this.checked||!ne.test(e))})).map((function(e,t){var n=v(this).val();return null==n?null:v.isArray(n)?v.map(n,(function(e){return{name:t.name,value:e.replace(rn,"\r\n")}})):{name:t.name,value:n.replace(rn,"\r\n")}})).get()}}),v.ajaxSettings.xhr=void 0!==n.ActiveXObject?function(){return this.isLocal?fn():s.documentMode>8?hn():/^(get|post|head|put|delete|options)$/i.test(this.type)&&hn()||fn()}:hn;var un=0,dn={},pn=v.ajaxSettings.xhr();function hn(){try{return new n.XMLHttpRequest}catch(r){}}function fn(){try{return new n.ActiveXObject("Microsoft.XMLHTTP")}catch(r){}}n.attachEvent&&n.attachEvent("onunload",(function(){for(var e in dn)dn[e](void 0,!0)})),m.cors=!!pn&&"withCredentials"in pn,pn=m.ajax=!!pn,pn&&v.ajaxTransport((function(e){var t;if(!e.crossDomain||m.cors)return{send:function(a,i){var o,r=e.xhr(),c=++un;if(r.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(o in e.xhrFields)r[o]=e.xhrFields[o];for(o in e.mimeType&&r.overrideMimeType&&r.overrideMimeType(e.mimeType),e.crossDomain||a["X-Requested-With"]||(a["X-Requested-With"]="XMLHttpRequest"),a)void 0!==a[o]&&r.setRequestHeader(o,a[o]+"");r.send(e.hasContent&&e.data||null),t=function(n,a){var o,s,l;if(t&&(a||4===r.readyState))if(delete dn[c],t=void 0,r.onreadystatechange=v.noop,a)4!==r.readyState&&r.abort();else{l={},o=r.status,"string"==typeof r.responseText&&(l.text=r.responseText);try{s=r.statusText}catch(b){s=""}o||!e.isLocal||e.crossDomain?1223===o&&(o=204):o=l.text?200:404}l&&i(o,s,l,r.getAllResponseHeaders())},e.async?4===r.readyState?n.setTimeout(t):r.onreadystatechange=dn[c]=t:t()},abort:function(){t&&t(void 0,!0)}}})),v.ajaxPrefilter((function(e){e.crossDomain&&(e.contents.script=!1)})),v.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return v.globalEval(e),e}}}),v.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)})),v.ajaxTransport("script",(function(e){if(e.crossDomain){var t,n=s.head||v("head")[0]||s.documentElement;return{send:function(a,i){t=s.createElement("script"),t.async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||i(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}}));var bn=[],mn=/(=)\?(?=&|$)|\?\?/;v.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=bn.pop()||v.expando+"_"+$t++;return this[e]=!0,e}}),v.ajaxPrefilter("json jsonp",(function(e,t,a){var i,o,r,c=!1!==e.jsonp&&(mn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&mn.test(e.data)&&"data");return c||"jsonp"===e.dataTypes[0]?(i=e.jsonpCallback=v.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,c?e[c]=e[c].replace(mn,"$1"+i):!1!==e.jsonp&&(e.url+=(Lt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return r||v.error(i+" was not called"),r[0]},e.dataTypes[0]="json",o=n[i],n[i]=function(){r=arguments},a.always((function(){void 0===o?v(n).removeProp(i):n[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,bn.push(i)),r&&v.isFunction(o)&&o(r[0]),r=o=void 0})),"script"):void 0})),v.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||s;var a=C.exec(e),i=!n&&[];return a?[t.createElement(a[1])]:(a=fe([e],t,i),i&&i.length&&v(i).remove(),v.merge([],a.childNodes))};var gn=v.fn.load;function vn(e){return v.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}v.fn.load=function(e,t,n){if("string"!=typeof e&&gn)return gn.apply(this,arguments);var a,i,r,c=this,s=e.indexOf(" ");return s>-1&&(a=v.trim(e.slice(s,e.length)),e=e.slice(0,s)),v.isFunction(t)?(n=t,t=void 0):t&&"object"==o(t)&&(i="POST"),c.length>0&&v.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done((function(e){r=arguments,c.html(a?v("<div>").append(v.parseHTML(e)).find(a):e)})).always(n&&function(e,t){c.each((function(){n.apply(c,r||[e.responseText,t,e])}))}),this},v.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){v.fn[t]=function(e){return this.on(t,e)}})),v.expr.filters.animated=function(e){return v.grep(v.timers,(function(t){return e===t.elem})).length},v.offset={setOffset:function(e,t,n){var a,i,o,r,c,s,l,u=v.css(e,"position"),d=v(e),p={};"static"===u&&(e.style.position="relative"),c=d.offset(),o=v.css(e,"top"),s=v.css(e,"left"),l=("absolute"===u||"fixed"===u)&&v.inArray("auto",[o,s])>-1,l?(a=d.position(),r=a.top,i=a.left):(r=parseFloat(o)||0,i=parseFloat(s)||0),v.isFunction(t)&&(t=t.call(e,n,v.extend({},c))),null!=t.top&&(p.top=t.top-c.top+r),null!=t.left&&(p.left=t.left-c.left+i),"using"in t?t.using.call(e,p):d.css(p)}},v.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){v.offset.setOffset(this,e,t)}));var t,n,a={top:0,left:0},i=this[0],o=i&&i.ownerDocument;return o?(t=o.documentElement,v.contains(t,i)?("undefined"!=typeof i.getBoundingClientRect&&(a=i.getBoundingClientRect()),n=vn(o),{top:a.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),left:a.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):a):void 0},position:function(){if(this[0]){var e,t,n={top:0,left:0},a=this[0];return"fixed"===v.css(a,"position")?t=a.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),v.nodeName(e[0],"html")||(n=e.offset()),n.top+=v.css(e[0],"borderTopWidth",!0),n.left+=v.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-v.css(a,"marginTop",!0),left:t.left-n.left-v.css(a,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){var e=this.offsetParent;while(e&&!v.nodeName(e,"html")&&"static"===v.css(e,"position"))e=e.offsetParent;return e||Qe}))}}),v.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n=/Y/.test(t);v.fn[e]=function(a){return te(this,(function(e,a,i){var o=vn(e);return void 0===i?o?t in o?o[t]:o.document.documentElement[a]:e[a]:void(o?o.scrollTo(n?v(o).scrollLeft():i,n?i:v(o).scrollTop()):e[a]=i)}),e,a,arguments.length,null)}})),v.each(["top","left"],(function(e,t){v.cssHooks[t]=Xe(m.pixelPosition,(function(e,n){return n?(n=Ge(e,t),He.test(n)?v(e).position()[t]+"px":n):void 0}))})),v.each({Height:"height",Width:"width"},(function(e,t){v.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,a){v.fn[a]=function(a,i){var o=arguments.length&&(n||"boolean"!=typeof a),r=n||(!0===a||!0===i?"margin":"border");return te(this,(function(t,n,a){var i;return v.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(i=t.documentElement,Math.max(t.body["scroll"+e],i["scroll"+e],t.body["offset"+e],i["offset"+e],i["client"+e])):void 0===a?v.css(t,n,r):v.style(t,n,a,r)}),t,o?a:void 0,o,null)}}))})),v.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,a){return this.on(t,e,n,a)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),v.fn.size=function(){return this.length},v.fn.andSelf=v.fn.addBack,a=[],i=function(){return v}.apply(t,a),void 0===i||(e.exports=i);var jn=n.jQuery,yn=n.$;return v.noConflict=function(e){return n.$===v&&(n.$=yn),e&&n.jQuery===v&&(n.jQuery=jn),v},r||(n.jQuery=n.$=v),v}))}).call(this,n("62e4")(e))},f2dd:function(e,t,n){},f2fe:function(e,t,n){"use strict";n("c7dc")},f3c3:function(e,t,n){},f501:function(e,t,n){"use strict";n("4549")},f5b1:function(e,t,n){"use strict";n("87e4")},f6e8:function(e,t,n){"use strict";n("96e9")},f86c:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAABdUlEQVQ4T+2UwVHCYBCF3/6MepQSDBZgrIBcCBwxWAAdSAekBOyAAgQ5QjgYO8ACIJQARw/8z9lgMogho1fHHP/sfruz7+0KSr6k3XBp5AWQjdj3W2cSbzQ8aXtVyNkNiK0zmS8yhJTBVh1/SAWBVVIIcCMCDxAX5CsFrtnRy4CFsH3lizoNeiBdiFTTZCA2xsTO0zTWrm1F+iDersdRqE3lMAVYc97/UvkoGRVTt6AnTLvbQhjLjoNvnS0DPxTAE2PCrHJhMhnDVmJnMl0fjyjvLIWJ1KlzOaxcknwSlrRbV6zYUH6RfBKW/VAFFeiM58NS29y3PNJ2a6Ooe9Iaq8CPVTVVSBXT4abqAlCf5W8KszasjSPvR7BVp8naaCY6T03QAtlb8g/LhTsU4C/PTM8OsU5tEPiL2jhyl0Gzt7fGbKBjUG8lQaNLEa/UtJ9BD7Izd0XLvD+O6eo9C/l4uCmF9yw1qaAnkMuilSK4BTHI7lgW8wGhC4Qjdt2TBQAAAABJRU5ErkJggg=="},fb7a:function(e,t,n){},fbc1:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAACNUlEQVQ4T61VPWhTURT+vntfUlORTsaAfyguOhS1xoIu1VpEh0iVQnSwi5MguIg4pU4iLoLg5KJDLQQFHRSp1VKoUkLRSRdRrApJnUoxSZN375H3kmeT+FCUd6d73znne+ec79zvEqErp/ozx9bXbDmllbNTadXluYk1VSN8H0O8NPf46Xfgqu0MZ+eHviOTPXatkyb0WSU4CKCbpO8nIgKgbIlZgbmnfriF+edDS60YbYD9J19uqrv6ggKyIFOExMMKELAmIkUBJmKOuTX38NDXwO8XYPrEs81Gusc0ZQTAuhagugBF70wgBSDWYls2InnN6ljh0dEvTR+gb2SyB7U1V5TIebANDCJYANwzDRBnnMSWtqwFy5a8jXj12nx+aInIidr9ZmrQQfzOb85+3/CRlIFGDzlNYntnG7yfuqide7tncIq9w7PJmLE3FOV0RzmNOMEnrcxhb2usfgFiW0hf61Z4v67VJe7NzPQq4gGBHaEECBaUNDK09DNsL7kZJMAHKzjFfZmZLMmbgGwA4BNAgdvC2jdhxe8hJTEuwMbAJoSzShRLInKR+4dfj4p1r3uAAn621s3GdYNVv+IVuIlK41xJIMUuOIGtZpBSypkgZCvAEpVzOXrAyEuOnJTIxybywfYYi/bqNWfg/8WBec1yuzgEc/Uv8gWRov2TfAWggcBq0aMADoQJLIBXhubuXwV29S7nVPr4QNI4Oqk0dinE/CfAor5iDd5p1ywWnkwvhj0BPwFGvWNKUEwe0AAAAABJRU5ErkJggg=="},fc10:function(e,t,n){"use strict";n("b83d")},fd4b:function(e,t,n){},fe73:function(e,t,n){"use strict";n("b917")}});
//# sourceMappingURL=app.2784294d.js.map