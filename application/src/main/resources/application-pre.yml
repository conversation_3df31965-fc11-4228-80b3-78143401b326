spring:
  application:
    name: plyh-schedule-server
  datasource:
    master:
      password: Microvideo@2023
      username: plyh
      jdbc-url: ***************************************************************************************************************************
      driver-class-name: com.mysql.cj.jdbc.Driver
    slave1:
      password: Microvideo@2023
      username: plyh
      jdbc-url: ***************************************************************************************************************************
      driver-class-name: com.mysql.cj.jdbc.Driver
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        #关闭注册中心服务
        enabled: false

#minio的配置
minio:
  endpoint: http://************:9000
  accessKey: admin
  secretKey: Microvideo@2023
  bucketName: plyh-server


idocview:
  url: http://************:8090

#cas配置
cas:
  server-url-prefix: https://oatest.plyh.gx.cn/MicrovideoCAS
  server-login-url: https://oatest.plyh.gx.cn/MicrovideoCAS/login
  client-host-url: https://oatest.plyh.gx.cn/ # cas服务器通过这个关联ticket信息，需要保证后续请求cas服务器的host都是这个
  validation-type: CAS

microvideo:
  qs:
    url: http://************:8080/Qsecurity/
    secretKey: MicroVideo2003
  cas:
    sessionKey: microvideo.jchc-invite #session key
    #拦截器放行配置
    interceptorsIgnoreUrl: /jchcfile/upload|/jchcfile/uploadFile|/jchcfile/download/*|/jchcfile/downloadByCode/*|/js/*|/css/*|/img/*|/doc.html|/swagger-*/**|/webjars/**|/v3/*|/login|/wechat/login|/login|/favicon.ico|/index.html|/holiday/exportDutyExcel|/holiday/api/*
    #忽略配置
    serverIgnoreUrl: /jchcfile/upload|/jchcfile/uploadFile|/jchcfile/download/*|/jchcfile/downloadByCode/*|/static/*|/css/*|/img/*|/js/*|/checkUser/*|/logout/*|/wechat/*|/oauth2|/doc.html|/v3/*|/swagger-*/*|/mobile.html|/holiday/exportDutyExcel|/holiday/api/*
    serverLogoutUrl: https://oatest.plyh.gx.cn/plyh-schedule-server/MicrovideoCAS/logout #cas服务登出配置
    serverHtmlUrl: https://oatest.plyh.gx.cn/schedule/index.html  #服务跳转地址前端地址
    baseServerUrl: https://oatest.plyh.gx.cn/plyh-schedule-server/login #服务跳转地址
  common:
    redis:
      cluster:
        nodes:
          - { host: ************,port: 8001,type: MASTER }
          - { host: ************,port: 8002,type: MASTER }
          - { host: ************,port: 8003,type: MASTER }
          - { host: ************,port: 8004,type: MASTER }
          - { host: ************,port: 8005,type: MASTER }
          - { host: ************,port: 8006,type: MASTER }
        password: plyh-microvideo-2024
      pool:
        enabled: true
        maxWaitMillis: 3000 #最大建立 连接等待时间。如果超过此时间将接到异常。设为-1表示无限制
        maxTotal: 50 #最大连接数
        maxActive: 50 #最大激活连接数
        maxIdle: 10  #最大空闲连接数

logging:
  level:
    cn.microvideo.module.plyh.core.mapper.*: debug
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false #关闭驼峰

#bpm流程调用接口地址
bpm:
  server:
    url: http://************:8091/plyh-server/business/bpm

#门户的待办的服务地址
portal:
  server:
    url: http://************:8091/plyh-server/noticepublicity/api/
    noticeUrl: https://oatest.plyh.gx.cn/schedule/index.html#/publish?id=%s

#第三方对接服务地址
docking:
  services:
    #考勤系统服务地址
    checkService: http://************:8092/check-server/monthholidaydetails/addWithUpdateBatchEntity

plyh:
  log:
    server:
      url: http://************:8080/plyh-log/msg/doSend

