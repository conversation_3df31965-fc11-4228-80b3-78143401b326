<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suppressions PUBLIC
"-//Checkstyle//DTD SuppressionXpathFilter Experimental Configuration 1.2//EN"
"https://checkstyle.org/dtds/suppressions_1_2_xpath_experimental.dtd">

<suppressions>
    <suppress id="javadocForPropertyFields" files=".*(?&lt;!Properties\.java)$" />
    <suppress id="useTestConfiguration" files=".*src/main/java.*" />
    <suppress id="stackTraceConsoleLogs" files="(.*Tests*|Mock.*|Test.*)\.java" />
    <suppress id="objectProvider" files="(.*Tests*|Mock.*|Test.*)\.java" />
    <suppress id="forbiddenAnnotations" files="(.*Tests*|Mock.*|Test.*|.*TestConfiguration.*)\.java" />
    <suppress id="collectionsUnmodifiable" files="(.*Tests*|Mock.*|Test.*)\.java" />
    <suppress id="collectionsEmpty" files="(.*Tests*|Mock.*|Test.*)\.java" />
    <suppress id="methodLength" files="(.*Tests*|Mock.*|Test.*)\.java" />
    <suppress id="sysOutConsoleLogs" files="\.xml" />
    <suppress id="secureRandom" files="RandomUtils\.java" />
    <suppress id="namedAnnotations" files="(Base.*|Abstract.*|Root.*)\.java" />
    <suppress id="finalNonUtilClass" files="(.*(Utils|Constants|Facade|Support|Beans|Tickets))\.java" />
    <suppress id="useLombokVar" files="(.*ConfigurationMetadataGenerator|.*ConfigurationMetadataHint|.*RelaxedPropertyNames)\.java" />
    <suppress id="usingAutowiredInApiClasses" files="(.*Command|.*Configuration|.*WebApplication|.*Controller|.*Tests*|Mock.*|Test.*)\.java" />
    <suppress id="useLombokValForFinalVar" files="(.*ConfigurationMetadataGenerator|.*ConfigurationMetadataHint|.*RelaxedPropertyNames)\.java" />
    <suppress id="useLombokVarForGenerics" files="(.*ConfigurationMetadataGenerator|.*ConfigurationMetadataHint|.*RelaxedPropertyNames)\.java" />

    <suppress checks="JavadocVariable" files="(.*Tests*|Mock.*|Test.*)\.java"/>
    <suppress checks="IllegalCatch" files="(.*Aspect)\.java"/>
    <suppress checks="JavadocStyleCheck" files="(.*Tests*|Mock.*|Test.*)\.java"/>
    <suppress checks="Indentation" files="(.*Tests*|Mock.*|Test.*)\.java"/>
    <suppress checks="JavadocMethod" files="(.*Configuration|.*Tests*|Mock.*|Test.*)\.java"/>
    <suppress checks="JavadocType" files="(.*Tests*|Mock.*|Test.*|.*Properties)\.java"/>
    <suppress checks="JavadocVariable" files="(.*Tests*|Mock.*|Test.*)\.java"/>
    <suppress checks="MagicNumber" files="(.*Tests*|Mock.*|Test.*)\.java"/>
    <suppress checks="DesignForExtension" files="(.*Tests*|Mock.*|Test.*)\.java"/>
    <suppress checks="InterfaceIsType"  files=".*Constants\.java"/>
    <suppress checks="\w+" files="(\.(crt|crl|class|keystore))|rebel.xml"/>
    <suppress checks="." files="com[\\/]duosecurity[\\/]duoweb[\\/]"/>
    <suppress checks="." files="org[\\/]apereo[\\/]cas[\\/]authentication[\\/]soap[\\/]"/>
</suppressions>
