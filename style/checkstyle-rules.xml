<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
        "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">
<module name="Checker">
    <property name="severity" value="warning"/>
    <property name="charset" value="UTF-8"/>

    <module name="LineLength">
        <property name="max" value="190"/>
        <property name="tabWidth" value="4"/>
        <property name="severity" value="error"/>
    </module>

    <!-- TreeWalker module checks -->
    <module name="TreeWalker">
        <property name="tabWidth" value="4"/>
        <module name="SuppressionCommentFilter"/>

        <module name="JavadocContentLocationCheck">
            <property name="severity" value="error"/>
            <property name="location" value="SECOND_LINE" />
        </module>
        <module name="NoEnumTrailingComma">
            <property name="severity" value="error"/>
        </module>
        <module name="AvoidNoArgumentSuperConstructorCall">
            <property name="severity" value="error"/>
        </module>
        <module name="UnnecessarySemicolonAfterTypeMemberDeclaration">
            <property name="severity" value="error"/>
        </module>
        <module name="NonEmptyAtclauseDescription">
            <property name="severity" value="error"/>
        </module>
        <module name="LambdaParameterName"/>
        <module name="HiddenField">
            <property name="tokens" value="VARIABLE_DEF" />
            <property name="ignoreSetter" value="true"/>
            <property name="severity" value="error"/>
            <property name="ignoreConstructorParameter" value="true"/>
        </module>
        <module name="JavadocMethod">
            <property name="scope" value="protected"/>
            <property name="severity" value="error"/>
            <property name="tokens" value="METHOD_DEF, ANNOTATION_FIELD_DEF"/>
        </module>
        <module name="JavadocType">
            <property name="severity" value="error"/>
            <property name="scope" value="public"/>
            <property name="authorFormat" value="\w+"/>
            <property name="allowMissingParamTags" value="true"/>
        </module>
        <module name="JavadocVariable">
            <property name="id" value="javadocForAllProtectedAndPublicFields"/>
            <property name="severity" value="error"/>
            <property name="scope" value="protected"/>
            <property name="ignoreNamePattern" value="log|logger|LOG|LOGGER"/>
        </module>
        <module name="JavadocVariable">
            <property name="id" value="javadocForPropertyFields"/>
            <property name="severity" value="error"/>
            <property name="ignoreNamePattern" value="log|logger|LOG|LOGGER"/>
        </module>
        <module name="JavadocStyle">
            <property name="severity" value="error"/>
        </module>
        <module name="EmptyCatchBlock">
            <property name="severity" value="error"/>
        </module>
        <module name="ClassFanOutComplexity">
            <property name="max" value="120"/>
            <property name="severity" value="error"/>
        </module>
        <module name="CommentsIndentation">
            <property name="severity" value="error"/>
        </module>
        <module name="CyclomaticComplexity">
            <property name="max" value="30"/>
            <property name="severity" value="info"/>
        </module>
        <module name="DefaultComesLast">
            <property name="severity" value="error"/>
        </module>
        <!--
        <module name="MissingCtorCheck">
            <property name="severity" value="error"/>
        </module>
        -->
        <module name="AnnotationLocation">
            <property name="severity" value="error"/>
            <property name="allowSamelineSingleParameterlessAnnotation" value="true"/>
        </module>
        <module name="ConstantName">
            <property name="severity" value="error"/>
        </module>
        <module name="GenericWhitespaceCheck">
            <property name="severity" value="error"/>
        </module>
        <module name="ModifiedControlVariable">
            <property name="severity" value="error"/>
        </module>
        <module name="MagicNumber">
            <property name="ignoreAnnotation" value="true"/>
            <property name="ignoreHashCodeMethod" value="true"/>
            <property name="ignoreFieldDeclaration" value="true"/>
            <property name="severity" value="error"/>
            <property name="ignoreNumbers" value="-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 100, 1000"/>
        </module>
        <module name="LocalVariableName">
            <property name="severity" value="error"/>
        </module>
        <module name="AbstractClassName">
            <property name="ignoreModifier" value="true"/>
            <property name="severity" value="error"/>
            <property name="format" value="^Abstract.*$|^.*Factory$|^Base.*$|^Root.*$"/>
        </module>
        <module name="MemberName">
            <property name="severity" value="error"/>
        </module>
        <module name="MethodName">
            <property name="severity" value="error"/>
        </module>
        <module name="GenericWhitespace">
            <property name="severity" value="error"/>
        </module>
        <module name="PackageName">
            <property name="severity" value="error"/>
        </module>
        <module name="UnnecessaryParentheses">
            <property name="severity" value="error"/>
        </module>
        <module name="ParameterName">
            <property name="severity" value="error"/>
            <property name="ignoreOverridden" value="true"/>
        </module>
        <module name="StaticVariableName">
            <property name="severity" value="error"/>
            <property name="format" value="(^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$)"/>
        </module>
        <module name="SingleSpaceSeparator">
            <property name="validateComments" value="true"/>
            <property name="severity" value="error"/>
        </module>
        <module name="AbbreviationAsWordInName">
            <property name="allowedAbbreviationLength" value="5"/>
        </module>
        <module name="TypeName">
            <property name="severity" value="error"/>
        </module>

        <module name="AvoidStarImport">
            <property name="severity" value="error"/>
            <property name="excludes"
                      value="java.io,
                      java.net,
                      java.lang.Math,
                      org.junit.jupiter.api.Assertions,
                      org.junit.jupiter.api.Assumptions,
                      org.mockito.Mockito,
                      org.mockito.Matchers,
                      org.springframework.test.web.servlet.request.MockMvcRequestBuilders,
                      org.springframework.test.web.servlet.result.MockMvcResultMatchers,
                      org.springframework.test.web.client.ExpectedCount,
                      org.springframework.test.web.client.match.MockRestRequestMatchers,
                      org.springframework.test.web.client.response.MockRestResponseCreators,
                      java.nio.file.StandardWatchEventKinds"/>
            <property name="allowStaticMemberImports" value="true"/>
        </module>
        <module name="SingleLineJavadoc">
            <property name="severity" value="warning"/>
        </module>
        <module name="IllegalThrows">
            <property name="illegalClassNames" value="java.lang.Error,java.lang.RuntimeException"/>
            <property name="severity" value="error"/>
        </module>
        <module name="IllegalCatch">
            <property name="illegalClassNames" value="java.lang.Error,java.lang.RuntimeException"/>
            <property name="severity" value="error"/>
        </module>
        <module name="IllegalImport">
            <property name="severity" value="error"/>
        </module>
        <module name="RedundantImport">
            <property name="severity" value="error"/>
        </module>

        <!--
        This check insofar is unable to ignore constants and logger fields.
        <module name="RequireThis">
            <property name="severity" value="error"/>
            <property name="checkMethods" value="false" />
            <property name="validateOnlyOverlapping" value="false" />
        </module>
        -->

        <module name="UnusedImports">
            <property name="severity" value="error"/>
        </module>
        <module name="UnnecessaryParentheses">
            <property name="severity" value="error"/>
        </module>
        <module name="SuperClone">
            <property name="severity" value="info"/>
        </module>
        <module name="SuperFinalize">
            <property name="severity" value="error"/>
        </module>
        <module name="MethodLength">
            <property name="id" value="methodLength"/>
            <property name="max" value="180"/>
        </module>
        <module name="ParameterNumber">
            <property name="max" value="20"/>
            <property name="severity" value="error"/>
            <property name="tokens" value="METHOD_DEF"/>
        </module>
        <module name="ParameterNumber">
            <property name="max" value="15"/>
            <property name="severity" value="error"/>
            <property name="tokens" value="CTOR_DEF"/>
        </module>
        <module name="EmptyForIteratorPad"/>
        <module name="MethodParamPad">
            <property name="severity" value="error"/>
        </module>
        <module name="NoWhitespaceAfter">
            <property name="severity" value="error"/>
        </module>
        <module name="NoWhitespaceBefore">
            <property name="severity" value="error"/>
        </module>
        <module name="OperatorWrap">
            <property name="severity" value="error"/>
        </module>
        <module name="ParenPad">
            <property name="severity" value="error"/>
        </module>
        <module name="TypecastParenPad"/>
        <module name="WhitespaceAfter">
            <property name="severity" value="error"/>
        </module>
        <module name="ModifierOrder">
            <property name="severity" value="error"/>
        </module>
        <module name="RedundantModifier">
            <property name="severity" value="error"/>
        </module>
        <module name="AvoidNestedBlocks"/>
        <module name="EmptyBlock">
            <property name="severity" value="error"/>
        </module>
        <module name="FallThrough">
            <property name="severity" value="error"/>
        </module>
<!--        <module name="DeclarationOrder">-->
<!--            <property name="severity" value="error"/>-->
<!--        </module>-->
        <module name="CovariantEquals">
            <property name="severity" value="error"/>
        </module>
        <module name="ExplicitInitialization">
            <property name="severity" value="error"/>
        </module>
        <module name="LeftCurly">
            <property name="severity" value="error"/>
        </module>
        <module name="NeedBraces">
            <property name="severity" value="error"/>
        </module>
        <module name="EqualsAvoidNull">
            <property name="severity" value="error"/>
        </module>
        <module name="MultipleVariableDeclarations">
            <property name="severity" value="error"/>
        </module>
        <module name="OneTopLevelClass">
            <property name="severity" value="error"/>
        </module>
        <module name="StringLiteralEquality">
            <property name="severity" value="error"/>
        </module>
        <module name="Indentation">
            <property name="forceStrictCondition" value="false" />
            <property name="severity" value="error"/>
        </module>
        <!--
        <module name="MissingCtor">
            <property name="severity" value="error" />
        </module>
        -->

        <module name="OuterTypeFilename">
            <property name="severity" value="error"/>
        </module>
        <module name="OuterTypeNumber">
            <property name="severity" value="error"/>
        </module>
        <module name="AnnotationUseStyle">
            <property name="elementStyle" value="ignore"/>
            <property name="severity" value="error"/>
        </module>
        <module name="CatchParameterName">
            <property name="severity" value="error"/>
        </module>
        <module name="AnonInnerLength">
            <property name="max" value="40"/>
            <property name="severity" value="error"/>
        </module>
        <module name="RightCurly">
            <property name="severity" value="error"/>
        </module>
        <module name="NoFinalizer">
            <property name="severity" value="error"/>
        </module>
        <module name="EmptyStatement"/>
        <module name="EqualsHashCode">
            <property name="severity" value="info"/>
        </module>
        <module name="IllegalInstantiation"/>
        <module name="InnerAssignment"/>
        <module name="MissingSwitchDefault">
            <property name="severity" value="info"/>
        </module>
        <module name="SimplifyBooleanExpression">
            <property name="severity" value="error"/>
        </module>
        <module name="SimplifyBooleanReturn">
            <property name="severity" value="error"/>
        </module>
        <module name="FinalClass">
            <property name="severity" value="error"/>
        </module>
        <!--
        <module name="HideUtilityClassConstructor">
            <property name="severity" value="error"/>
        </module>
        <module name="InterfaceIsType">
            <property name="severity" value="info"/>
        </module>
        <module name="MultipleStringLiterals">
            <property name="severity" value="info"/>
            <property name="ignoreStringsRegexp"
                      value="(^&quot;&quot;$)|(^&quot;(no|false|\/|true|//|yes|default|\.|,|=|&amp;|\?|UTF-8|\*|\\|//)&quot;$)" />
        </module>
         -->

        <module name="VisibilityModifier">
            <property name="severity" value="error"/>
            <property name="protectedAllowed" value="true"/>
        </module>
        <module name="AtclauseOrder">
            <property name="severity" value="error"/>
        </module>
        <module name="BooleanExpressionComplexity">
            <property name="severity" value="error"/>
            <property name="max" value="4"/>
        </module>
        <module name="ArrayTypeStyle">
            <property name="severity" value="error"/>
        </module>
<!--        <module name="FinalParameters">-->
<!--            <property name="severity" value="error"/>-->
<!--            <property name="tokens" value="METHOD_DEF,CTOR_DEF,LITERAL_CATCH"/>-->
<!--        </module>-->
        <module name="FinalLocalVariable">
            <property name="severity" value="error"/>
            <property name="tokens" value="PARAMETER_DEF"/>
            <property name="validateEnhancedForLoopVariable" value="true"/>
        </module>
        <module name="TodoComment">
            <property name="severity" value="warning"/>
        </module>
        <module name="MutableException">
            <property name="severity" value="error"/>
        </module>
        <module name="UpperEll"/>

        <module name="OverloadMethodsDeclarationOrder">
            <property name="severity" value="error"/>
        </module>
        <module name="MissingOverride">
            <property name="severity" value="error"/>
        </module>
        <module name="MissingDeprecated">
            <property name="severity" value="error"/>
        </module>

        <!-- Custom checks based on regular expressions -->
        <module name="RegexpSinglelineJava">
            <property name="id" value="secureRandom"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Do not create a standalone instance of SecureRandom"/>
            <property name="severity" value="error"/>
            <property name="format" value="new SecureRandom\(.*\)"/>
            <property name="message"
                      value="Do not create a standalone instance of SecureRandom. Use RandomUtils instead."/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="id" value="logParameters"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Log parameters must be enclosed between brackets"/>
            <property name="severity" value="error"/>
            <property name="format" value="(LOGGER|log|logger)+\.\w+\(&quot;.+((?&lt;!\[)\{|(\}(?!s)(?!\]))).+&quot;"/>
            <property name="message" value="Log parameters must be enclosed between brackets"/>
        </module>

        <module name="RegexpSinglelineJava">
            <property name="id" value="externalProperties"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Consider using inline properties instead"/>
            <property name="severity" value="error"/>
            <property name="format" value="@TestPropertySource\(locations.*"/>
            <property name="message" value="Consider using inline properties instead using the properties attribute"/>
        </module>

        <module name="RegexpSinglelineJava">
            <property name="id" value="codeTagJavadocs"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Consider using {@code} Javadoc instead"/>
            <property name="severity" value="error"/>
            <property name="format" value="&lt;code&gt;"/>
            <property name="message" value="Consider using '{'@code'}' tag when wrapping code elements in Javadocs"/>
        </module>

        <module name="RegexpSinglelineJava">
            <property name="id" value="tests-suite-classname"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Name the test suite class consistently to match the CAS codebase"/>
            <property name="severity" value="error"/>
            <property name="format" value="public\sclass\s\w+TestSuite\s*\{"/>
            <property name="message" value="Test suite class names should end with TestSuite (i.e. AllAuthenticationTestsSuite)"/>
        </module>


        <module name="RegexpSinglelineJava">
            <property name="id" value="zero-length-array"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Using a zero-length array is not recommended."/>
            <property name="severity" value="error"/>
            <property name="format" value="new\s+(String|Object|byte|int|long|double|float|Class|char|boolean)\[(\d*)*\]\s*\{\}"/>
            <property name="message" value="Use constants in org.apache.commons.lang3.ArrayUtils instead of declaring zero-length arrays"/>
        </module>

        <module name="RegexpSinglelineJava">
            <property name="id" value="streamed-foreach-loop"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Using stream().forEach() is unnecessary where forEach() can be used directly."/>
            <property name="severity" value="error"/>
            <property name="format" value="\.stream\(\)\.forEach\("/>
            <property name="message" value="Simplify the Stream API. Using stream().forEach() is unnecessary where forEach() can be used directly."/>
        </module>

        <module name="RegexpSinglelineJava">
            <property name="id" value="collectionsEmpty"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Avoid using an empty immutable collection"/>
            <property name="severity" value="error"/>
            <property name="format" value="Collections\.empty(List|Map|Set)"/>
            <property name="message"
                      value="Returning an empty immutable collection is specially problematic for serialization tasks. Try using an empty collection of size zero instead"/>
        </module>

        <module name="RegexpSinglelineJava">
            <property name="id" value="collectionsUnmodifiable"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Avoid using an unmodifiable immutable collection"/>
            <property name="severity" value="error"/>
            <property name="format" value="Collections\.unmodifiable(List|Set|Map)"/>
            <property name="message"
                      value="Returning an unmodifiable collection is problematic for serialization. Try using an empty collection, of size zero, instead"/>
        </module>

        <module name="RegexpSinglelineJava">
            <property name="id" value="namedAnnotations"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Component/Controller/Configuration annotations must be named"/>
            <property name="severity" value="error"/>
            <property name="format" value="@(Configuration|Component|Controller)(\(\))*$"/>
            <property name="message"
                      value="You should name controller/component/configuration annotations to avoid implicit configuration"/>
        </module>

        <module name="RegexpSinglelineJava">
            <property name="id" value="singlelineComment"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Convert single-line comments into Javadocs"/>
            <property name="severity" value="error"/>
            <property name="format" value="^\s*\/\/(?!CHECKSTYLE)\s*.*"/>
            <property name="message" value="Avoid single-line comments. Turn notes and explanations into public javadocs. "/>
        </module>

        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="Parameteric log messages"/>
            <property name="severity" value="error"/>
            <property name="format" value="log\.\w+\(((\&quot;.+\&quot;\s*\+)|(.*\s*\+\s*\&quot;))"/>
            <property name="message"
                      value="Avoid string concatenation for constructing log messages. Use parametric messages instead"/>
        </module>

        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="Empty String constants"/>
            <property name="severity" value="error"/>
            <property name="format" value="(?&lt;!\\)&quot;(?&lt;!\\)&quot;"/>
            <property name="message" value="Consider using MicrovideoStringUtil.EMPTY instead"/>
        </module>


        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="JavaDoc @version tag"/>
            <property name="severity" value="error"/>
            <property name="format" value="@version\s+(.+)*(\$Revision|\$Date)"/>
            <property name="message" value="Invalid JavaDoc @version tag."/>
        </module>
        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="Logging framework selection"/>
            <property name="severity" value="error"/>
            <property name="format" value="LogFactory\.getLog"/>
            <property name="message" value="Microvideo uses the slf4j logging framework."/>
        </module>
        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="Logger as non-static"/>
            <property name="severity" value="error"/>
            <property name="format" value="(protected|public)\s+(final\s)*Logger*="/>
            <property name="message" value="Non-static logger must be declared as static to avoid serialization issues."/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="id" value="sysOutConsoleLogs"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Console output messages"/>
            <property name="severity" value="error"/>
            <property name="format" value="System\.(out|err)"/>
            <property name="message"
                      value="Avoid sending messages to the console directly. Use a logger object instead"/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="id" value="stackTraceConsoleLogs"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Printing stack traces to the console"/>
            <property name="severity" value="error"/>
            <property name="format" value="\.printStackTrace\(\)"/>
            <property name="message"
                      value="Avoid sending stack traces to the console directly. Use a logger object instead"/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="id" value="junitTestMethodName"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Using 'test' prefix for JUnit Tests"/>
            <property name="severity" value="error"/>
            <property name="format" value="(public|protected)\s+void\s+test\w+\(.+\{$"/>
            <property name="message"
                      value="JUnit test methods should not begin with the 'test' prefix. Use annotations instead and/or rename the method"/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="id" value="commonslang3"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Force using commons-lang v3"/>
            <property name="severity" value="error"/>
            <property name="format" value="org\.apache\.commons\.lang\."/>
            <property name="message" value="Use 'org.apache.commons.lang3' instead."/>
        </module>
        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="The logger object must be named &quot;logger&quot;"/>
            <property name="severity" value="error"/>
            <property name="format" value="(private|public|protected)\s+(static\s)*(final\s)*(static\s)*Logger\s+(log|LOG|LOGGER|logger)\b"/>
            <property name="message" value="Use the Lombok @Slf4j annotation to obtain a logger object instead."/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="id" value="finalNonUtilClass"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="final non-utility classes"/>
            <property name="severity" value="error"/>
            <property name="format" value="(public\s)*(static\s)*final\s+class\s+\w+"/>
            <property name="message" value="Non-utility class should not be marked as final"/>
        </module>
        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="Qualifying logger with the &quot;this&quot; keyword"/>
            <property name="severity" value="error"/>
            <property name="format" value="((this\.logger)|(super\.logger))\.\w+\("/>
            <property name="message" value="The Logger object need not be qualified with the &quot;this&quot; keyword"/>
        </module>
        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="Using @Test instead of extending TestCase"/>
            <property name="severity" value="error"/>
            <property name="format" value="class\s+\w+\s+extends\s+(junit\.framework\.)*TestCase"/>
            <property name="message"
                      value="All testcase must use annotations (@Test) instead of extending junit.framework.TestCase"/>
        </module>
        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="Using the junit.framework package"/>
            <property name="severity" value="error"/>
            <property name="format" value="junit.framework"/>
            <property name="message" value="The package junit.framework belongs to JUnit v3. Use org.junit instead."/>
        </module>
        <module name="RegexpSinglelineJava">
            <metadata name="net.sf.eclipsecs.core.comment" value="Checking for logging level"/>
            <property name="severity" value="error"/>
            <property name="format" value="log\.is\w+Enabled\("/>
            <property name="message"
                      value="Consider not checking for logging levels. Let the logging framework handle that."/>
        </module>
<!--        <module name="RegexpSinglelineJava">-->
<!--            <property name="id" value="usingAutowiredInApiClasses"/>-->
<!--            <metadata name="net.sf.eclipsecs.core.comment" value="Using Autowired outside configuration components"/>-->
<!--            <property name="severity" value="error"/>-->
<!--            <property name="format" value="@Autowired"/>-->
<!--            <property name="message" value="Consider injecting the reference from a @Configuration class instead."/>-->
<!--        </module>-->
        <module name="RegexpSinglelineJava">
            <property name="id" value="useLombokVarForGenerics"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Using val for variable definitions"/>
            <property name="severity" value="error"/>
            <property name="format" value="^(?!private|public)*\s+final\s+\w+&lt;.+&gt;\s+\w+(\s+)*(=.+)*;"/>
            <property name="message" value="Use lombok.val when declaring collections with generics"/>
        </module>
        <module name="RegexpSinglelineJava">
            <property name="id" value="useLombokValForFinalVar"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Using val for variable definitions"/>
            <property name="severity" value="error"/>
            <property name="format" value="(final\s+var\s+)|(^\s*final\s*\w+\[\]\s*\w+\s*=)"/>
            <property name="message" value="Use lombok.val when declaring final variable definitions."/>
        </module>

<!--        <module name="RegexpSinglelineJava">-->
<!--            <property name="id" value="useLombokVar"/>-->
<!--            <metadata name="net.sf.eclipsecs.core.comment" value="Using val for variable definitions"/>-->
<!--            <property name="severity" value="error"/>-->
<!--            <property name="format" value="^\s*(?!return|throw|private|public|protected|val)(final\s+)*\b(?!var)\w+\b\s*(\b[a-z]\w*\b)(\s*=\s+(?!null).+)*;"/>-->
<!--            <property name="message" value="Use lombok.val or JDK var keyword when declaring variable definitions."/>-->
<!--        </module>-->

        <module name="RegexpSinglelineJava">
            <property name="id" value="valForLoop"/>
            <metadata name="net.sf.eclipsecs.core.comment" value="Using val for variable definitions"/>
            <property name="severity" value="error"/>
            <property name="format" value="\s*for\s*\((final\s+)*(?!val|var)\w+\s+\w+\s*:"/>
            <property name="message" value="Use lombok.val when declaring variable definitions for loops."/>
        </module>

<!--        <module name="CustomImportOrder">-->
<!--            <property name="severity" value="error"/>-->
<!--            <property name="customImportOrderRules"-->
<!--                      value="SAME_PACKAGE(3)###THIRD_PARTY_PACKAGE###SPECIAL_IMPORTS###STANDARD_JAVA_PACKAGE###STATIC"/>-->
<!--            <property name="specialImportsRegExp" value="^javax\."/>-->
<!--            <property name="standardPackageRegExp" value="^java\."/>-->
<!--            <property name="sortImportsInGroupAlphabetically" value="true"/>-->
<!--            <property name="separateLineBetweenGroups" value="false"/>-->
<!--        </module>-->
    </module>

    <!-- Checker module checks -->
    <module name="UniqueProperties">
        <property name="severity" value="error"/>
    </module>

    <module name="RegexpMultiline">
        <property name="id" value="objectProvider"/>
        <property name="severity" value="error"/>
        <property name="format" value="@Autowired\s*@Qualifier\(&quot;(\w+)&quot;\)\s*(private|public|protected)\s+(\w+)\s+(\w+)\s*;"/>
        <property name="message" value="Use ObjectProvider&lt;T&gt; for dependency injections to avoid premature/circular bean initializations"/>
        <property name="fileExtensions" value="java" />
    </module>

    <module name="RegexpMultiline">
        <property name="id" value="needlessElseClause"/>
        <property name="severity" value="error"/>
        <property name="format" value="\s*\}\s*else\s*\{\s*return\s+.+;"/>
        <property name="message" value="Remove the unnecessary else clause: consider using the Elvis operator"/>
        <property name="fileExtensions" value="java" />
    </module>

    <module name="RegexpMultiline">
        <property name="id" value="useQualifier"/>
        <property name="severity" value="error"/>
        <property name="format"
                  value="@Autowired\s*(private|public|protected)\s+(?!.*Properties|.*Environment|FlowBuilderServices|.*Mapper|SpringTemplateEngine|.*EventPublisher|.*ApplicationContext|ResourceLoader)\w+\s+\w+\s*;"/>
        <property name="message" value="Use @Qualifier annotation when injecting dependencies"/>
        <property name="fileExtensions" value="java" />
    </module>

    <module name="NewlineAtEndOfFile">
        <property name="fileExtensions" value="java, xml, properties, txt"/>
        <property name="lineSeparator" value="LF_CR_CRLF"/>
        <property name="severity" value="error"/>
    </module>
    <module name="Translation">
        <property name="severity" value="ignore"/>
    </module>
<!--    <module name="FileLength"/>-->
    <module name="FileTabCharacter">
        <property name="severity" value="error"/>
    </module>


    <!-- Custom checks based on regular expressions -->
    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Trailing spaces"/>
        <property name="severity" value="error"/>
        <property name="format" value="\w+\s+$"/>
        <property name="message" value="Line has trailing spaces."/>
    </module>
<!--    <module name="RegexpSingleline">-->
<!--        <metadata name="net.sf.eclipsecs.core.comment" value="Forbidden annotations"/>-->
<!--        <property name="severity" value="error"/>-->
<!--        <property name="id" value="forbiddenAnnotations"/>-->
<!--        <property name="format" value="@Component|@Service"/>-->
<!--        <property name="message" value="Avoid using the indicated annotations such as @Component, @Service, etc."/>-->
<!--    </module>-->
<!--    <module name="RegexpSingleline">-->
<!--        <metadata name="net.sf.eclipsecs.core.comment" value="Use @TestConfiguration"/>-->
<!--        <property name="severity" value="error"/>-->
<!--        <property name="id" value="useTestConfiguration"/>-->
<!--        <property name="format" value="@Configuration"/>-->
<!--        <property name="message" value="Configuration classes for tests must be marked with @TestConfiguration"/>-->
<!--    </module>-->

    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Use Java streams"/>
        <property name="severity" value="error"/>
        <property name="format" value="new .+(Set|List).+\(Arrays\.asList"/>
        <property name="message" value="Avoid chaining collections. Shoot for immutability. Try Stream.of() and collect."/>
    </module>
    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Space instead of tabs"/>
        <property name="severity" value="error"/>
        <property name="format" value="^\t+"/>
        <property name="message" value="Tabs should never be used for indentation. Use spaces instead"/>
    </module>
    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Space after cast"/>
        <property name="severity" value="error"/>
        <property name="format" value="\(\w+\)\w+"/>
        <property name="message" value="There are no spaces after cast."/>
    </module>
    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Usage of java.util.Random"/>
        <property name="severity" value="error"/>
        <property name="format" value="(java.util.Random)|(new Random\()"/>
        <property name="message" value="For security purposes, use 'java.security.SecureRandom' instead"/>
    </module>
    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Arrays.asList() method call"/>
        <property name="severity" value="error"/>
        <property name="format" value="Lists\.newArrayList\(.+"/>
        <property name="message" value="Use Arrays.asList() or Collections.singletonList() instead."/>
    </module>
    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Test class is named incorrectly"/>
        <property name="severity" value="error"/>
        <property name="format" value="public\s+(abstract\s+)*class\s+.+Test\s*\{"/>
        <property name="message" value="Name of the test class MUST end in 'Tests'"/>
    </module>
    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Use Collections.singleton instead"/>
        <property name="severity" value="info"/>
        <property name="format" value="new.*Set&lt;&gt;\((Arrays\.asList\([^,]+\)|Collections\.singletonList\(.*\))\)"/>
        <property name="message" value="Consider using Collections.singleton instead"/>
    </module>
    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Use cn.microvideo.server.RandomUtils for random-number generation"/>
        <property name="severity" value="error"/>
        <property name="format" value="RandomStringUtils\.(randomAlphanumeric|randomNumeric|randomAlphabetic|randomAscii|nextLong|nextInt|nextDouble|nextFloat)"/>
        <property name="message" value="Use cn.microvideo.server.RandomUtils for random-number generation instead."/>
    </module>
    <module name="RegexpSingleline">
        <metadata name="net.sf.eclipsecs.core.comment" value="Use specific request annotation"/>
        <property name="severity" value="error"/>
        <property name="format" value="method = RequestMethod\.(GET|POST|DELETE|PUT)"/>
        <property name="fileExtensions" value="java"/>
        <property name="message" value="Use specific request annotation from org.springframework.web.bind.annotation package"/>
    </module>
    <module name="SuppressionFilter">
        <property name="file" value="${checkstyle.suppressions.file}"/>
    </module>
</module>
